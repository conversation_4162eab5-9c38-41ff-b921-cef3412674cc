from typing import Dict, Any, List, Tuple, Optional, Set
import pandas as pd
import numpy as np
import asyncio
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.db.models import Interaction, Product, ModelMetadata
from src.ml_pipeline.base_trainer import BaseTrainer
from src.ml_pipeline.collaborative_trainer import CollaborativeTrainer
from src.ml_pipeline.content_trainer import ContentTrainer
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.ml_pipeline.evaluation import RecommendationEvaluator
from src.utils.base_logger import log_info, log_error, log_warning
from src.utils.system_events import SystemEventLogger
from src.services.limit_service import LimitService


class DataFetcher:
    """
    Clase responsable de obtener datos de interacciones y productos para el entrenamiento.
    """

    async def fetch_training_data(
        self,
        db: AsyncSession,
        account_id: int,
        max_interactions: int = 100000,
        sample_size: Optional[int] = None,
        days_limit: Optional[int] = 90,
        interaction_types: Optional[List[str]] = None,
        chunk_size: int = 10000,
        incremental: bool = False,
        last_training_date: Optional[datetime] = None,
        validate_limits: bool = True,
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Obtiene los datos de entrenamiento de la base de datos.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            max_interactions: Límite máximo de interacciones a procesar
            sample_size: Tamaño de la muestra (None = sin muestreo)
            days_limit: Limitar a interacciones de los últimos N días
            interaction_types: Tipos de interacción a incluir
            chunk_size: Tamaño del chunk para procesamiento por lotes
            incremental: Si es True, solo procesa datos nuevos desde el último entrenamiento
            last_training_date: Fecha del último entrenamiento (para modo incremental)
            validate_limits: Si es True, valida los límites de datos de entrenamiento

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: DataFrames de interacciones y productos
        """
        log_info(f"Obteniendo datos de entrenamiento para account_id={account_id}")

        # Obtener el número total de interacciones primero para validar límites
        if validate_limits:
            # Contar interacciones para validar contra límites del plan
            count_query = select(Interaction).where(Interaction.account_id == account_id)
            count_result = await db.execute(count_query)
            total_interactions = len(count_result.scalars().all())

            # Validar contra los límites del plan
            limit_service = LimitService(db, account_id)
            await limit_service.validate_training_data_limit(total_interactions)

            log_info(f"Validación de límites: {total_interactions} interacciones disponibles")

        # Construir la query base para obtener interacciones
        query = select(Interaction).where(Interaction.account_id == account_id)

        # Filtrar por fecha si es necesario
        if days_limit and not incremental:
            # Calcular la fecha límite
            from datetime import datetime, timedelta
            date_limit = datetime.now(timezone.utc) - timedelta(days=days_limit)
            query = query.where(Interaction.timestamp >= date_limit)
            log_info(f"Filtrando interacciones de los últimos {days_limit} días")
        elif incremental and last_training_date:
            # En modo incremental, solo obtener datos nuevos desde el último entrenamiento
            query = query.where(Interaction.timestamp > last_training_date)
            log_info(f"Modo incremental: obteniendo datos nuevos desde {last_training_date}")

        # Filtrar por tipos de interacción si es necesario
        if interaction_types:
            query = query.where(Interaction.interaction_type.in_(interaction_types))
            log_info(f"Filtrando por tipos de interacción: {interaction_types}")

        # Limitar el número máximo de interacciones
        if max_interactions:
            query = query.order_by(Interaction.timestamp.desc()).limit(max_interactions)
            log_info(f"Limitando a {max_interactions} interacciones")

        # Obtener las interacciones en chunks para evitar problemas de memoria
        all_interactions = []
        offset = 0

        log_info(f"Obteniendo interacciones en chunks de {chunk_size}")
        while True:
            chunk_query = query.offset(offset).limit(chunk_size)
            chunk_result = await db.execute(chunk_query)
            chunk_interactions = chunk_result.scalars().all()

            if not chunk_interactions:
                break

            all_interactions.extend(chunk_interactions)
            offset += chunk_size
            log_info(f"Obtenido chunk {offset//chunk_size} con {len(chunk_interactions)} interacciones")

            if len(all_interactions) >= max_interactions:
                all_interactions = all_interactions[:max_interactions]
                break

        log_info(f"Total de {len(all_interactions)} interacciones obtenidas")

        # Crear DataFrame de interacciones
        if not all_interactions:
            log_warning(f"No se encontraron interacciones para account_id={account_id}")
            return pd.DataFrame(), pd.DataFrame()

        interactions_data = []
        product_ids = set()

        for interaction in all_interactions:
            interactions_data.append({
                "user_id": interaction.user_id,
                "item_id": interaction.item_id,
                "rating": interaction.rating or 1.0,  # Valor por defecto si no hay rating
                "timestamp": interaction.timestamp,
                "interaction_type": interaction.interaction_type,
            })
            product_ids.add(interaction.item_id)

        interactions_df = pd.DataFrame(interactions_data)

        # Realizar muestreo si es necesario
        if sample_size and len(interactions_df) > sample_size:
            interactions_df = interactions_df.sample(sample_size, random_state=42)
            log_info(f"Muestra aleatoria de {sample_size} interacciones tomada")
            # Actualizar el conjunto de product_ids después del muestreo
            product_ids = set(interactions_df["item_id"].unique())

        # Obtener información de productos
        products_query = select(Product).where(
            (Product.account_id == account_id) &
            (Product.id.in_(product_ids))
        )

        products_result = await db.execute(products_query)
        products = products_result.scalars().all()

        # Crear DataFrame de productos
        products_data = []
        for product in products:
            product_dict = {
                "item_id": product.product_id,
                "name": product.name,
                "category": product.category,
                "description": product.description or "",
                "price": product.price,
                "average_rating": product.average_rating or 0.0,
                "num_ratings": product.num_ratings or 0,
            }
            # Agregar atributos personalizados si existen
            if product.attributes:
                for key, value in product.attributes.items():
                    product_dict[f"attr_{key}"] = value

            products_data.append(product_dict)

        products_df = pd.DataFrame(products_data)

        log_info(f"Obtenidos {len(products_df)} productos relacionados con las interacciones")

        # Validar y filtrar interacciones para asegurar que solo incluyen productos existentes
        valid_item_ids = set(products_df["item_id"])
        interactions_df = interactions_df[interactions_df["item_id"].isin(valid_item_ids)]

        log_info(f"DataFrame de interacciones final: {len(interactions_df)} filas")
        log_info(f"DataFrame de productos final: {len(products_df)} filas")

        # Verificar si hay suficientes datos para el entrenamiento
        if validate_limits and len(interactions_df) < 100:
            log_warning(f"Muy pocas interacciones ({len(interactions_df)}) para entrenamiento confiable")

        if validate_limits and len(products_df) < 10:
            log_warning(f"Muy pocos productos ({len(products_df)}) para entrenamiento confiable")

        return interactions_df, products_df


class DataPreparer:
    """
    Clase responsable de preparar los datos para el entrenamiento (train/test split).
    """

    def prepare_data(
        self,
        interactions_df: pd.DataFrame,
        test_size: float = 0.2,
        random_state: int = 42,
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Divide los datos de interacciones en conjuntos de entrenamiento y prueba.

        Args:
            interactions_df: DataFrame con interacciones
            test_size: Proporción del conjunto de prueba
            random_state: Semilla para reproducibilidad

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: DataFrames de entrenamiento y prueba
        """
        log_info(f"Preparando datos para entrenamiento/evaluación (test_size={test_size})")

        if interactions_df.empty:
            log_warning("DataFrame de interacciones vacío, no se puede preparar datos")
            return pd.DataFrame(), pd.DataFrame()

        # Ordenar por timestamp para asegurar que el split es temporal
        if "timestamp" in interactions_df.columns:
            interactions_df = interactions_df.sort_values("timestamp")

        # División simple por índice
        train_size = 1.0 - test_size
        train_idx = int(len(interactions_df) * train_size)

        train_interactions = interactions_df.iloc[:train_idx].copy()
        test_interactions = interactions_df.iloc[train_idx:].copy()

        log_info(f"Datos divididos: {len(train_interactions)} para entrenamiento, {len(test_interactions)} para prueba")

        return train_interactions, test_interactions


class ArtifactPersistor:
    """
    Clase responsable de persistir los artefactos de modelo y las métricas.
    """

    def __init__(self, artifact_manager: ModelArtifactManager, metrics_tracker: MetricsTracker):
        self.artifact_manager = artifact_manager
        self.metrics_tracker = metrics_tracker

    def create_artifacts_dict(
        self,
        account_id: int,
        collab_artifacts: Dict[str, Any],
        content_artifacts: Dict[str, Any],
        recommendations: List[Dict[str, Any]],
        metrics: Dict[str, Any],
        interactions_df: pd.DataFrame,
        products_df: pd.DataFrame,
    ) -> Dict[str, Any]:
        """
        Crea un diccionario con todos los artefactos del modelo para persistencia.

        Args:
            account_id: ID de la cuenta
            collab_artifacts: Artefactos del modelo colaborativo
            content_artifacts: Artefactos del modelo basado en contenido
            recommendations: Recomendaciones generadas para evaluación
            metrics: Métricas de evaluación
            interactions_df: DataFrame de interacciones
            products_df: DataFrame de productos

        Returns:
            Dict[str, Any]: Diccionario con todos los artefactos
        """
        # Crear diccionario de artefactos
        artifacts = {
            "account_id": account_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "collaborative": collab_artifacts,
            "content": content_artifacts,
            "metrics": metrics,
            "test_recommendations": recommendations[:100] if recommendations else [],  # Limitar para no guardar demasiados
            "data_stats": {
                "interactions_count": len(interactions_df),
                "users_count": interactions_df["user_id"].nunique(),
                "items_count": interactions_df["item_id"].nunique(),
                "products_count": len(products_df),
                "avg_interactions_per_user": interactions_df.groupby("user_id").size().mean(),
                "avg_interactions_per_item": interactions_df.groupby("item_id").size().mean(),
            }
        }

        return artifacts

    async def persist_artifacts_and_metrics(
        self,
        db: AsyncSession,
        account_id: int,
        artifacts: Dict[str, Any],
        metrics: Dict[str, Any],
    ) -> Optional[int]:
        """
        Persiste los artefactos y métricas en la base de datos y almacenamiento.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            artifacts: Artefactos del modelo
            metrics: Métricas de evaluación

        Returns:
            Optional[int]: ID del modelo persistido, o None si falla
        """
        try:
            # Generar una versión única para el artefacto
            artifact_version = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")

            # Persistir los artefactos usando el ModelArtifactManager
            artifact_path = await self.artifact_manager.save_artifacts(
                account_id=account_id,
                artifacts=artifacts,
                version=artifact_version
            )

            # Crear entrada de metadata en la base de datos
            metadata = ModelMetadata(
                account_id=account_id,
                artifact_path=artifact_path,
                artifact_version=artifact_version,
                status="active",
                created_at=datetime.now(timezone.utc)
            )

            db.add(metadata)
            await db.commit()
            await db.refresh(metadata)

            # Registrar las métricas
            await self.metrics_tracker.register_metrics(
                db=db,
                model_id=metadata.id,
                account_id=account_id,
                metrics=metrics,
                timestamp=datetime.now(timezone.utc)
            )

            log_info(f"Artefactos y métricas persistidos para account_id={account_id}, model_id={metadata.id}")
            return metadata.id

        except Exception as e:
            log_error(f"Error al persistir artefactos y métricas: {str(e)}")
            await db.rollback()
            return None


class TrainingPipeline:
    """
    Pipeline completo para el entrenamiento de modelos de recomendación.
    """

    def __init__(
        self,
        artifact_manager: ModelArtifactManager,
        metrics_tracker: MetricsTracker,
        evaluator: RecommendationEvaluator,
        event_logger: Optional[SystemEventLogger] = None
    ):
        self.artifact_manager = artifact_manager
        self.metrics_tracker = metrics_tracker
        self.evaluator = evaluator
        self.event_logger = event_logger

        # Componentes internos
        self.data_fetcher = DataFetcher()
        self.data_preparer = DataPreparer()
        self.artifact_persistor = ArtifactPersistor(artifact_manager, metrics_tracker)

    async def train(
        self,
        db: AsyncSession,
        account_id: int,
        data: Optional[Dict[str, Any]] = None,
        max_interactions: int = 100000,
        sample_size: Optional[int] = 50000,
        days_limit: Optional[int] = 90,
        interaction_types: Optional[List[str]] = None,
        incremental: bool = False,
    ) -> Dict[str, Any]:
        """
        Entrena los modelos de recomendación para una cuenta.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            data: Datos pre-cargados (opcional)
            max_interactions: Límite máximo de interacciones
            sample_size: Tamaño de la muestra
            days_limit: Limitar a interacciones de los últimos N días
            interaction_types: Tipos de interacción a incluir
            incremental: Si es True, solo procesa datos nuevos desde el último entrenamiento

        Returns:
            Dict[str, Any]: Resultados del entrenamiento
        """
        try:
            log_info(f"Iniciando entrenamiento de modelos para account_id={account_id}")

            # Notificar inicio de entrenamiento
            if self.event_logger:
                await self.event_logger.log_event(
                    account_id=account_id,
                    event_type="training_started",
                    details={"incremental": incremental}
                )

            # 1. Preparar datos de entrenamiento
            interactions_df, test_interactions, train_interactions, products_df = await self._prepare_training_data(
                db=db,
                account_id=account_id,
                data=data,
                max_interactions=max_interactions,
                sample_size=sample_size,
                days_limit=days_limit,
                interaction_types=interaction_types,
                incremental=incremental
            )

            if interactions_df.empty or products_df.empty:
                log_warning(f"No hay suficientes datos para entrenar modelos para account_id={account_id}")
                await self._finalize_training(
                    account_id=account_id,
                    success=False,
                    error_message="Datos de entrenamiento insuficientes"
                )
                return {"success": False, "error": "Datos de entrenamiento insuficientes"}

            # 2. Entrenar modelos en paralelo
            collab_artifacts, content_artifacts = await self._train_models_parallel(
                account_id=account_id,
                train_interactions=train_interactions,
                products_df=products_df
            )

            # 3. Evaluar modelos
            recommendations, metrics = await self._evaluate_models(
                collab_artifacts=collab_artifacts,
                content_artifacts=content_artifacts,
                test_interactions=test_interactions,
                interactions_df=interactions_df,
                products_df=products_df
            )

            # 4. Crear diccionario de artefactos
            artifacts = self.artifact_persistor.create_artifacts_dict(
                account_id=account_id,
                collab_artifacts=collab_artifacts,
                content_artifacts=content_artifacts,
                recommendations=recommendations,
                metrics=metrics,
                interactions_df=interactions_df,
                products_df=products_df
            )

            # 5. Persistir artefactos y métricas
            model_id = await self.artifact_persistor.persist_artifacts_and_metrics(
                db=db,
                account_id=account_id,
                artifacts=artifacts,
                metrics=metrics
            )

            if not model_id:
                log_error(f"Error al persistir artefactos para account_id={account_id}")
                await self._finalize_training(
                    account_id=account_id,
                    success=False,
                    error_message="Error al persistir artefactos"
                )
                return {"success": False, "error": "Error al persistir artefactos"}

            # 6. Finalizar entrenamiento
            log_info(f"Modelos entrenados exitosamente para account_id={account_id}")
            await self._finalize_training(account_id=account_id, success=True)

            return {
                "success": True,
                "model_id": model_id,
                "metrics": metrics,
                "data_stats": artifacts["data_stats"]
            }

        except Exception as e:
            log_error(f"Error en el entrenamiento de modelos: {str(e)}")
            await self._finalize_training(
                account_id=account_id,
                success=False,
                error_message=str(e)
            )
            return {"success": False, "error": str(e)}

    async def _prepare_training_data(
        self,
        db: AsyncSession,
        account_id: int,
        data: Optional[Dict[str, Any]] = None,
        max_interactions: int = 100000,
        sample_size: Optional[int] = None,
        days_limit: Optional[int] = 90,
        interaction_types: Optional[List[str]] = None,
        incremental: bool = False,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Prepara los datos para el entrenamiento.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            data: Datos pre-cargados (opcional)
            max_interactions: Límite máximo de interacciones
            sample_size: Tamaño de la muestra
            days_limit: Limitar a interacciones de los últimos N días
            interaction_types: Tipos de interacción a incluir
            incremental: Si es True, solo procesa datos nuevos desde el último entrenamiento

        Returns:
            Tuple: DataFrames de interacciones, test, train y productos
        """
        # Obtener fecha del último entrenamiento si estamos en modo incremental
        last_training_date = None
        if incremental:
            # Obtener el modelo más reciente
            query = select(ModelMetadata).where(
                ModelMetadata.account_id == account_id
            ).order_by(ModelMetadata.created_at.desc()).limit(1)

            result = await db.execute(query)
            latest_model = result.scalar_one_or_none()

            if latest_model:
                last_training_date = latest_model.created_at
                log_info(f"Último entrenamiento: {last_training_date}")
            else:
                log_info("No se encontró un modelo previo, se realizará entrenamiento completo")
                incremental = False

        # Obtener datos de entrenamiento
        if data and "interactions" in data and "products" in data:
            # Usar datos pre-cargados si están disponibles
            interactions_df = pd.DataFrame(data["interactions"])
            products_df = pd.DataFrame(data["products"])
            log_info(f"Usando datos pre-cargados: {len(interactions_df)} interacciones, {len(products_df)} productos")
        else:
            # Obtener datos de la base de datos
            interactions_df, products_df = await self.data_fetcher.fetch_training_data(
                db=db,
                account_id=account_id,
                max_interactions=max_interactions,
                sample_size=sample_size,
                days_limit=days_limit,
                interaction_types=interaction_types,
                incremental=incremental,
                last_training_date=last_training_date
            )

        # Preparar división train/test
        train_interactions, test_interactions = self.data_preparer.prepare_data(
            interactions_df=interactions_df
        )

        return interactions_df, test_interactions, train_interactions, products_df

    async def _train_models_parallel(
        self,
        account_id: int,
        train_interactions: pd.DataFrame,
        products_df: pd.DataFrame,
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Entrena los modelos colaborativo y basado en contenido en paralelo.

        Args:
            account_id: ID de la cuenta
            train_interactions: DataFrame de interacciones de entrenamiento
            products_df: DataFrame de productos

        Returns:
            Tuple: Artefactos de los modelos colaborativo y basado en contenido
        """
        log_info(f"Iniciando entrenamiento de modelos en paralelo para account_id={account_id}")

        # Crear instancias de los trainers
        collab_trainer = CollaborativeTrainer()
        content_trainer = ContentTrainer()

        # Configurar y entrenar modelos en paralelo
        collab_task = asyncio.create_task(
            collab_trainer.train(train_interactions)
        )

        content_task = asyncio.create_task(
            content_trainer.train(train_interactions, products_df)
        )

        # Esperar a que ambos entrenamientos finalicen
        collab_artifacts, content_artifacts = await asyncio.gather(collab_task, content_task)

        log_info(f"Entrenamiento en paralelo completado para account_id={account_id}")

        return collab_artifacts, content_artifacts

    async def _evaluate_models(
        self,
        collab_artifacts: Dict[str, Any],
        content_artifacts: Dict[str, Any],
        test_interactions: pd.DataFrame,
        interactions_df: pd.DataFrame,
        products_df: pd.DataFrame,
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Evalúa los modelos entrenados con el conjunto de prueba y entrena el modelo LTR
        con la relevancia real basada en interacciones del usuario.

        Args:
            collab_artifacts: Artefactos del modelo colaborativo
            content_artifacts: Artefactos del modelo basado en contenido
            test_interactions: DataFrame de interacciones de prueba
            interactions_df: DataFrame completo de interacciones
            products_df: DataFrame de productos

        Returns:
            Tuple: Recomendaciones generadas y métricas de evaluación
        """
        log_info("Evaluando modelos entrenados")

        # Generar recomendaciones para el conjunto de prueba
        recommendations = await self._generate_recommendations(
            collab_artifacts=collab_artifacts,
            content_artifacts=content_artifacts,
            test_interactions=test_interactions
        )

        # Extraer user_history para la evaluación
        user_history = self._extract_user_history(interactions_df)

        # Extraer item_categories para la evaluación
        item_categories = self._extract_item_categories(products_df)

        # Entrenar modelo LTR con relevancia real (basada en interacciones del test set)
        try:
            from src.ml_pipeline.learning_to_rank import LearningToRankModel

            # Crear instancia del modelo LTR con el account_id correcto
            account_id = collab_artifacts.get("account_id", 1)
            ltr_model = LearningToRankModel(account_id=account_id, model_type="gbdt")

            # Extraer target_scores basados en interacciones reales (is_hit)
            target_scores = [rec.get("target_relevance", 0.0) for rec in recommendations]

            # Entrenar modelo LTR con relevancia real
            ltr_metrics = ltr_model.train(
                training_data=recommendations,
                target_scores=target_scores  # Usar relevancia real como objetivo
            )

            log_info(f"Modelo LTR entrenado con relevancia real: {ltr_metrics}")

            # Aplicar modelo LTR a las recomendaciones
            ranked_recommendations = ltr_model.predict(recommendations)

            # Usar las recomendaciones rankeadas para evaluación
            recommendations = ranked_recommendations

        except Exception as e:
            log_error(f"Error entrenando modelo LTR: {str(e)}")

        # Evaluar modelos
        metrics = self.evaluator.evaluate(
            recommendations=recommendations,
            test_interactions=test_interactions,
            user_history=user_history,
            item_categories=item_categories,
            verbose=True
        )

        log_info(f"Evaluación completada: {len(metrics)} métricas calculadas")

        return recommendations, metrics

    async def _generate_recommendations(
        self,
        collab_artifacts: Dict[str, Any],
        content_artifacts: Dict[str, Any],
        test_interactions: pd.DataFrame,
    ) -> List[Dict[str, Any]]:
        """
        Genera recomendaciones para los usuarios en el conjunto de prueba.

        Args:
            collab_artifacts: Artefactos del modelo colaborativo
            content_artifacts: Artefactos del modelo basado en contenido
            test_interactions: DataFrame de interacciones de prueba

        Returns:
            List[Dict[str, Any]]: Lista de recomendaciones generadas con información
            sobre si son hits (el usuario interactuó con el ítem en el conjunto de prueba)
            y scores de ambos modelos para entrenamiento del LTR.
        """
        # Obtener usuarios únicos del conjunto de prueba
        test_users = test_interactions["user_id"].unique()

        # Limitar a 100 usuarios para la evaluación
        if len(test_users) > 100:
            import random
            random.seed(42)
            test_users = random.sample(list(test_users), 100)

        log_info(f"Generando recomendaciones para {len(test_users)} usuarios de prueba")

        all_recommendations = []

        # Para cada usuario de prueba, generar recomendaciones
        for user_id in test_users:
            # Obtener ítems que el usuario ha interactuado en el conjunto de prueba
            user_test_items = test_interactions[test_interactions["user_id"] == user_id]["item_id"].tolist()
            
            # Obtener timestamps de las interacciones de prueba (importante para evitar feature leakage)
            user_test_timestamps = {}
            if "timestamp" in test_interactions.columns:
                user_test_df = test_interactions[test_interactions["user_id"] == user_id]
                for _, row in user_test_df.iterrows():
                    user_test_timestamps[row["item_id"]] = row["timestamp"]
            
            # Timestamp mínimo para cálculos de features (importante para evitar feature leakage)
            # Usamos el timestamp mínimo de las interacciones de test como punto de corte
            cutoff_timestamp = None
            if user_test_timestamps:
                cutoff_timestamp = min(user_test_timestamps.values())
            
            # Obtener historial de interacciones del usuario (del conjunto de entrenamiento)
            user_history = self._get_user_history(user_id)

            # Generar Top-10 recomendaciones para el usuario
            user_collab_recs = self._get_user_collab_recs(collab_artifacts, user_id, 10)
            user_content_recs = self._get_user_content_recs(content_artifacts, user_id, 10)

            # Combinar recomendaciones (versión simplificada para evaluación)
            combined_recs = []

            # Usar un enfoque simple de intercalado
            i, j = 0, 0
            while len(combined_recs) < 10 and (i < len(user_collab_recs) or j < len(user_content_recs)):
                if i < len(user_collab_recs):
                    rec = user_collab_recs[i]
                    # Verificar si es un hit (el usuario interactuó con este ítem en el test set)
                    is_hit = rec["item_id"] in user_test_items
                    
                    # Timestamp específico para este ítem (si existe)
                    item_timestamp = user_test_timestamps.get(rec["item_id"], cutoff_timestamp)
                    
                    # Calcular características adicionales
                    item_data = {
                        "user_id": user_id,
                        "item_id": rec["item_id"],
                        "collab_score": rec["score"],  # Guardar score original del modelo colaborativo
                        "content_score": 0.0,  # Valor por defecto para el score de contenido
                        "score": rec["score"],
                        "rank": len(combined_recs) + 1,
                        "model_type": "collaborative",
                        "is_hit": is_hit,
                        "target_relevance": 1.0 if is_hit else 0.0,  # Relevancia real basada en interacciones
                    }
                    
                    # Añadir información temporal para evitar feature leakage
                    if cutoff_timestamp:
                        item_data["request_timestamp"] = cutoff_timestamp
                        
                        # Añadir información de interacciones históricas
                        if user_history and rec["item_id"] in user_history:
                            interaction_data = user_history[rec["item_id"]]
                            
                            # Solo usar información anterior al timestamp de corte
                            if isinstance(interaction_data, dict) and "timestamp" in interaction_data:
                                if interaction_data["timestamp"] < cutoff_timestamp:
                                    item_data["last_interaction_timestamp"] = interaction_data["timestamp"]
                    
                    combined_recs.append(item_data)
                    i += 1

                if j < len(user_content_recs) and len(combined_recs) < 10:
                    rec = user_content_recs[j]
                    # Verificar si es un hit (el usuario interactuó con este ítem en el test set)
                    is_hit = rec["item_id"] in user_test_items
                    
                    # Timestamp específico para este ítem (si existe)
                    item_timestamp = user_test_timestamps.get(rec["item_id"], cutoff_timestamp)
                    
                    # Calcular características adicionales
                    item_data = {
                        "user_id": user_id,
                        "item_id": rec["item_id"],
                        "collab_score": 0.0,  # Valor por defecto para el score colaborativo
                        "content_score": rec["score"],  # Guardar score original del modelo de contenido
                        "score": rec["score"],
                        "rank": len(combined_recs) + 1,
                        "model_type": "content",
                        "is_hit": is_hit,
                        "target_relevance": 1.0 if is_hit else 0.0,  # Relevancia real basada en interacciones
                    }
                    
                    # Añadir información temporal para evitar feature leakage
                    if cutoff_timestamp:
                        item_data["request_timestamp"] = cutoff_timestamp
                        
                        # Añadir información de interacciones históricas
                        if user_history and rec["item_id"] in user_history:
                            interaction_data = user_history[rec["item_id"]]
                            
                            # Solo usar información anterior al timestamp de corte
                            if isinstance(interaction_data, dict) and "timestamp" in interaction_data:
                                if interaction_data["timestamp"] < cutoff_timestamp:
                                    item_data["last_interaction_timestamp"] = interaction_data["timestamp"]
                    
                    combined_recs.append(item_data)
                    j += 1

            all_recommendations.extend(combined_recs)

        log_info(f"Generadas {len(all_recommendations)} recomendaciones para evaluación")

        return all_recommendations

    def _get_user_collab_recs(
        self,
        collab_artifacts: Dict[str, Any],
        user_id: int,
        n: int
    ) -> List[Dict[str, Any]]:
        """Obtiene recomendaciones colaborativas para un usuario."""
        # Implementación simplificada para la evaluación
        model = collab_artifacts.get("model")
        if not model or user_id not in model.user_factors:
            return []

        scores = {}
        user_factors = model.user_factors[user_id]

        for item_id, item_factors in model.item_factors.items():
            scores[item_id] = np.dot(user_factors, item_factors)

        # Ordenar y obtener top-n
        top_items = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:n]

        return [{"item_id": item_id, "score": float(score)} for item_id, score in top_items]

    def _get_user_content_recs(
        self,
        content_artifacts: Dict[str, Any],
        user_id: int,
        n: int
    ) -> List[Dict[str, Any]]:
        """Obtiene recomendaciones basadas en contenido para un usuario."""
        # Implementación simplificada para la evaluación
        model = content_artifacts.get("model")
        if not model or user_id not in model.user_profiles:
            return []

        scores = {}
        user_profile = model.user_profiles[user_id]

        for item_id, item_vector in model.item_vectors.items():
            scores[item_id] = np.dot(user_profile, item_vector)

        # Ordenar y obtener top-n
        top_items = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:n]

        return [{"item_id": item_id, "score": float(score)} for item_id, score in top_items]

    def _extract_user_history(self, interactions_df: pd.DataFrame) -> Dict[int, Set[int]]:
        """
        Extrae el historial de interacciones de cada usuario.

        Args:
            interactions_df: DataFrame de interacciones

        Returns:
            Dict[int, Set[int]]: Diccionario con los ítems que ha interactuado cada usuario
        """
        user_history = {}

        for user_id, group in interactions_df.groupby("user_id"):
            user_history[user_id] = set(group["item_id"].unique())

        return user_history

    def _extract_item_categories(self, products_df: pd.DataFrame) -> Dict[int, str]:
        """
        Extrae las categorías de cada ítem.

        Args:
            products_df: DataFrame de productos

        Returns:
            Dict[int, str]: Diccionario con la categoría de cada ítem
        """
        item_categories = {}

        for _, row in products_df.iterrows():
            item_categories[row["item_id"]] = row.get("category", "")

        return item_categories

    def _get_user_history(self, user_id: int) -> Dict[int, Dict[str, Any]]:
        """
        Obtiene el historial de interacciones de un usuario para entrenamiento.

        Args:
            user_id: ID del usuario

        Returns:
            Diccionario con historial de interacciones del usuario {item_id: datos_interacción}
        """
        # Verificar si tenemos acceso a los datos de entrenamiento
        if not hasattr(self, "train_interactions") or self.train_interactions is None:
            return {}

        # Filtrar interacciones del usuario
        user_interactions = self.train_interactions[self.train_interactions["user_id"] == user_id]
        
        if user_interactions.empty:
            return {}
            
        # Construir diccionario de historial
        history = {}
        
        # Columnas que esperamos tener disponibles
        expected_columns = ["item_id", "interaction_value", "timestamp"]
        
        for _, row in user_interactions.iterrows():
            item_id = row["item_id"]
            
            # Crear entrada en el historial
            history[item_id] = {}
            
            # Añadir score/valor de interacción
            if "interaction_value" in row:
                history[item_id]["score"] = float(row["interaction_value"])
            
            # Añadir timestamp si está disponible
            if "timestamp" in row:
                history[item_id]["timestamp"] = int(row["timestamp"])
                
            # Contar interacciones por ítem
            if item_id in history:
                history[item_id]["interaction_count"] = history[item_id].get("interaction_count", 0) + 1
            else:
                history[item_id]["interaction_count"] = 1
                
        return history

    async def _finalize_training(
        self, account_id: int, success: bool, error_message: Optional[str] = None
    ) -> None:
        """
        Finaliza el proceso de entrenamiento, registrando el resultado.

        Args:
            account_id: ID de la cuenta
            success: Si el entrenamiento fue exitoso
            error_message: Mensaje de error (si aplica)
        """
        # Registrar finalización en el log de eventos del sistema
        if self.event_logger:
            event_type = "training_completed" if success else "training_failed"
            details = {"success": success}

            if error_message:
                details["error"] = error_message

            await self.event_logger.log_event(
                account_id=account_id,
                event_type=event_type,
                details=details
            )

        # Registrar en logs
        if success:
            log_info(f"Entrenamiento finalizado con éxito para account_id={account_id}")
        else:
            log_error(f"Entrenamiento fallido para account_id={account_id}: {error_message}")

    async def train_ltr(
        self,
        account_id: int,
        collab_artifacts: Dict[str, Any],
        content_artifacts: Dict[str, Any],
        test_interactions: pd.DataFrame,
    ) -> Dict[str, Any]:
        """
        Entrena y evalúa un modelo LTR basado en las recomendaciones generadas.

        Args:
            account_id: ID de la cuenta
            collab_artifacts: Artefactos del modelo colaborativo
            content_artifacts: Artefactos del modelo basado en contenido
            test_interactions: DataFrame con interacciones de prueba

        Returns:
            Dict[str, Any]: Artefactos del modelo LTR
        """
        # Obtener modelo LTR
        from src.ml_pipeline.learning_to_rank import LearningToRankModel
        ltr_model = LearningToRankModel(account_id=account_id)

        # Generar recomendaciones para entrenar el modelo LTR
        training_data = await self._generate_recommendations(
            collab_artifacts, content_artifacts, test_interactions
        )

        if not training_data:
            log_error("No se pudieron generar recomendaciones para entrenar el modelo LTR")
            return {}

        # Extraer interacciones objetivo y recomendaciones
        target_scores = [rec["target_relevance"] for rec in training_data]
        
        # Determinar un timestamp máximo para entrenamiento (si disponible)
        max_timestamp = None
        if "timestamp" in test_interactions.columns:
            max_timestamp = test_interactions["timestamp"].max()

        # Entrenar modelo LTR
        try:
            metrics = ltr_model.train(
                training_data=training_data,
                target_scores=target_scores,
                timestamp=max_timestamp
            )

            # Guardar modelo
            ltr_model.save_model()

            # Evaluar modelo LTR
            # Para una evaluación justa, usamos el modelo para reordenar las recomendaciones
            # y comparamos con las interacciones reales del conjunto de prueba.
            ranked_recs = ltr_model.predict(
                recommendations=training_data,
                user_id=training_data[0].get("user_id") if training_data else None,
                timestamp=max_timestamp
            )

            # Evaluar las recomendaciones ordenadas
            from src.ml_pipeline.evaluation import RecommendationEvaluator
            evaluator = RecommendationEvaluator(k=10)  # Evaluar Top-10

            ndcg = evaluator.calculate_ndcg(
                predicted_recommendations=ranked_recs,
                true_interactions=set(
                    (row["user_id"], row["item_id"])
                    for _, row in test_interactions.iterrows()
                ),
            )

            log_info(f"Modelo LTR entrenado con NDCG@10: {ndcg:.4f}")

            # Crear artefactos
            artifacts = {
                "model": ltr_model,
                "metrics": {
                    "train_metrics": metrics,
                    "ndcg": ndcg,
                },
                "parameters": ltr_model.model_params,
            }

            return artifacts
        except Exception as e:
            log_error(f"Error entrenando modelo LTR: {str(e)}")
            return {}