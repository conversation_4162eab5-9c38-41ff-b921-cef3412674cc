#!/usr/bin/env python3
"""
Script de prueba para verificar la implementación de los modelos Order y OrderItem
y los endpoints de catalog insights.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# Agregar el directorio src al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from src.db.models import Order, OrderItem, Product, EndUser, Account
from src.db.enums import OrderStatus
from src.ml_pipeline.catalog_insights_service import CatalogInsightsService


async def test_order_models():
    """Prueba básica de los modelos Order y OrderItem"""
    print("🧪 Probando modelos Order y OrderItem...")

    # Crear instancias de prueba
    order = Order(
        id=1,
        account_id=1,
        order_number="ORD-001",
        end_user_id=1,
        status=OrderStatus.PENDING,
        total_amount=Decimal("99.99"),
        currency="USD",
        order_date=datetime.utcnow(),
    )

    order_item = OrderItem(
        id=1,
        account_id=1,
        order_id=1,
        product_id=1,
        quantity=2,
        unit_price=Decimal("49.99"),
        total_price=Decimal("99.98"),
    )

    print(f"✅ Order creado: {order}")
    print(f"✅ OrderItem creado: {order_item}")

    # Verificar enums
    print(f"✅ OrderStatus disponibles: {list(OrderStatus)}")

    return True


async def test_catalog_insights_service():
    """Prueba básica del servicio CatalogInsightsService"""
    print("\n🧪 Probando CatalogInsightsService...")

    service = CatalogInsightsService()

    # Verificar que los métodos existen y tienen la firma correcta
    methods_to_test = [
        "get_most_sold",
        "get_also_bought",
        "get_most_searched",
        "get_trending_searches",
        "get_related_searches",
        "get_category_products",
        "get_related_categories",
        "get_similar_products",
    ]

    for method_name in methods_to_test:
        if hasattr(service, method_name):
            method = getattr(service, method_name)
            print(f"✅ Método {method_name} existe")

            # Verificar que es async
            if asyncio.iscoroutinefunction(method):
                print(f"✅ Método {method_name} es async")
            else:
                print(f"❌ Método {method_name} no es async")
        else:
            print(f"❌ Método {method_name} no existe")

    return True


def test_imports():
    """Prueba que todas las importaciones funcionan correctamente"""
    print("\n🧪 Probando importaciones...")

    try:
        from src.db.models import Order, OrderItem

        print("✅ Importación de Order y OrderItem exitosa")

        from src.db.enums import OrderStatus

        print("✅ Importación de OrderStatus exitosa")

        from src.ml_pipeline.catalog_insights_service import CatalogInsightsService

        print("✅ Importación de CatalogInsightsService exitosa")

        return True

    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        return False


def test_model_relationships():
    """Prueba las relaciones entre modelos"""
    print("\n🧪 Probando relaciones entre modelos...")

    try:
        # Verificar que Order tiene la relación con OrderItem
        order = Order()
        if hasattr(order, "order_items"):
            print("✅ Order tiene relación order_items")
        else:
            print("❌ Order no tiene relación order_items")

        # Verificar que OrderItem tiene relación con Order
        order_item = OrderItem()
        if hasattr(order_item, "order"):
            print("✅ OrderItem tiene relación order")
        else:
            print("❌ OrderItem no tiene relación order")

        # Verificar que OrderItem tiene relación con Product
        if hasattr(order_item, "product"):
            print("✅ OrderItem tiene relación product")
        else:
            print("❌ OrderItem no tiene relación product")

        return True

    except Exception as e:
        print(f"❌ Error probando relaciones: {e}")
        return False


async def main():
    """Función principal de pruebas"""
    print("🚀 Iniciando pruebas de implementación de Order y OrderItem\n")

    tests = [
        ("Importaciones", test_imports),
        ("Modelos Order/OrderItem", test_order_models),
        ("Relaciones entre modelos", test_model_relationships),
        ("CatalogInsightsService", test_catalog_insights_service),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Ejecutando: {test_name}")
        print("=" * 50)

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            results.append((test_name, result))

        except Exception as e:
            print(f"❌ Error en {test_name}: {e}")
            results.append((test_name, False))

    # Resumen de resultados
    print(f"\n{'='*50}")
    print("RESUMEN DE PRUEBAS")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASÓ" if result else "❌ FALLÓ"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\nResultado: {passed}/{total} pruebas pasaron")

    if passed == total:
        print("🎉 ¡Todas las pruebas pasaron! La implementación está lista.")
    else:
        print("⚠️  Algunas pruebas fallaron. Revisar la implementación.")

    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
