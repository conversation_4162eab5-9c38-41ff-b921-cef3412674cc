#!/usr/bin/env python3
"""
Script específico para corregir errores de sintaxis e indentación en tests.
"""

import os
import ast
import shutil
from pathlib import Path
from typing import List


def backup_file(file_path: str) -> str:
    """Crea un backup de un archivo antes de modificarlo."""
    backup_path = f"{file_path}.syntax_backup"
    shutil.copy2(file_path, backup_path)
    return backup_path


def has_syntax_error(file_path: str) -> bool:
    """Verifica si un archivo tiene errores de sintaxis."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        ast.parse(content, filename=file_path)
        return False
    except SyntaxError:
        return True
    except Exception:
        return True


def fix_indentation_errors(file_path: str) -> bool:
    """Intenta corregir errores de indentación básicos."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        original_lines = lines.copy()
        fixed_lines = []

        for i, line in enumerate(lines):
            stripped = line.strip()

            # Si la línea está vacía, mantenerla
            if not stripped:
                fixed_lines.append(line)
                continue

            # Detectar nivel de indentación apropiado basado en el contexto
            if i > 0:
                prev_line = lines[i - 1].strip()

                # Si la línea anterior termina con :, aumentar indentación
                if prev_line.endswith(":"):
                    if not line.startswith("    "):
                        # Agregar indentación si no la tiene
                        fixed_lines.append("    " + stripped + "\n")
                    else:
                        fixed_lines.append(line)
                    continue

                # Si es una línea de nivel superior (def, class, import, etc.)
                if stripped.startswith(
                    ("def ", "class ", "import ", "from ", "@")
                ) or stripped.startswith(("async def ", "if __name__")):
                    fixed_lines.append(stripped + "\n")
                    continue

            # Para otras líneas, mantener la indentación original si parece correcta
            fixed_lines.append(line)

        # Solo escribir si hay cambios
        if fixed_lines != original_lines:
            with open(file_path, "w", encoding="utf-8") as f:
                f.writelines(fixed_lines)
            return True

        return False

    except Exception as e:
        print(f"Error fixing indentation in {file_path}: {e}")
        return False


def restore_from_backup(file_path: str):
    """Restaura un archivo desde su backup si existe."""
    backup_paths = [f"{file_path}.backup", f"{file_path}.syntax_backup"]

    for backup_path in backup_paths:
        if os.path.exists(backup_path):
            print(f"Restaurando {file_path} desde {backup_path}")
            shutil.copy2(backup_path, file_path)
            return True

    return False


def main():
    """Función principal para corregir errores de sintaxis."""
    print("🔧 Corrigiendo errores de sintaxis en tests...")

    # Archivos con errores de sintaxis identificados
    problem_files = [
        "tests/e2e/test_api_flows.py",
        "tests/integration/test_cache.py",
        "tests/integration/test_data_ingestion.py",
        "tests/integration/test_data_integrity.py",
        "tests/integration/test_db_interaction.py",
        "tests/integration/test_error_handling.py",
        "tests/integration/test_hybrid_recommender_integration.py",
        "tests/integration/test_ml_pipeline.py",
        "tests/integration/test_multi_tenancy.py",
        "tests/integration/test_multi_tenancy_security.py",
        "tests/middleware/test_usage_meter_middleware.py",
    ]

    fixed_count = 0
    failed_count = 0

    for file_path in problem_files:
        if not os.path.exists(file_path):
            print(f"❌ Archivo no encontrado: {file_path}")
            continue

        print(f"🔧 Procesando: {file_path}")

        # Crear backup
        backup_path = backup_file(file_path)

        try:
            # Intentar corregir indentación
            if fix_indentation_errors(file_path):
                print(f"  📝 Indentación corregida")

            # Verificar si se corrigió el error
            if not has_syntax_error(file_path):
                print(f"  ✅ Error de sintaxis corregido")
                fixed_count += 1
            else:
                print(f"  ❌ No se pudo corregir automáticamente")
                # Restaurar desde backup original si existe
                restore_from_backup(file_path)
                failed_count += 1

        except Exception as e:
            print(f"  ❌ Error procesando {file_path}: {e}")
            # Restaurar desde backup
            shutil.copy2(backup_path, file_path)
            failed_count += 1

    print(f"\n📊 RESUMEN:")
    print(f"✅ Archivos corregidos: {fixed_count}")
    print(f"❌ Archivos que necesitan corrección manual: {failed_count}")

    if failed_count > 0:
        print(f"\n💡 Para archivos que no se pudieron corregir automáticamente:")
        print(f"   1. Revisa manualmente la indentación")
        print(f"   2. Usa un editor con syntax highlighting")
        print(f"   3. Verifica que las líneas después de : estén indentadas")


if __name__ == "__main__":
    main()
