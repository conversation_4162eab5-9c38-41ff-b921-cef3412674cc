#!/usr/bin/env python
"""
Script para ejecutar tests unitarios específicos sin depender de la estructura completa.
Este script permite ejecutar tests unitarios específicos sin cargar toda la aplicación.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.utils.base_logger import logger


def run_unit_tests(test_path=None, verbose=False, pattern=None):
    """
    Ejecutar tests unitarios específicos.
    
    Args:
        test_path: Ruta al directorio o archivo de test
        verbose: Mostrar salida detallada
        pattern: Patrón para filtrar tests
    
    Returns:
        Código de salida (0 si todos los tests pasan, 1 si hay errores)
    """
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")
    
    # Construir el comando
    cmd = ["pytest"]
    
    if test_path:
        cmd.append(test_path)
    else:
        cmd.append("tests/unit")
    
    if verbose:
        cmd.append("-v")
    
    if pattern:
        cmd.append(f"-k {pattern}")
    
    # Ejecutar el comando
    logger.info(f"Ejecutando tests unitarios: {' '.join(cmd)}")
    result = subprocess.run(cmd, env=env)
    
    return result.returncode


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Ejecutar tests unitarios")
    parser.add_argument("--path", type=str, default=None, help="Ruta al directorio o archivo de test")
    parser.add_argument("--verbose", "-v", action="store_true", help="Mostrar salida detallada")
    parser.add_argument("--pattern", "-k", type=str, default=None, help="Patrón para filtrar tests")
    
    args = parser.parse_args()
    
    return run_unit_tests(args.path, args.verbose, args.pattern)


if __name__ == "__main__":
    sys.exit(main())
