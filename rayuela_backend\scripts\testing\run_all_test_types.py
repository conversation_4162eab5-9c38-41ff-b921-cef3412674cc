#!/usr/bin/env python
"""
Script para ejecutar todos los tipos de tests de Rayuela en secuencia.
Este script ejecuta tests unitarios, de integración, end-to-end y de carga.
"""

import os
import sys
import time
import subprocess
import click
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.core.config import settings
from src.utils.base_logger import logger


def run_unit_tests(verbose=False, coverage=False):
    """Ejecutar tests unitarios"""
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["python", "-m", "scripts.run_all_tests", "unit"]

    if verbose:
        cmd.append("-v")

    if coverage:
        cmd.append("--coverage")

    logger.info("Ejecutando tests unitarios...")
    result = subprocess.run(cmd, env=env)
    return result.returncode == 0


def run_integration_tests(verbose=False, coverage=False, setup_db=True):
    """Ejecutar tests de integración"""
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["python", "-m", "scripts.run_all_tests", "integration"]

    if verbose:
        cmd.append("-v")

    if coverage:
        cmd.append("--coverage")

    if setup_db:
        cmd.append("--setup-db")

    logger.info("Ejecutando tests de integración...")
    result = subprocess.run(cmd, env=env)
    return result.returncode == 0


def run_e2e_tests(verbose=False):
    """Ejecutar tests end-to-end"""
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["python", "-m", "scripts.run_all_tests", "e2e"]

    if verbose:
        cmd.append("-v")

    logger.info("Ejecutando tests end-to-end...")
    result = subprocess.run(cmd, env=env)
    return result.returncode == 0


def run_load_tests(headless=True, users=10, time=30):
    """Ejecutar tests de carga"""
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = [
        "python", "-m", "scripts.run_load_tests",
        "--users", str(users),
        "--time", str(time)
    ]

    if headless:
        cmd.append("--headless")

    logger.info("Ejecutando tests de carga...")
    result = subprocess.run(cmd, env=env)
    return result.returncode == 0


def run_multi_tenancy_tests(verbose=False):
    """Ejecutar tests de multi-tenancy"""
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["python", "-m", "scripts.test_multi_tenancy"]

    if verbose:
        cmd.append("-v")

    logger.info("Ejecutando tests de multi-tenancy...")
    result = subprocess.run(cmd, env=env)
    return result.returncode == 0


@click.command()
@click.option("--verbose", "-v", is_flag=True, help="Mostrar salida detallada")
@click.option("--coverage", is_flag=True, help="Ejecutar con cobertura de código")
@click.option("--skip-unit", is_flag=True, help="Omitir tests unitarios")
@click.option("--skip-integration", is_flag=True, help="Omitir tests de integración")
@click.option("--skip-e2e", is_flag=True, help="Omitir tests end-to-end")
@click.option("--skip-load", is_flag=True, help="Omitir tests de carga")
@click.option("--skip-multi-tenancy", is_flag=True, help="Omitir tests de multi-tenancy")
@click.option("--load-users", default=10, help="Número de usuarios para tests de carga")
@click.option("--load-time", default=30, help="Tiempo de ejecución para tests de carga (segundos)")
def main(verbose, coverage, skip_unit, skip_integration, skip_e2e, skip_load,
         skip_multi_tenancy, load_users, load_time):
    """Ejecutar todos los tipos de tests de Rayuela"""
    success = True

    # Ejecutar tests unitarios
    if not skip_unit:
        if not run_unit_tests(verbose, coverage):
            success = False
            logger.error("Tests unitarios fallaron.")

    # Ejecutar tests de integración
    if not skip_integration:
        if not run_integration_tests(verbose, coverage):
            success = False
            logger.error("Tests de integración fallaron.")

    # Ejecutar tests end-to-end
    if not skip_e2e:
        if not run_e2e_tests(verbose):
            success = False
            logger.error("Tests end-to-end fallaron.")

    # Ejecutar tests de multi-tenancy
    if not skip_multi_tenancy:
        if not run_multi_tenancy_tests(verbose):
            success = False
            logger.error("Tests de multi-tenancy fallaron.")

    # Ejecutar tests de carga
    if not skip_load:
        if not run_load_tests(True, load_users, load_time):
            success = False
            logger.error("Tests de carga fallaron.")

    if success:
        logger.info("Todos los tests pasaron correctamente.")
        return 0
    else:
        logger.error("Algunos tests fallaron.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
