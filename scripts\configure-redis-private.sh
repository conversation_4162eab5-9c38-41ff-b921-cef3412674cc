#!/bin/bash

# Configure Cloud Memorystore (Redis) for Private Network Only
# Ensures Redis instances are only accessible from private VPC network

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="${GCP_PROJECT_ID:-$(gcloud config get-value project)}"
REGION="${GCP_REGION:-us-central1}"
VPC_NETWORK="default"

echo -e "${BLUE}🔒 Configurando Cloud Memorystore (Redis) para Red Privada${NC}"
echo "=========================================================="
echo "📋 Proyecto: $PROJECT_ID"
echo "🌍 Región: $REGION"
echo "🌐 Red VPC: $VPC_NETWORK"
echo ""

# Function to enable required APIs
enable_apis() {
    echo -e "${BLUE}🔌 Habilitando APIs requeridas...${NC}"
    
    REQUIRED_APIS=(
        "redis.googleapis.com"
        "servicenetworking.googleapis.com"
        "compute.googleapis.com"
    )
    
    for api in "${REQUIRED_APIS[@]}"; do
        echo "📡 Habilitando $api..."
        gcloud services enable $api --project=$PROJECT_ID
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ $api habilitada${NC}"
        else
            echo -e "${RED}❌ Error habilitando $api${NC}"
            exit 1
        fi
    done
    
    echo ""
}

# Function to check VPC network
check_vpc_network() {
    echo -e "${BLUE}🌐 Verificando red VPC...${NC}"
    
    if gcloud compute networks describe $VPC_NETWORK --project=$PROJECT_ID &>/dev/null; then
        echo -e "${GREEN}✅ Red VPC '$VPC_NETWORK' existe${NC}"
        
        # Get network details
        NETWORK_URI="projects/$PROJECT_ID/global/networks/$VPC_NETWORK"
        echo "📡 URI de la red: $NETWORK_URI"
        echo ""
        return 0
    else
        echo -e "${RED}❌ Red VPC '$VPC_NETWORK' no existe${NC}"
        echo "💡 Ejecuta primero: ./scripts/setup-vpc-connector.sh"
        exit 1
    fi
}

# Function to list and configure Redis instances
configure_redis_instances() {
    echo -e "${BLUE}🗄️ Configurando Instancias de Redis...${NC}"
    
    # Get all Redis instances
    INSTANCES=$(gcloud redis instances list --region=$REGION --project=$PROJECT_ID \
        --format="value(name)" 2>/dev/null)
    
    if [ -z "$INSTANCES" ]; then
        echo -e "${YELLOW}⚠️ No se encontraron instancias de Redis${NC}"
        echo "💡 Cuando crees una instancia de Redis, usa este comando:"
        echo ""
        echo "gcloud redis instances create rayuela-redis \\"
        echo "  --size=1 \\"
        echo "  --region=$REGION \\"
        echo "  --redis-version=redis_6_x \\"
        echo "  --network=projects/$PROJECT_ID/global/networks/$VPC_NETWORK \\"
        echo "  --project=$PROJECT_ID"
        echo ""
        return 0
    fi
    
    echo "📋 Instancias encontradas: $INSTANCES"
    echo ""
    
    for instance in $INSTANCES; do
        echo -e "${BLUE}🔧 Analizando instancia: $instance${NC}"
        
        # Get current configuration
        INSTANCE_INFO=$(gcloud redis instances describe $instance \
            --region=$REGION --project=$PROJECT_ID \
            --format="value(host,authorizedNetwork,reservedIpRange)" 2>/dev/null)
        
        if [ -n "$INSTANCE_INFO" ]; then
            HOST=$(echo "$INSTANCE_INFO" | cut -d$'\t' -f1)
            AUTH_NETWORK=$(echo "$INSTANCE_INFO" | cut -d$'\t' -f2)
            IP_RANGE=$(echo "$INSTANCE_INFO" | cut -d$'\t' -f3)
            
            echo "📊 Configuración actual:"
            echo "   Host: $HOST"
            echo "   Red autorizada: $AUTH_NETWORK"
            echo "   Rango IP: $IP_RANGE"
            echo ""
            
            # Check if it's using private IP (10.x.x.x, 172.x.x.x, or 192.168.x.x)
            if [[ $HOST =~ ^10\.|^172\.|^192\.168\. ]]; then
                echo -e "${GREEN}✅ Redis usando IP privada${NC}"
            else
                echo -e "${YELLOW}⚠️ IP no parece ser privada: $HOST${NC}"
            fi
            
            # Check if authorized network is correct
            EXPECTED_NETWORK="projects/$PROJECT_ID/global/networks/$VPC_NETWORK"
            if [ "$AUTH_NETWORK" = "$EXPECTED_NETWORK" ]; then
                echo -e "${GREEN}✅ Red autorizada configurada correctamente${NC}"
            else
                echo -e "${YELLOW}⚠️ Red autorizada: $AUTH_NETWORK${NC}"
                echo -e "${YELLOW}   Esperada: $EXPECTED_NETWORK${NC}"
            fi
            
            echo -e "${GREEN}✅ Instancia $instance configurada para red privada${NC}"
        else
            echo -e "${RED}❌ No se pudo obtener información de la instancia${NC}"
        fi
        
        echo ""
    done
}

# Function to create example Redis instance
create_example_redis() {
    echo -e "${BLUE}🔧 Opción: Crear Instancia de Redis de Ejemplo${NC}"
    echo ""
    
    read -p "¿Crear una instancia de Redis de ejemplo? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        INSTANCE_NAME="rayuela-redis-$(date +%s)"
        
        echo "🔧 Creando instancia Redis: $INSTANCE_NAME"
        echo "⚙️ Configuración:"
        echo "   Tamaño: 1GB"
        echo "   Versión: Redis 6.x"
        echo "   Red: $VPC_NETWORK"
        echo "   Región: $REGION"
        echo ""
        
        gcloud redis instances create $INSTANCE_NAME \
            --size=1 \
            --region=$REGION \
            --redis-version=redis_6_x \
            --network=projects/$PROJECT_ID/global/networks/$VPC_NETWORK \
            --project=$PROJECT_ID
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Instancia Redis '$INSTANCE_NAME' creada exitosamente${NC}"
            
            # Wait for instance to be ready
            echo "⏳ Esperando que la instancia esté lista..."
            
            for i in {1..20}; do
                STATE=$(gcloud redis instances describe $INSTANCE_NAME \
                    --region=$REGION --project=$PROJECT_ID \
                    --format="value(state)" 2>/dev/null)
                
                if [ "$STATE" = "READY" ]; then
                    echo -e "${GREEN}✅ Instancia Redis está lista${NC}"
                    break
                elif [ "$STATE" = "CREATING" ]; then
                    echo "⏳ Estado: CREATING... (intento $i/20)"
                    sleep 15
                else
                    echo -e "${YELLOW}⚠️ Estado: $STATE (intento $i/20)${NC}"
                    sleep 15
                fi
                
                if [ $i -eq 20 ]; then
                    echo -e "${YELLOW}⚠️ La instancia aún se está creando. Verifica manualmente.${NC}"
                fi
            done
            
            # Get connection details
            REDIS_INFO=$(gcloud redis instances describe $INSTANCE_NAME \
                --region=$REGION --project=$PROJECT_ID \
                --format="value(host,port)" 2>/dev/null)
            
            if [ -n "$REDIS_INFO" ]; then
                REDIS_HOST=$(echo "$REDIS_INFO" | cut -d$'\t' -f1)
                REDIS_PORT=$(echo "$REDIS_INFO" | cut -d$'\t' -f2)
                
                echo ""
                echo -e "${GREEN}📋 Detalles de conexión:${NC}"
                echo "   Host: $REDIS_HOST"
                echo "   Puerto: $REDIS_PORT"
                echo "   URL: redis://$REDIS_HOST:$REDIS_PORT"
                echo ""
                
                echo -e "${YELLOW}💡 Actualiza tus secretos con estos valores:${NC}"
                echo "   gcloud secrets create REDIS_HOST --data-file=- <<< \"$REDIS_HOST\""
                echo "   gcloud secrets create REDIS_PORT --data-file=- <<< \"$REDIS_PORT\""
                echo "   gcloud secrets create REDIS_URL --data-file=- <<< \"redis://$REDIS_HOST:$REDIS_PORT\""
            fi
        else
            echo -e "${RED}❌ Error creando instancia Redis${NC}"
        fi
    else
        echo -e "${YELLOW}⏭️ Creación de instancia Redis omitida${NC}"
    fi
    
    echo ""
}

# Function to verify configuration
verify_configuration() {
    echo -e "${BLUE}🔍 Verificando Configuración Final...${NC}"
    
    INSTANCES=$(gcloud redis instances list --region=$REGION --project=$PROJECT_ID \
        --format="value(name)" 2>/dev/null)
    
    if [ -z "$INSTANCES" ]; then
        echo -e "${YELLOW}⚠️ No hay instancias de Redis para verificar${NC}"
        return 0
    fi
    
    echo "📋 Estado de seguridad de las instancias Redis:"
    echo ""
    
    for instance in $INSTANCES; do
        echo "🔍 Instancia: $instance"
        
        INSTANCE_INFO=$(gcloud redis instances describe $instance \
            --region=$REGION --project=$PROJECT_ID \
            --format="value(host,authorizedNetwork,state)" 2>/dev/null)
        
        if [ -n "$INSTANCE_INFO" ]; then
            HOST=$(echo "$INSTANCE_INFO" | cut -d$'\t' -f1)
            AUTH_NETWORK=$(echo "$INSTANCE_INFO" | cut -d$'\t' -f2)
            STATE=$(echo "$INSTANCE_INFO" | cut -d$'\t' -f3)
            
            echo "   Estado: $STATE"
            echo "   Host: $HOST"
            echo "   Red: $AUTH_NETWORK"
            
            # Verify private IP
            if [[ $HOST =~ ^10\.|^172\.|^192\.168\. ]]; then
                echo -e "${GREEN}✅ Usando IP privada${NC}"
            else
                echo -e "${YELLOW}⚠️ IP podría no ser privada${NC}"
            fi
            
            # Verify network
            EXPECTED_NETWORK="projects/$PROJECT_ID/global/networks/$VPC_NETWORK"
            if [ "$AUTH_NETWORK" = "$EXPECTED_NETWORK" ]; then
                echo -e "${GREEN}✅ Red autorizada correcta${NC}"
            else
                echo -e "${YELLOW}⚠️ Verificar red autorizada${NC}"
            fi
        else
            echo -e "${RED}❌ No se pudo obtener información${NC}"
        fi
        
        echo ""
    done
}

# Function to provide next steps
provide_next_steps() {
    echo -e "${BLUE}🚀 Próximos Pasos${NC}"
    echo "================="
    echo ""
    
    echo -e "${YELLOW}📋 Para completar la configuración de seguridad:${NC}"
    echo ""
    
    echo "1. 🔗 Asegurar VPC Connector (si no está hecho):"
    echo "   ./scripts/setup-vpc-connector.sh"
    echo ""
    
    echo "2. 🔒 Configurar Cloud SQL para IP privada (si no está hecho):"
    echo "   ./scripts/configure-sql-private.sh"
    echo ""
    
    echo "3. 🔐 Actualizar secretos de Redis con IP privada:"
    echo "   # Obtener IP privada de Redis"
    echo "   REDIS_HOST=\$(gcloud redis instances describe INSTANCE_NAME --region=$REGION --format='value(host)')"
    echo "   gcloud secrets versions add REDIS_HOST --data-file=- <<< \"\$REDIS_HOST\""
    echo ""
    
    echo "4. 🚀 Desplegar servicios:"
    echo "   gcloud builds submit --config cloudbuild-deploy-production.yaml"
    echo ""
    
    echo "5. 🔍 Verificar configuración completa:"
    echo "   ./scripts/verify-vpc-security.sh"
    echo ""
    
    echo -e "${GREEN}💡 Redis ya está configurado para acceso privado por defecto${NC}"
}

# Main execution
echo "🚀 Iniciando configuración de Redis privado..."
echo ""

enable_apis
check_vpc_network
configure_redis_instances
create_example_redis
verify_configuration

echo ""
echo "======================================================"
echo -e "${GREEN}🎉 Configuración de Redis completada!${NC}"
echo "======================================================"

provide_next_steps

echo ""
echo -e "${YELLOW}📝 Nota: Redis por defecto solo acepta conexiones desde la red VPC especificada${NC}" 