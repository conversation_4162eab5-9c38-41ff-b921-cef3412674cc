#!/usr/bin/env python
"""
Quick RLS Status Checker

This script provides a quick overview of the current RLS implementation status
without requiring a database connection.

Usage:
    python -m scripts.check_rls_status
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any

def check_migration_files() -> Dict[str, Any]:
    """Check if RLS migration files exist."""
    migrations_dir = Path(__file__).parent.parent / "alembic" / "versions"
    
    rls_migrations = []
    all_migrations = []
    
    if migrations_dir.exists():
        for migration_file in migrations_dir.glob("*.py"):
            all_migrations.append(migration_file.name)
            
            # Check if it's an RLS-related migration
            with open(migration_file, 'r') as f:
                content = f.read().lower()
                if any(keyword in content for keyword in ['rls', 'row level security', 'row_level_security']):
                    rls_migrations.append(migration_file.name)
    
    return {
        "total_migrations": len(all_migrations),
        "rls_migrations": rls_migrations,
        "has_rls_migrations": len(rls_migrations) > 0
    }

def check_verification_scripts() -> Dict[str, Any]:
    """Check if RLS verification scripts exist."""
    scripts_dir = Path(__file__).parent.parent / "scripts"
    
    verification_scripts = []
    expected_scripts = [
        "scripts/maintenance/verify_rls_comprehensive.py",
        "scripts/maintenance/verify_rls_policies.py",
        "scripts/testing/test_rls_isolation.py",
        "scripts/deploy_rls_policies.py"
    ]

    for script_path in expected_scripts:
        # Remove the scripts/ prefix and construct the full path
        if script_path.startswith("scripts/"):
            relative_path = script_path[8:]  # Remove "scripts/" prefix
        else:
            relative_path = script_path
        full_path = Path(__file__).parent / relative_path
        if full_path.exists():
            verification_scripts.append(script_path)
    
    return {
        "expected_scripts": expected_scripts,
        "existing_scripts": verification_scripts,
        "all_scripts_exist": len(verification_scripts) == len(expected_scripts)
    }

def check_ci_cd_integration() -> Dict[str, Any]:
    """Check if RLS verification is integrated in CI/CD."""
    ci_config_path = Path(__file__).parent.parent / "cloudbuild-tests.yaml"
    
    if not ci_config_path.exists():
        return {
            "ci_config_exists": False,
            "rls_verification_integrated": False
        }
    
    with open(ci_config_path, 'r') as f:
        ci_config = f.read()
    
    rls_keywords = [
        "verify_rls_comprehensive",
        "rls-verification", 
        "test_rls_comprehensive_security"
    ]
    
    integrated_checks = [keyword for keyword in rls_keywords if keyword in ci_config]
    
    return {
        "ci_config_exists": True,
        "rls_verification_integrated": len(integrated_checks) > 0,
        "integrated_checks": integrated_checks
    }

def check_test_files() -> Dict[str, Any]:
    """Check if RLS test files exist."""
    tests_dir = Path(__file__).parent.parent / "tests"
    
    rls_test_files = []
    expected_test_files = [
        "tests/integration/test_rls_comprehensive_security.py",
        "tests/integration/test_multi_tenancy_comprehensive.py"
    ]
    
    for test_path in expected_test_files:
        full_path = Path(__file__).parent.parent / test_path.replace("tests/", "tests/")
        if full_path.exists():
            rls_test_files.append(test_path)
    
    return {
        "expected_test_files": expected_test_files,
        "existing_test_files": rls_test_files,
        "all_tests_exist": len(rls_test_files) == len(expected_test_files)
    }

def generate_status_report() -> None:
    """Generate a comprehensive status report."""
    print("🔍 RLS Implementation Status Report")
    print("=" * 50)
    
    # Check migrations
    migration_status = check_migration_files()
    print(f"\n📦 Migration Files:")
    print(f"   Total migrations: {migration_status['total_migrations']}")
    print(f"   RLS migrations: {len(migration_status['rls_migrations'])}")
    if migration_status['rls_migrations']:
        for migration in migration_status['rls_migrations']:
            print(f"   ✅ {migration}")
    else:
        print("   ❌ No RLS migrations found")
    
    # Check verification scripts
    script_status = check_verification_scripts()
    print(f"\n🔧 Verification Scripts:")
    print(f"   Expected: {len(script_status['expected_scripts'])}")
    print(f"   Existing: {len(script_status['existing_scripts'])}")
    for script in script_status['existing_scripts']:
        print(f"   ✅ {script}")
    
    missing_scripts = set(script_status['expected_scripts']) - set(script_status['existing_scripts'])
    for script in missing_scripts:
        print(f"   ❌ {script}")
    
    # Check CI/CD integration
    ci_status = check_ci_cd_integration()
    print(f"\n🚀 CI/CD Integration:")
    if ci_status['ci_config_exists']:
        print(f"   ✅ CI/CD config exists")
        if ci_status['rls_verification_integrated']:
            print(f"   ✅ RLS verification integrated")
            for check in ci_status['integrated_checks']:
                print(f"      - {check}")
        else:
            print(f"   ❌ RLS verification NOT integrated")
    else:
        print(f"   ❌ CI/CD config not found")
    
    # Check test files
    test_status = check_test_files()
    print(f"\n🧪 Test Files:")
    print(f"   Expected: {len(test_status['expected_test_files'])}")
    print(f"   Existing: {len(test_status['existing_test_files'])}")
    for test_file in test_status['existing_test_files']:
        print(f"   ✅ {test_file}")
    
    missing_tests = set(test_status['expected_test_files']) - set(test_status['existing_test_files'])
    for test_file in missing_tests:
        print(f"   ❌ {test_file}")
    
    # Overall status
    print(f"\n📊 Overall Status:")
    
    all_good = (
        migration_status['has_rls_migrations'] and
        script_status['all_scripts_exist'] and
        ci_status['rls_verification_integrated'] and
        test_status['all_tests_exist']
    )
    
    if all_good:
        print("   🎉 RLS implementation appears complete!")
        print("   ✅ All required files and integrations are present")
        print("   🔒 Ready for deployment and security validation")
    else:
        print("   ⚠️  RLS implementation is incomplete")
        print("   🚨 Missing components detected - see details above")
        print("   📋 Next steps:")
        
        if not migration_status['has_rls_migrations']:
            print("      1. Create and apply RLS migration")
        if not script_status['all_scripts_exist']:
            print("      2. Create missing verification scripts")
        if not ci_status['rls_verification_integrated']:
            print("      3. Integrate RLS verification in CI/CD")
        if not test_status['all_tests_exist']:
            print("      4. Create missing test files")
    
    print(f"\n🔗 Quick Actions:")
    print(f"   Deploy RLS:     python -m scripts.deploy_rls_policies")
    print(f"   Verify RLS:     python -m scripts.maintenance.verify_rls_comprehensive")
    print(f"   Run RLS tests:  pytest tests/integration/test_rls_comprehensive_security.py")

def main():
    """Main function."""
    try:
        generate_status_report()
    except Exception as e:
        print(f"❌ Error generating status report: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
