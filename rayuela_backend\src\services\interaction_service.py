"""
Servicio para la gestión de interacciones.
"""

from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, update
from redis.asyncio import Redis
from fastapi import HTTPException, status
from datetime import datetime

from src.db import schemas
from src.db.models.end_user import EndUser
from src.db.models.product import Product
from src.db.models.interaction import Interaction
from src.core.exceptions import (
    ResourceNotFoundError,
    RateLimitExceededError,
    ProductNotFoundException,
    LimitExceededError,
)
from src.utils.base_logger import log_error, log_info
from src.services import LimitService
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager


class InteractionService:
    """
    Servicio para la gestión de interacciones entre usuarios y productos.

    Este servicio maneja la validación de usuarios y productos, límites de tasa,
    y la creación de interacciones.
    """

    def __init__(
        self, db: AsyncSession, account_id, redis: Redis, limit_service: LimitService
    ):
        """
        Inicializa el servicio de interacciones.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta (puede ser un entero o un objeto Column)
            redis: Cliente Redis para límites de tasa
            limit_service: Servicio de límites
        """
        self.db = db
        # Convertir account_id a entero si es necesario
        self.account_id = account_id.id if hasattr(account_id, "id") else account_id
        self.redis = redis
        self.limit_service = limit_service
        self.artifact_manager = ModelArtifactManager()

    async def verify_user_exists(self, user_id: int) -> EndUser:
        """
        Verifica que un usuario exista y pertenezca a la cuenta.

        Args:
            user_id: ID del usuario

        Returns:
            Objeto EndUser si existe

        Raises:
            ResourceNotFoundError: Si el usuario no existe o no pertenece a la cuenta
        """
        end_user_query = select(EndUser).where(
            EndUser.id == user_id, EndUser.account_id == self.account_id
        )
        end_user_result = await self.db.execute(end_user_query)
        end_user = end_user_result.scalars().first()

        if not end_user:
            raise ResourceNotFoundError("EndUser", user_id)

        return end_user

    async def verify_product_exists(self, product_id: int) -> Product:
        """
        Verifica que un producto exista y pertenezca a la cuenta.

        Args:
            product_id: ID del producto

        Returns:
            Objeto Product si existe

        Raises:
            ProductNotFoundException: Si el producto no existe o no pertenece a la cuenta
        """
        product_query = select(Product).where(
            Product.product_id == product_id, Product.account_id == self.account_id
        )
        product_result = await self.db.execute(product_query)
        product = product_result.scalars().first()

        if not product:
            raise ProductNotFoundException(product_id)

        return product

    async def check_rate_limit(self, user_id: int) -> None:
        """
        Verifica si el usuario ha excedido el límite de tasa para interacciones.

        Args:
            user_id: ID del usuario

        Raises:
            RateLimitExceededError: Si se ha excedido el límite de tasa
        """
        rate_key = f"interaction_rate:{self.account_id}:{user_id}"
        if await self.redis.exists(rate_key):
            raise RateLimitExceededError(
                "Too many interactions. Please try again later."
            )

    async def set_rate_limit(self, user_id: int, cooldown_seconds: int = 60) -> None:
        """
        Establece un límite de tasa para un usuario.

        Args:
            user_id: ID del usuario
            cooldown_seconds: Tiempo de espera en segundos
        """
        rate_key = f"interaction_rate:{self.account_id}:{user_id}"
        await self.redis.setex(rate_key, cooldown_seconds, "1")

    async def _invalidate_user_cache(self, user_id: int) -> bool:
        """
        Invalida la caché de recomendaciones para un usuario específico.

        Args:
            user_id: ID del usuario

        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            # Usar el gestor de artefactos para invalidar la caché
            success = await self.artifact_manager.invalidate_user_cache(
                account_id=self.account_id, user_id=user_id
            )

            if success:
                log_info(
                    f"Caché invalidada automáticamente para user_id={user_id}, account_id={self.account_id}"
                )
            else:
                log_error(
                    f"Error al invalidar caché para user_id={user_id}, account_id={self.account_id}"
                )

            return success
        except Exception as e:
            log_error(f"Error en _invalidate_user_cache: {str(e)}")
            return False

    async def create_interaction(
        self, interaction_data: schemas.InteractionCreate
    ) -> Interaction:
        """
        Crea una nueva interacción entre un usuario y un producto.

        Args:
            interaction_data: Datos de la interacción

        Returns:
            Objeto Interaction creado

        Raises:
            HTTPException: Si ocurre un error durante la creación
            ResourceNotFoundError: Si el usuario no existe
            ProductNotFoundException: Si el producto no existe
            RateLimitExceededError: Si se ha excedido el límite de tasa
        """
        try:
            # Validar límites de interacciones (fuera de la transacción)
            await self.limit_service.validate_interaction_limit()

            # Extraer el ID de usuario directamente (ya no necesitamos mapeo)
            user_id = interaction_data.user_id

            # Verificar usuario (fuera de la transacción)
            end_user = await self.verify_user_exists(user_id)

            # Verificar producto (fuera de la transacción)
            product = await self.verify_product_exists(interaction_data.product_id)

            # Verificar límite de tasa (fuera de la transacción)
            await self.check_rate_limit(end_user.id)

            # Iniciar transacción para operaciones de escritura
            async with self.db.begin():
                # Obtener timestamp actual para consistencia
                current_time = datetime.utcnow()

                # Crear interacción
                # Extraer los campos necesarios del modelo de interacción
                interaction_dict = {
                    "user_id": user_id,
                    "product_id": interaction_data.product_id,
                    "interaction_type": interaction_data.interaction_type,
                    "value": interaction_data.value,
                    "account_id": self.account_id,
                }

                # Incluir metadatos si están disponibles
                if interaction_data.recommendation_metadata:
                    interaction_dict["recommendation_metadata"] = (
                        interaction_data.recommendation_metadata
                    )

                # Si es una interacción de tipo click, view o purchase y tiene recommendation_id,
                # añadir información de confianza para análisis posterior
                if (
                    interaction_data.recommendation_metadata
                    and "recommendation_id" in interaction_data.recommendation_metadata
                    and "confidence" not in interaction_data.recommendation_metadata
                ):

                    # Intentar obtener información de confianza del servicio de recomendaciones
                    try:
                        # Añadir información básica de confianza si no está presente
                        if "confidence" not in interaction_data.recommendation_metadata:
                            interaction_dict["recommendation_metadata"][
                                "confidence"
                            ] = 0.7  # Valor por defecto

                        if "model_type" not in interaction_data.recommendation_metadata:
                            interaction_dict["recommendation_metadata"][
                                "model_type"
                            ] = "hybrid"  # Valor por defecto
                    except Exception as e:
                        log_error(f"Error al añadir información de confianza: {str(e)}")

                db_interaction = Interaction(**interaction_dict)
                self.db.add(db_interaction)

                # Actualizar last_interaction_at en el producto
                await self.db.execute(
                    update(Product)
                    .where(
                        Product.account_id == self.account_id,
                        Product.id == interaction_data.product_id,
                    )
                    .values(last_interaction_at=current_time, updated_at=current_time)
                )

                # Actualizar last_activity_at en el usuario final
                await self.db.execute(
                    update(EndUser)
                    .where(
                        EndUser.account_id == self.account_id,
                        EndUser.id == user_id,
                    )
                    .values(last_activity_at=current_time, updated_at=current_time)
                )

                log_info(
                    f"Updated tracking columns: product_id={interaction_data.product_id}, user_id={user_id}, timestamp={current_time}"
                )

            # Operaciones post-transacción
            # Establecer límite de tasa
            await self.set_rate_limit(end_user.id)

            # Invalidar caché de recomendaciones para este usuario
            await self._invalidate_user_cache(end_user.id)

            return db_interaction

        except (
            ResourceNotFoundError,
            ProductNotFoundException,
            RateLimitExceededError,
            LimitExceededError,
        ):
            # Re-lanzar excepciones específicas para mantener el mismo comportamiento
            raise
        except Exception as e:
            log_error(f"Error creating interaction: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating interaction: {str(e)}",
            )

    async def get_interactions(self, skip: int = 0, limit: int = 10) -> Dict[str, Any]:
        """
        Obtiene las interacciones de la cuenta con paginación.

        Args:
            skip: Número de elementos a saltar
            limit: Número máximo de elementos a devolver

        Returns:
            Diccionario con las interacciones y metadatos de paginación
        """
        try:
            # Usar índice compuesto account_id, timestamp
            interactions_query = (
                select(Interaction)
                .where(Interaction.account_id == self.account_id)
                .order_by(Interaction.timestamp.desc())
                .with_hint(Interaction, "USE INDEX (idx_interaction_account_timestamp)")
                .offset(skip)
                .limit(limit)
            )

            result = await self.db.execute(interactions_query)
            interactions = result.scalars().all()

            # Obtener el total de interacciones
            total_query = (
                select(func.count())
                .select_from(Interaction)
                .where(Interaction.account_id == self.account_id)
            )
            total_result = await self.db.execute(total_query)
            total = total_result.scalar_one()

            return {
                "items": interactions,
                "total": total,
                "page": skip // limit + 1 if limit > 0 else 1,
                "size": limit,
            }

        except Exception as e:
            log_error(f"Error getting interactions: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting interactions: {str(e)}",
            )
