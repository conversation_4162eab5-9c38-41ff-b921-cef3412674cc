"""
Service for tracking API usage and enforcing rate limits.
"""
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timezone, timedelta
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
import time

from src.core.config import settings
from src.db.models import Subscription, Account, AccountUsageMetrics
from src.core.exceptions import RateLimitExceededError, LimitExceededError
from src.utils.base_logger import log_info, log_error, log_warning
from src.core.redis_utils import get_redis


class UsageMeterService:
    """
    Service for tracking API usage and enforcing rate limits.
    This service centralizes the logic for counting API calls and enforcing rate limits.
    It uses Redis for fast, atomic counters and periodically syncs to the database.
    """

    def __init__(self, db: AsyncSession, redis: Optional[Redis] = None):
        """Initialize the service with a database session and Redis connection."""
        self.db = db
        self._redis = redis
        self._cache_prefix = "usage_meter"
        self._rate_limit_prefix = "rate_limit"
        self._monthly_counter_prefix = "monthly_counter"

    async def get_redis(self) -> Redis:
        """Get Redis connection."""
        if not self._redis:
            self._redis = await get_redis()
        return self._redis

    def _get_rate_limit_key(self, account_id: str) -> str:
        """Generate key for rate limit counter."""
        current_minute = int(time.time() / 60)
        return f"{self._rate_limit_prefix}:{account_id}:{current_minute}"

    def _get_monthly_counter_key(self, account_id: str) -> str:
        """Generate key for monthly API call counter."""
        current_month = datetime.now(timezone.utc).strftime("%Y-%m")
        return f"{self._monthly_counter_prefix}:{account_id}:{current_month}"

    async def increment_api_call(self, account_id: str) -> int:
        """
        Increment API call counter for an account.

        Args:
            account_id: Account ID

        Returns:
            Current count for the current month
        """
        try:
            redis = await self.get_redis()

            # Increment monthly counter in Redis
            monthly_key = self._get_monthly_counter_key(account_id)
            current_count = await redis.incr(monthly_key)

            # Set expiration if this is a new key (30 days)
            if current_count == 1:
                await redis.expire(monthly_key, 60 * 60 * 24 * 30)

            return current_count
        except Exception as e:
            log_error(f"Error incrementing API call counter: {str(e)}")
            # Fallback to database update
            return await self._increment_api_call_in_db(account_id)

    async def _increment_api_call_in_db(self, account_id: str) -> int:
        """
        Increment API call counter directly in the database.
        This is a fallback method when Redis is not available.

        Args:
            account_id: Account ID

        Returns:
            Current count for the current month
        """
        try:
            # Use explicit transaction for database write operation
            async with self.db.begin():
                # Get subscription
                stmt = select(Subscription).where(Subscription.account_id == account_id)
                result = await self.db.execute(stmt)
                subscription = result.scalars().first()

                if not subscription:
                    log_warning(f"No subscription found for account {account_id}")
                    return 0

                # Update subscription in database
                stmt = (
                    update(Subscription)
                    .where(Subscription.account_id == account_id)
                    .values(
                        monthly_api_calls_used=Subscription.monthly_api_calls_used + 1,
                    )
                )
                await self.db.execute(stmt)

                # Return the updated count
                return subscription.monthly_api_calls_used + 1
        except Exception as e:
            log_error(f"Error incrementing API call counter in database: {str(e)}")
            return 0

    async def check_rate_limit(self, account_id: str, max_requests_per_minute: int) -> bool:
        """
        Check if the account has exceeded its rate limit.

        Args:
            account_id: Account ID
            max_requests_per_minute: Maximum requests allowed per minute

        Returns:
            True if the limit is not exceeded, False otherwise

        Raises:
            RateLimitExceededError: If the rate limit is exceeded
        """
        try:
            redis = await self.get_redis()

            # Get rate limit key
            rate_key = self._get_rate_limit_key(account_id)

            # Increment counter
            current_count = await redis.incr(rate_key)

            # Set expiration if this is a new key (60 seconds)
            if current_count == 1:
                await redis.expire(rate_key, 60)

            # Check if limit is exceeded
            if current_count > max_requests_per_minute:
                log_warning(f"Rate limit exceeded for account {account_id}: {current_count}/{max_requests_per_minute}")
                raise RateLimitExceededError(
                    f"Rate limit exceeded: {max_requests_per_minute} requests per minute"
                )

            return True
        except RateLimitExceededError:
            raise
        except Exception as e:
            log_error(f"Error checking rate limit: {str(e)}")
            # If Redis is not available, allow the request
            return True

    async def check_monthly_limit(self, account_id: str, max_requests_per_month: int) -> bool:
        """
        Check if the account has exceeded its monthly API call limit.

        Args:
            account_id: Account ID
            max_requests_per_month: Maximum requests allowed per month

        Returns:
            True if the limit is not exceeded, False otherwise

        Raises:
            LimitExceededError: If the monthly limit is exceeded
        """
        try:
            redis = await self.get_redis()

            # Get monthly counter key
            monthly_key = self._get_monthly_counter_key(account_id)

            # Get current count
            current_count = await redis.get(monthly_key)

            if current_count is None:
                # If no count in Redis, get from database
                current_count = await self._get_monthly_count_from_db(account_id)

                # Store in Redis for future checks
                await redis.set(monthly_key, current_count, ex=60 * 60 * 24 * 30)
            else:
                current_count = int(current_count)

            # Check if limit is exceeded
            if current_count > max_requests_per_month:
                log_warning(f"Monthly API call limit exceeded for account {account_id}: {current_count}/{max_requests_per_month}")
                raise LimitExceededError(
                    f"Monthly API call limit exceeded: {current_count}/{max_requests_per_month}"
                )

            return True
        except LimitExceededError:
            raise
        except Exception as e:
            log_error(f"Error checking monthly limit: {str(e)}")
            # If Redis is not available, check database
            return await self._check_monthly_limit_in_db(account_id, max_requests_per_month)

    async def _get_monthly_count_from_db(self, account_id: str) -> int:
        """
        Get monthly API call count from database.

        Args:
            account_id: Account ID

        Returns:
            Current count for the current month
        """
        try:
            # Get subscription
            stmt = select(Subscription).where(Subscription.account_id == account_id)
            result = await self.db.execute(stmt)
            subscription = result.scalars().first()

            if not subscription:
                log_warning(f"No subscription found for account {account_id}")
                return 0

            return subscription.monthly_api_calls_used
        except Exception as e:
            log_error(f"Error getting monthly count from database: {str(e)}")
            return 0

    async def _check_monthly_limit_in_db(self, account_id: str, max_requests_per_month: int) -> bool:
        """
        Check monthly API call limit directly in the database.
        This is a fallback method when Redis is not available.

        Args:
            account_id: Account ID
            max_requests_per_month: Maximum requests allowed per month

        Returns:
            True if the limit is not exceeded, False otherwise

        Raises:
            LimitExceededError: If the monthly limit is exceeded
        """
        try:
            # Get subscription
            stmt = select(Subscription).where(Subscription.account_id == account_id)
            result = await self.db.execute(stmt)
            subscription = result.scalars().first()

            if not subscription:
                log_warning(f"No subscription found for account {account_id}")
                return True

            # Check if limit is exceeded
            if subscription.monthly_api_calls_used > max_requests_per_month:
                log_warning(f"Monthly API call limit exceeded for account {account_id}: {subscription.monthly_api_calls_used}/{max_requests_per_month}")
                raise LimitExceededError(
                    f"Monthly API call limit exceeded: {subscription.monthly_api_calls_used}/{max_requests_per_month}"
                )

            return True
        except LimitExceededError:
            raise
        except Exception as e:
            log_error(f"Error checking monthly limit in database: {str(e)}")
            # If database check fails, allow the request
            return True

    async def sync_counters_to_db(self) -> Dict[str, int]:
        """
        Sync Redis counters to the database.
        This should be called periodically by a Celery task.

        Returns:
            Dictionary with account IDs and their updated counts
        """
        try:
            redis = await self.get_redis()
            results = {}

            # Get all monthly counter keys
            keys = await redis.keys(f"{self._monthly_counter_prefix}:*")

            for key in keys:
                try:
                    # Extract account ID and month from key
                    parts = key.decode().split(":")
                    if len(parts) != 3:
                        continue

                    account_id = parts[1]
                    month = parts[2]

                    # Get current count from Redis
                    count = await redis.get(key)
                    if count is None:
                        continue

                    count = int(count)

                    # Update database
                    await self._update_db_counter(account_id, count)

                    results[account_id] = count
                except Exception as e:
                    log_error(f"Error syncing counter for key {key}: {str(e)}")
                    continue

            return results
        except Exception as e:
            log_error(f"Error syncing counters to database: {str(e)}")
            return {}

    async def _update_db_counter(self, account_id: str, count: int) -> None:
        """
        Update API call counter in the database.

        Args:
            account_id: Account ID
            count: Current count from Redis
        """
        try:
            # Use explicit transaction for database write operation
            async with self.db.begin():
                # Get subscription
                stmt = select(Subscription).where(Subscription.account_id == account_id)
                result = await self.db.execute(stmt)
                subscription = result.scalars().first()

                if not subscription:
                    log_warning(f"No subscription found for account {account_id}")
                    return

                # Update only if Redis count is higher
                if count > subscription.monthly_api_calls_used:
                    # Update subscription in database
                    stmt = (
                        update(Subscription)
                        .where(Subscription.account_id == account_id)
                        .values(
                            monthly_api_calls_used=count,
                        )
                    )
                    await self.db.execute(stmt)

                    log_info(f"Updated API call counter for account {account_id}: {count}")
        except Exception as e:
            log_error(f"Error updating DB counter: {str(e)}")

    async def reset_monthly_counters(self) -> Dict[str, bool]:
        """
        Reset monthly API call counters for all accounts.
        This should be called at the beginning of each month by a Celery task.

        Returns:
            Dictionary with account IDs and reset status
        """
        try:
            redis = await self.get_redis()

            # Get all monthly counter keys
            keys = await redis.keys(f"{self._monthly_counter_prefix}:*")

            # Delete all keys
            if keys:
                await redis.delete(*keys)

            # Reset counters in database within explicit transaction
            async with self.db.begin():
                stmt = (
                    update(Subscription)
                    .values(
                        monthly_api_calls_used=0,
                        last_reset_date=datetime.now(timezone.utc)
                    )
                )
                await self.db.execute(stmt)

            log_info("Reset monthly API call counters for all accounts")
            return {"success": True}
        except Exception as e:
            log_error(f"Error resetting monthly counters: {str(e)}")
            return {"success": False, "error": str(e)}
