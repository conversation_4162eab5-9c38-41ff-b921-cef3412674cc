"""
Tests for UsageMeterMiddleware Redis cache functionality.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from src.middleware.usage_meter_middleware import UsageMeterMiddleware
from src.db.models import Account, Subscription
from src.db.enums import SubscriptionPlan


@pytest.fixture
def mock_app():
    """Mock FastAPI app."""
    return MagicMock()


@pytest.fixture
def middleware(mock_app):
    """Create UsageMeterMiddleware instance."""
    return UsageMeterMiddleware(mock_app)


@pytest.fixture
def mock_account():
    """Mock Account object."""
    account = Account()
    account.account_id = 123
    account.name = "Test Account"
    account.mercadopago_customer_id = "mp_123"
    account.created_at = datetime(2023, 1, 1, tzinfo=timezone.utc)
    account.updated_at = datetime(2023, 1, 2, tzinfo=timezone.utc)
    account.is_active = True
    account.deleted_at = None
    return account


@pytest.fixture
def mock_subscription():
    """Mock Subscription object."""
    subscription = Subscription()
    subscription.account_id = 123
    subscription.plan_type = SubscriptionPlan.BASIC
    subscription.api_calls_limit = 1000
    subscription.storage_limit = 1000000
    subscription.is_active = True
    subscription.created_at = datetime(2023, 1, 1, tzinfo=timezone.utc)
    subscription.updated_at = datetime(2023, 1, 2, tzinfo=timezone.utc)
    subscription.monthly_api_calls_used = 50
    subscription.storage_used = 50000
    return subscription


@pytest.mark.asyncio
async def test_get_account_from_api_key_cache_hit(middleware, mock_account):
    """Test account retrieval from Redis cache."""
    api_key = "test_api_key"
    
    # Mock cached data
    cached_data = {
        "account_id": 123,
        "name": "Test Account",
        "mercadopago_customer_id": "mp_123",
        "created_at": "2023-01-01T00:00:00+00:00",
        "updated_at": "2023-01-02T00:00:00+00:00",
        "is_active": True,
        "deleted_at": None,
    }
    
    with patch.object(middleware._cache_manager, 'get', return_value=cached_data):
        mock_db = AsyncMock()
        result = await middleware._get_account_from_api_key(mock_db, api_key)
        
        assert result is not None
        assert result.account_id == 123
        assert result.name == "Test Account"
        assert result.is_active is True


@pytest.mark.asyncio
async def test_get_account_from_api_key_cache_miss(middleware, mock_account):
    """Test account retrieval when cache misses."""
    api_key = "test_api_key"
    
    with patch.object(middleware._cache_manager, 'get', return_value=None), \
         patch.object(middleware._cache_manager, 'set', return_value=True), \
         patch('src.core.security.api_key.hash_api_key', return_value="hashed_key"), \
         patch('src.db.repositories.api_key.ApiKeyRepository') as mock_api_repo, \
         patch('src.db.repositories.account.AccountRepository') as mock_account_repo:
        
        # Mock API key repository
        mock_api_key_instance = AsyncMock()
        mock_api_key_model = MagicMock()
        mock_api_key_model.account_id = 123
        mock_api_key_instance.get_by_api_key_hash.return_value = mock_api_key_model
        mock_api_key_instance.update_last_used.return_value = None
        mock_api_repo.return_value = mock_api_key_instance
        
        # Mock account repository
        mock_account_instance = AsyncMock()
        mock_account_instance.get_by_id.return_value = mock_account
        mock_account_repo.return_value = mock_account_instance
        
        mock_db = AsyncMock()
        result = await middleware._get_account_from_api_key(mock_db, api_key)
        
        assert result is not None
        assert result.account_id == 123
        assert result.name == "Test Account"


@pytest.mark.asyncio
async def test_get_subscription_cache_hit(middleware, mock_subscription):
    """Test subscription retrieval from Redis cache."""
    account_id = 123
    
    # Mock cached data
    cached_data = {
        "account_id": 123,
        "plan_type": "BASIC",
        "api_calls_limit": 1000,
        "storage_limit": 1000000,
        "is_active": True,
        "created_at": "2023-01-01T00:00:00+00:00",
        "updated_at": "2023-01-02T00:00:00+00:00",
        "monthly_api_calls_used": 50,
        "storage_used": 50000,
    }
    
    with patch.object(middleware._cache_manager, 'get', return_value=cached_data):
        mock_db = AsyncMock()
        result = await middleware._get_subscription(mock_db, account_id)
        
        assert result is not None
        assert result.account_id == 123
        assert result.plan_type == SubscriptionPlan.BASIC
        assert result.api_calls_limit == 1000
        assert result.is_active is True


@pytest.mark.asyncio
async def test_get_subscription_cache_miss(middleware, mock_subscription):
    """Test subscription retrieval when cache misses."""
    account_id = 123
    
    with patch.object(middleware._cache_manager, 'get', return_value=None), \
         patch.object(middleware._cache_manager, 'set', return_value=True):
        
        # Mock database query
        mock_db = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalars.return_value.first.return_value = mock_subscription
        mock_db.execute.return_value = mock_result
        
        result = await middleware._get_subscription(mock_db, account_id)
        
        assert result is not None
        assert result.account_id == 123
        assert result.plan_type == SubscriptionPlan.BASIC


@pytest.mark.asyncio
async def test_cache_error_handling(middleware):
    """Test that cache errors don't break functionality."""
    api_key = "test_api_key"
    
    # Mock cache to raise exception
    with patch.object(middleware._cache_manager, 'get', side_effect=Exception("Redis error")), \
         patch('src.core.security.api_key.hash_api_key', return_value="hashed_key"), \
         patch('src.db.repositories.api_key.ApiKeyRepository') as mock_api_repo, \
         patch('src.db.repositories.account.AccountRepository') as mock_account_repo:
        
        # Mock repositories to return None (API key not found)
        mock_api_key_instance = AsyncMock()
        mock_api_key_instance.get_by_api_key_hash.return_value = None
        mock_api_repo.return_value = mock_api_key_instance
        
        mock_db = AsyncMock()
        result = await middleware._get_account_from_api_key(mock_db, api_key)
        
        # Should return None gracefully, not raise exception
        assert result is None
