import pytest
import httpx
from datetime import datetime
from src.core.config import settings

class TestCoreFlows:
    """Tests E2E CRÍTICOS que bloquean el deployment si fallan.

    Esta clase contiene únicamente los flujos de negocio más importantes
    que deben funcionar correctamente antes de cualquier deployment.
    """

    @pytest.fixture
    def api_client(self):
        """Crear cliente HTTP para pruebas."""
        return httpx.AsyncClient(base_url=settings.API_V1_STR)

    @pytest.mark.asyncio
    async def test_critical_auth_flow(
        self,
        api_client,
        db_session
    ):
        """CRÍTICO: Test para el flujo completo de autenticación."""
        # Registrar nueva cuenta
        register_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "name": "Critical Test Account"
        }
        response = await api_client.post("/accounts/register", json=register_data)
        assert response.status_code == 201, f"Registration failed: {response.text}"
        account_data = response.json()
        assert account_data["email"] == register_data["email"]

        # Login
        login_data = {
            "username": register_data["email"],
            "password": register_data["password"]
        }
        response = await api_client.post("/auth/login", data=login_data)
        assert response.status_code == 200, f"Login failed: {response.text}"
        tokens = response.json()
        assert "access_token" in tokens
        assert "api_key" in tokens

        # Verificar acceso autenticado
        response = await api_client.get(
            "/accounts/me",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        assert response.status_code == 200, f"Authenticated access failed: {response.text}"
        assert response.json()["email"] == register_data["email"]

    @pytest.mark.asyncio
    async def test_critical_api_key_auth(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """CRÍTICO: Test para autenticación con API Key."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]

        # Verificar acceso con API Key válida
        response = await api_client.get(
            "/products",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200, f"API Key auth failed: {response.text}"

        # Verificar rechazo sin API Key
        response = await api_client.get("/products")
        assert response.status_code == 401, "Unauthenticated access should be rejected"

    @pytest.mark.asyncio
    async def test_critical_multi_tenancy_isolation(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """CRÍTICO: Test para aislamiento entre tenants."""
        # Obtener tokens de diferentes tenants
        account_a = test_accounts[0]
        account_b = test_accounts[1]
        api_key_a = test_tokens[account_a.id]["regular"]["api_key"]
        api_key_b = test_tokens[account_b.id]["regular"]["api_key"]

        # Crear producto en tenant A
        product_data = {
            "name": "Critical Tenant A Product",
            "price": 100.0
        }
        response = await api_client.post(
            "/products",
            json=product_data,
            headers={"X-API-Key": api_key_a}
        )
        assert response.status_code == 201, f"Product creation failed: {response.text}"
        product_id = response.json()["id"]

        # CRÍTICO: Verificar que tenant B NO puede acceder al producto de tenant A
        response = await api_client.get(
            f"/products/{product_id}",
            headers={"X-API-Key": api_key_b}
        )
        assert response.status_code == 404, "CRITICAL: Tenant isolation breach detected!"

        # CRÍTICO: Verificar que cada tenant solo ve sus propios productos
        response_a = await api_client.get(
            "/products",
            headers={"X-API-Key": api_key_a}
        )
        response_b = await api_client.get(
            "/products",
            headers={"X-API-Key": api_key_b}
        )

        assert response_a.status_code == 200
        assert response_b.status_code == 200

        products_a = response_a.json()
        products_b = response_b.json()

        # Verificar que no hay productos compartidos entre tenants
        product_ids_a = {p["id"] for p in products_a}
        product_ids_b = {p["id"] for p in products_b}
        assert not product_ids_a.intersection(product_ids_b), "CRITICAL: Data leakage between tenants!"

    @pytest.mark.asyncio
    async def test_critical_recommendations_flow(
        self,
        api_client,
        test_accounts,
        test_tokens,
        test_end_users
    ):
        """CRÍTICO: Test para el flujo básico de recomendaciones."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        user = test_end_users[account.id][0]

        # Obtener recomendaciones personalizadas
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={"user_id": user.id},
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200, f"Recommendations failed: {response.text}"
        recommendations = response.json()
        assert "items" in recommendations
        assert len(recommendations["items"]) >= 0  # Puede estar vacío en tests


class TestAPIFlows:
    """Tests E2E para los flujos críticos de la API."""

    @pytest.fixture
    def api_client(self):
        """Crear cliente HTTP para pruebas."""
        return httpx.AsyncClient(base_url=settings.API_V1_STR)
    
@pytest.mark.asyncio
async def test_account_registration_and_login(
        self,
        api_client,
        db_session
    ):
        """Test para el flujo de registro y login."""
        # Registrar nueva cuenta
        register_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "name": "Test Account"
        }
        response = await api_client.post("/accounts/register", json=register_data)
        assert response.status_code == 201
        account_data = response.json()
        assert account_data["email"] == register_data["email"]
        
        # Login
        login_data = {
            "username": register_data["email"],
            "password": register_data["password"]
        }
        response = await api_client.post("/auth/login", data=login_data)
        assert response.status_code == 200
        tokens = response.json()
        assert "access_token" in tokens
        assert "api_key" in tokens
        
        # Obtener información de la cuenta
        response = await api_client.get(
            "/accounts/me",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        assert response.status_code == 200
        assert response.json()["email"] == register_data["email"]
    
async def test_api_key_authentication(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """Test para autenticación con API Key."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        
        # Acceder a endpoint privado con API Key
        response = await api_client.get(
            "/products",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        
        # Intentar acceder sin API Key
        response = await api_client.get("/products")
        assert response.status_code == 401
    
async def test_data_ingestion_flow(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """Test para el flujo de ingesta de datos."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        
        # Subir lote de datos
        batch_data = {
            "type": "products",
            "items": [
                {"name": "Product 1", "price": 10.0},
                {"name": "Product 2", "price": 20.0}
            ]
        }
        response = await api_client.post(
            "/data_ingestion/batch",
            json=batch_data,
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 202
        job_data = response.json()
        assert "job_id" in job_data
        
        # Consultar estado del job
        response = await api_client.get(
            f"/data_ingestion/batch/{job_data['job_id']}",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        assert "status" in response.json()
    
async def test_training_pipeline_flow(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """Test para el flujo del pipeline de entrenamiento."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        
        # Iniciar entrenamiento
        training_data = {
            "params": {
                "epochs": 10,
                "batch_size": 32
            }
        }
        response = await api_client.post(
            "/pipeline/train",
            json=training_data,
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 202
        job_data = response.json()
        assert "job_id" in job_data
        
        # Consultar estado del job
        response = await api_client.get(
            f"/pipeline/jobs/{job_data['job_id']}/status",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        assert "status" in response.json()
    
async def test_recommendations_flow(
        self,
        api_client,
        test_accounts,
        test_tokens,
        test_end_users
    ):
        """Test para el flujo de recomendaciones."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        user = test_end_users[account.id][0]
        
        # Obtener recomendaciones
        response = await api_client.get(
            f"/recommendations/personalized/{user.id}",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        recommendations = response.json()
        assert "items" in recommendations
        assert len(recommendations["items"]) > 0
    
async def test_multi_tenancy_e2e(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """Test para verificar aislamiento entre tenants en flujos E2E."""
        # Obtener tokens de diferentes tenants
        account_a = test_accounts[0]
        account_b = test_accounts[1]
        api_key_a = test_tokens[account_a.id]["regular"]["api_key"]
        api_key_b = test_tokens[account_b.id]["regular"]["api_key"]
        
        # Crear producto en tenant A
        product_data = {
            "name": "Tenant A Product",
            "price": 100.0
        }
        response = await api_client.post(
            "/products",
            json=product_data,
            headers={"X-API-Key": api_key_a}
        )
        assert response.status_code == 201
        product_id = response.json()["id"]
        
        # Intentar acceder al producto con tenant B
        response = await api_client.get(
            f"/products/{product_id}",
            headers={"X-API-Key": api_key_b}
        )
        assert response.status_code == 404
        
        # Verificar que cada tenant solo ve sus propios productos
        response = await api_client.get(
            "/products",
            headers={"X-API-Key": api_key_a}
        )
        assert response.status_code == 200
        products_a = response.json()
        assert len(products_a) > 0
        
        response = await api_client.get(
            "/products",
            headers={"X-API-Key": api_key_b}
        )
        assert response.status_code == 200
        products_b = response.json()
        assert len(products_b) > 0
        
        # Verificar que no hay productos compartidos
        product_ids_a = {p["id"] for p in products_a}
        product_ids_b = {p["id"] for p in products_b}
        assert not product_ids_a.intersection(product_ids_b)
    
async def test_error_handling(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """Test para el manejo de errores en la API."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        
        # Intentar crear producto inválido
        invalid_product = {
            "name": "",  # Nombre vacío
            "price": -10.0  # Precio negativo
        }
        response = await api_client.post(
            "/products",
            json=invalid_product,
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 422
        assert "validation_error" in response.json()
        
        # Intentar acceder a recurso inexistente
        response = await api_client.get(
            "/products/999999",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 404
        
        # Intentar acceder sin autenticación
        response = await api_client.get("/products")
        assert response.status_code == 401
        
        # Intentar acceder con token inválido
        response = await api_client.get(
            "/products",
            headers={"X-API-Key": "invalid_key"}
        )
        assert response.status_code == 401 