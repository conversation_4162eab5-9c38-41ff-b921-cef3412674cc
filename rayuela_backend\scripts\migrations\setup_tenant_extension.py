import asyncio
import asyncpg
from src.core.config import settings
from src.utils.base_logger import log_info, log_error

async def setup_tenant_extension():
    """Configura la extensión de tenant en PostgreSQL."""
    try:
        # Conectar a la base de datos
        conn = await asyncpg.connect(
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD,
            database=settings.POSTGRES_DB,
            host=settings.POSTGRES_SERVER,
            port=settings.POSTGRES_PORT
        )
        
        # Leer y ejecutar el script SQL
        with open('scripts/create_tenant_extension.sql', 'r') as f:
            sql_script = f.read()
            
        await conn.execute(sql_script)
        log_info("Tenant extension setup completed successfully")
        
    except Exception as e:
        log_error(f"Error setting up tenant extension: {str(e)}")
        raise
    finally:
        if conn:
            await conn.close()

if __name__ == "__main__":
    asyncio.run(setup_tenant_extension()) 