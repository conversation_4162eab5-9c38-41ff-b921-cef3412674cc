#!/usr/bin/env python
"""
Script para ejecutar tests de carga con Locust.
Este script inicia la API simplificada y ejecuta los tests de carga.
"""

import os
import sys
import argparse
import subprocess
import time
import signal
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.utils.base_logger import logger


def start_api_server():
    """
    Iniciar el servidor API en un proceso separado.
    
    Returns:
        Proceso del servidor API
    """
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")
    
    logger.info("Iniciando servidor API...")
    api_process = subprocess.Popen(
        ["python", "-m", "scripts.run_api_simple"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        env=env
    )
    
    # Esperar a que el servidor esté listo
    logger.info("Esperando a que el servidor API esté listo...")
    time.sleep(5)
    
    return api_process


def run_locust_test(host="localhost", port=8001, users=10, spawn_rate=1, 
                   run_time=60, headless=True):
    """
    Ejecutar test de carga con Locust.
    
    Args:
        host: Host del servidor API
        port: Puerto del servidor API
        users: Número de usuarios concurrentes
        spawn_rate: Tasa de creación de usuarios por segundo
        run_time: Tiempo de ejecución en segundos
        headless: Ejecutar en modo headless (sin interfaz web)
    
    Returns:
        Código de salida (0 si el test fue exitoso, 1 en caso contrario)
    """
    # Configurar el entorno para que locust pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")
    
    # Construir el comando
    cmd = [
        "locust",
        "-f", "tests/load/locustfile.py",
        "--host", f"http://{host}:{port}",
    ]
    
    if headless:
        cmd.extend([
            "--headless",
            "-u", str(users),
            "-r", str(spawn_rate),
            "-t", f"{run_time}s",
            "--html", "load_test_report.html",
        ])
    
    # Ejecutar el comando
    logger.info(f"Ejecutando test de carga: {' '.join(cmd)}")
    result = subprocess.run(cmd, env=env)
    
    if headless:
        logger.info(f"Reporte generado en load_test_report.html")
    
    return result.returncode


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Ejecutar tests de carga")
    parser.add_argument("--host", type=str, default="localhost", help="Host del servidor API")
    parser.add_argument("--port", type=int, default=8001, help="Puerto del servidor API")
    parser.add_argument("--users", type=int, default=10, help="Número de usuarios concurrentes")
    parser.add_argument("--spawn-rate", type=int, default=1, help="Tasa de creación de usuarios por segundo")
    parser.add_argument("--time", type=int, default=60, help="Tiempo de ejecución en segundos")
    parser.add_argument("--headless", action="store_true", help="Ejecutar en modo headless (sin interfaz web)")
    parser.add_argument("--no-api", action="store_true", help="No iniciar el servidor API (asume que ya está en ejecución)")
    
    args = parser.parse_args()
    
    api_process = None
    
    try:
        # Iniciar el servidor API
        if not args.no_api:
            api_process = start_api_server()
        
        # Ejecutar el test de carga
        return run_locust_test(
            host=args.host,
            port=args.port,
            users=args.users,
            spawn_rate=args.spawn_rate,
            run_time=args.time,
            headless=args.headless
        )
    
    finally:
        # Detener el servidor API
        if api_process:
            logger.info("Deteniendo servidor API...")
            api_process.terminate()
            api_process.wait()


if __name__ == "__main__":
    sys.exit(main())
