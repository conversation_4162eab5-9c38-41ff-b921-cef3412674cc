#!/usr/bin/env python
"""
Script to monitor Celery worker resource usage.

This script connects to the Celery broker and retrieves information about
active workers, their queues, and resource usage.

Usage:
    python monitor_workers.py [--broker BROKER_URL]
"""

import argparse
import json
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

from celery import Celery


def get_celery_app(broker_url: Optional[str] = None) -> Celery:
    """
    Create a Celery app instance for monitoring.

    Args:
        broker_url: Redis URL for the Celery broker (optional)

    Returns:
        Celery app instance
    """
    # Use provided broker URL or get from environment
    broker = broker_url or os.environ.get("REDIS_URL", "redis://localhost:6379/0")
    
    # Create a minimal Celery app for monitoring
    app = Celery(broker=broker)
    return app


def get_worker_stats(app: Celery) -> Dict[str, Any]:
    """
    Get statistics about active Celery workers.

    Args:
        app: Celery app instance

    Returns:
        Dictionary with worker statistics
    """
    # Get active workers
    active_workers = app.control.inspect().active()
    
    # Get worker stats
    stats = app.control.inspect().stats()
    
    # Get registered tasks
    registered = app.control.inspect().registered()
    
    # Get active queues
    active_queues = app.control.inspect().active_queues()
    
    # Get reserved tasks
    reserved = app.control.inspect().reserved()
    
    # Get scheduled tasks
    scheduled = app.control.inspect().scheduled()
    
    return {
        "timestamp": datetime.now().isoformat(),
        "active_workers": active_workers or {},
        "stats": stats or {},
        "registered": registered or {},
        "active_queues": active_queues or {},
        "reserved": reserved or {},
        "scheduled": scheduled or {},
    }


def format_worker_stats(stats: Dict[str, Any]) -> str:
    """
    Format worker statistics for display.

    Args:
        stats: Worker statistics dictionary

    Returns:
        Formatted string with worker statistics
    """
    output = []
    
    # Add timestamp
    output.append(f"Timestamp: {stats['timestamp']}")
    output.append("")
    
    # Add active workers
    output.append("=== Active Workers ===")
    if not stats["active_workers"]:
        output.append("No active workers found.")
    else:
        for worker_name, tasks in stats["active_workers"].items():
            output.append(f"Worker: {worker_name}")
            output.append(f"  Active tasks: {len(tasks)}")
            for i, task in enumerate(tasks[:3], 1):  # Show only first 3 tasks
                output.append(f"    {i}. {task['name']} (id: {task['id'][:8]}...)")
            if len(tasks) > 3:
                output.append(f"    ... and {len(tasks) - 3} more tasks")
            output.append("")
    
    # Add worker stats
    output.append("=== Worker Stats ===")
    if not stats["stats"]:
        output.append("No worker stats found.")
    else:
        for worker_name, worker_stats in stats["stats"].items():
            output.append(f"Worker: {worker_name}")
            output.append(f"  Processes: {worker_stats.get('pool', {}).get('max-concurrency', 'N/A')}")
            output.append(f"  Prefetch: {worker_stats.get('prefetch_count', 'N/A')}")
            output.append(f"  Broker transport: {worker_stats.get('broker', {}).get('transport', 'N/A')}")
            output.append("")
    
    # Add active queues
    output.append("=== Active Queues ===")
    if not stats["active_queues"]:
        output.append("No active queues found.")
    else:
        for worker_name, queues in stats["active_queues"].items():
            output.append(f"Worker: {worker_name}")
            for queue in queues:
                output.append(f"  Queue: {queue['name']}")
                output.append(f"    Exchange: {queue['exchange']['name']}")
                output.append(f"    Routing key: {queue['routing_key']}")
            output.append("")
    
    # Add reserved tasks
    output.append("=== Reserved Tasks ===")
    if not stats["reserved"]:
        output.append("No reserved tasks found.")
    else:
        for worker_name, tasks in stats["reserved"].items():
            output.append(f"Worker: {worker_name}")
            output.append(f"  Reserved tasks: {len(tasks)}")
            for i, task in enumerate(tasks[:3], 1):  # Show only first 3 tasks
                output.append(f"    {i}. {task['name']} (id: {task['id'][:8]}...)")
            if len(tasks) > 3:
                output.append(f"    ... and {len(tasks) - 3} more tasks")
            output.append("")
    
    return "\n".join(output)


def monitor_workers(broker_url: Optional[str] = None, interval: int = 5, count: int = 1) -> None:
    """
    Monitor Celery workers and print statistics.

    Args:
        broker_url: Redis URL for the Celery broker (optional)
        interval: Interval between checks in seconds (default: 5)
        count: Number of checks to perform (default: 1, 0 for infinite)
    """
    app = get_celery_app(broker_url)
    
    iteration = 0
    try:
        while count == 0 or iteration < count:
            stats = get_worker_stats(app)
            print(format_worker_stats(stats))
            print("\n" + "=" * 80 + "\n")
            
            iteration += 1
            if count == 0 or iteration < count:
                time.sleep(interval)
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user.")


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Monitor Celery worker resource usage")
    parser.add_argument("--broker", help="Redis URL for the Celery broker")
    parser.add_argument("--interval", type=int, default=5, help="Interval between checks in seconds (default: 5)")
    parser.add_argument("--count", type=int, default=1, help="Number of checks to perform (default: 1, 0 for infinite)")
    parser.add_argument("--json", action="store_true", help="Output in JSON format")
    
    args = parser.parse_args()
    
    if args.json:
        app = get_celery_app(args.broker)
        stats = get_worker_stats(app)
        print(json.dumps(stats, indent=2))
    else:
        monitor_workers(args.broker, args.interval, args.count)


if __name__ == "__main__":
    main()
