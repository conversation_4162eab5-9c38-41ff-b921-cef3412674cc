-- Crear el esquema app si no existe
CREATE SCHEMA IF NOT EXISTS app;

-- Crear la extensión para manejar el tenant_id
CREATE OR REPLACE FUNCTION app.set_tenant_id()
RETURNS void AS $$
BEGIN
    -- Verificar si la variable ya está configurada
    IF current_setting('app.tenant_id', true) IS NULL THEN
        -- Establecer un valor por defecto (NULL)
        PERFORM set_config('app.tenant_id', NULL, false);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Crear un trigger para establecer el tenant_id al inicio de cada transacción
CREATE OR REPLACE FUNCTION app.tenant_trigger()
RETURNS trigger AS $$
BEGIN
    -- Llamar a la función para establecer el tenant_id
    PERFORM app.set_tenant_id();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear el trigger en la base de datos
DROP TRIGGER IF EXISTS set_tenant_id_trigger ON pg_database;
CREATE TRIGGER set_tenant_id_trigger
    AFTER CONNECT ON DATABASE
    FOR EACH STATEMENT
    EXECUTE FUNCTION app.tenant_trigger();

-- Otorgar permisos necesarios
GRANT USAGE ON SCHEMA app TO PUBLIC;
GRANT EXECUTE ON FUNCTION app.set_tenant_id() TO PUBLIC;
GRANT EXECUTE ON FUNCTION app.tenant_trigger() TO PUBLIC; 