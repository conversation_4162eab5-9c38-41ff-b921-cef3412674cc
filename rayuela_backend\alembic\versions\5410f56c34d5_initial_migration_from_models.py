"""initial_migration_from_models

Revision ID: 5410f56c34d5
Revises: 
Create Date: 2025-06-04 10:49:59.621614

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5410f56c34d5'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Create necessary extensions
    op.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm;")
    op.execute("CREATE EXTENSION IF NOT EXISTS btree_gin;")
    
    op.create_table('accounts',
    sa.Column('account_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('mercadopago_customer_id', sa.String(length=255), nullable=True, comment='Mercado Pago Customer ID'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('account_id')
    )
    op.create_index(op.f('ix_accounts_mercadopago_customer_id'), 'accounts', ['mercadopago_customer_id'], unique=False)
    op.create_table('account_usage_metrics',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('api_calls_count', sa.Integer(), nullable=True),
    sa.Column('storage_used', sa.Integer(), nullable=True),
    sa.Column('endpoint_usage', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('training_count', sa.Integer(), nullable=True),
    sa.Column('recommendation_count', sa.Integer(), nullable=True),
    sa.Column('avg_response_time', sa.Float(), nullable=True),
    sa.Column('p95_response_time', sa.Float(), nullable=True),
    sa.Column('p99_response_time', sa.Float(), nullable=True),
    sa.Column('error_count', sa.Integer(), nullable=True),
    sa.Column('error_rate', sa.Float(), nullable=True),
    sa.Column('error_types', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('last_reset', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('last_billing_cycle', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('billing_period_api_calls', sa.Integer(), nullable=True),
    sa.Column('billing_period_storage', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_table('api_keys',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True, comment='Descriptive name for this API key'),
    sa.Column('api_key_hash', sa.String(length=64), nullable=False, comment='SHA-256 hash of the API key'),
    sa.Column('api_key_prefix', sa.String(length=10), nullable=False, comment='Prefix of the API key for display'),
    sa.Column('api_key_last_chars', sa.String(length=6), nullable=False, comment='Last 6 characters for display'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='Whether this API key is active'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('last_used', sa.DateTime(timezone=True), nullable=True, comment='When this API key was last used'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('api_key_hash', name='uq_api_key_hash_global')
    )
    op.create_table('artifact_metadata',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('artifact_name', sa.String(), nullable=True),
    sa.Column('artifact_version', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('training_date', sa.DateTime(), nullable=True),
    sa.Column('performance_metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('parameters', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('artifacts_path', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    sa.UniqueConstraint('account_id', 'artifact_name', 'artifact_version', name='uq_artifact_name_version'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_artifact_account_date', 'artifact_metadata', ['account_id', 'training_date'], unique=False)
    op.create_index('idx_artifact_account_name_version', 'artifact_metadata', ['account_id', 'artifact_name', 'artifact_version'], unique=False)
    op.create_index(op.f('ix_artifact_metadata_artifact_name'), 'artifact_metadata', ['artifact_name'], unique=False)
    op.create_table('audit_logs',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('action', sa.String(), nullable=False),
    sa.Column('entity_type', sa.String(), nullable=False),
    sa.Column('entity_id', sa.String(), nullable=False),
    sa.Column('changes', sa.JSON(), nullable=True),
    sa.Column('performed_by', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('details', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_audit_account_timestamp', 'audit_logs', ['account_id', 'created_at'], unique=False)
    op.create_table('batch_ingestion_jobs',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELED', name='batchingestionjobstatus'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('task_id', sa.String(), nullable=True),
    sa.Column('processed_count', sa.JSON(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.Column('data_file_path', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_batch_ingestion_job_created_at', 'batch_ingestion_jobs', ['account_id', 'created_at'], unique=False)
    op.create_index('idx_batch_ingestion_job_status', 'batch_ingestion_jobs', ['account_id', 'status'], unique=False)
    op.create_index(op.f('ix_batch_ingestion_jobs_id'), 'batch_ingestion_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_batch_ingestion_jobs_status'), 'batch_ingestion_jobs', ['status'], unique=False)
    op.create_table('end_users',
    sa.Column('user_id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('last_activity_at', sa.DateTime(timezone=True), nullable=True, comment='Timestamp of last activity by this end user'),
    sa.Column('preferred_categories', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='List of preferred product categories for cold start recommendations'),
    sa.Column('disliked_categories', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='List of disliked product categories to avoid in recommendations'),
    sa.Column('preferred_brands', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='List of preferred brands for cold start recommendations'),
    sa.Column('disliked_brands', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='List of disliked brands to avoid in recommendations'),
    sa.Column('price_range_min', sa.Integer(), nullable=True, comment='Minimum price preference for recommendations'),
    sa.Column('price_range_max', sa.Integer(), nullable=True, comment='Maximum price preference for recommendations'),
    sa.Column('demographic_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Demographic information (age_group, gender, location, etc.) for cold start'),
    sa.Column('onboarding_preferences', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Additional preferences collected during onboarding process'),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('user_id', 'account_id'),
    sa.UniqueConstraint('account_id', 'external_id', name='uq_end_user_external_id')
    )
    op.create_index('idx_end_user_preferred_categories', 'end_users', ['preferred_categories'], unique=False, postgresql_using='gin')
    op.create_index('idx_end_user_price_range', 'end_users', ['account_id', 'price_range_min', 'price_range_max'], unique=False)
    op.create_table('endpoint_metrics',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('endpoint', sa.String(), nullable=False),
    sa.Column('method', sa.String(), nullable=False),
    sa.Column('call_count', sa.Integer(), nullable=True),
    sa.Column('avg_response_time', sa.Float(), nullable=True),
    sa.Column('p95_response_time', sa.Float(), nullable=True),
    sa.Column('p99_response_time', sa.Float(), nullable=True),
    sa.Column('min_response_time', sa.Float(), nullable=True),
    sa.Column('max_response_time', sa.Float(), nullable=True),
    sa.Column('error_count', sa.Integer(), nullable=True),
    sa.Column('error_rate', sa.Float(), nullable=True),
    sa.Column('last_called_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'endpoint', 'method'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_table('notifications',
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('type', sa.Enum('INFO', 'WARNING', 'ERROR', 'SUCCESS', name='notificationtype'), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('is_read', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_notification_account', 'notifications', ['account_id'], unique=False)
    op.create_table('permissions',
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('name', sa.Enum('READ', 'WRITE', 'DELETE', 'ADMIN', 'PRODUCT_READ', 'PRODUCT_CREATE', 'PRODUCT_UPDATE', 'PRODUCT_DELETE', 'USER_READ', 'USER_CREATE', 'USER_UPDATE', 'USER_DELETE', 'SYSTEM_USER_READ', 'SYSTEM_USER_CREATE', 'SYSTEM_USER_UPDATE', 'SYSTEM_USER_DELETE', 'ROLE_READ', 'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_DELETE', 'PERMISSION_ASSIGN', 'ANALYTICS_READ', 'MODEL_READ', 'MODEL_CREATE', 'MODEL_UPDATE', 'MODEL_DELETE', 'TRAINING_JOB_READ', 'TRAINING_JOB_CREATE', 'TRAINING_JOB_UPDATE', 'TRAINING_JOB_CANCEL', name='permissiontype'), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_permission_account', 'permissions', ['account_id'], unique=False)
    op.create_index('idx_permission_name', 'permissions', ['name'], unique=False)
    op.create_table('products',
    sa.Column('product_id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('price', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('average_rating', sa.Float(), nullable=True),
    sa.Column('num_ratings', sa.Integer(), nullable=True),
    sa.Column('inventory_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('search_vector', sa.Text(), nullable=True),
    sa.Column('name_trgm', sa.Text(), nullable=True),
    sa.Column('description_trgm', sa.Text(), nullable=True),
    sa.Column('last_interaction_at', sa.DateTime(timezone=True), nullable=True, comment='Timestamp of last interaction with this product'),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('product_id', 'account_id')
    )
    op.create_index('idx_product_account_category', 'products', ['account_id', 'category'], unique=False)
    op.create_index('idx_product_account_name', 'products', ['account_id', 'name'], unique=False)
    op.create_index('idx_product_description_trgm', 'products', ['description_trgm'], unique=False, postgresql_using='gin')
    op.create_index('idx_product_name_trgm', 'products', ['name_trgm'], unique=False, postgresql_using='gin')
    op.create_index('idx_product_search_vector', 'products', ['search_vector'], unique=False, postgresql_using='gin')
    op.create_index('idx_products_account_last_interaction', 'products', ['account_id', 'last_interaction_at'], unique=False)
    op.create_table('roles',
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('name', sa.Enum('ADMIN', 'EDITOR', 'VIEWER', name='roletype'), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('account_id', 'id')
    )
    op.create_index('idx_role_account', 'roles', ['account_id'], unique=False)
    op.create_index('idx_role_name', 'roles', ['name'], unique=False)
    op.create_table('subscriptions',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('plan_type', sa.Enum('FREE', 'STARTER', 'PRO', 'ENTERPRISE', name='subscriptionplan'), nullable=False),
    sa.Column('api_calls_limit', sa.Integer(), nullable=True),
    sa.Column('storage_limit', sa.Integer(), nullable=True),
    sa.Column('training_frequency', sa.Interval(), nullable=True),
    sa.Column('mercadopago_subscription_id', sa.String(length=255), nullable=True),
    sa.Column('mercadopago_price_id', sa.String(length=255), nullable=True),
    sa.Column('payment_gateway', sa.String(length=20), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('monthly_api_calls_used', sa.Integer(), nullable=False),
    sa.Column('storage_used', sa.BigInteger(), nullable=False),
    sa.Column('last_reset_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('available_models', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('last_successful_training_at', sa.DateTime(timezone=True), nullable=True, comment='Fecha del último entrenamiento exitoso'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index(op.f('ix_subscriptions_mercadopago_subscription_id'), 'subscriptions', ['mercadopago_subscription_id'], unique=False)
    op.create_index(op.f('ix_subscriptions_plan_type'), 'subscriptions', ['plan_type'], unique=False)
    op.create_table('system_users',
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_admin', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True, comment='Timestamp of last login by this user'),
    sa.Column('email_verified', sa.Boolean(), nullable=True),
    sa.Column('verification_token', sa.String(length=255), nullable=True),
    sa.Column('verification_token_expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    comment='Emails should be globally unique across all accounts. This is enforced at the application level.'
    )
    op.create_index('idx_system_user_account', 'system_users', ['account_id'], unique=False)
    op.create_index('idx_system_user_email', 'system_users', ['account_id', 'email'], unique=True)
    op.create_index('idx_system_users_account_last_login', 'system_users', ['account_id', 'last_login_at'], unique=False)
    op.create_table('interactions',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('interaction_type', sa.Enum('VIEW', 'LIKE', 'PURCHASE', 'CART', 'RATING', 'WISHLIST', 'CLICK', 'SEARCH', 'FAVORITE', name='interactiontype'), nullable=False),
    sa.Column('value', sa.Float(), nullable=True),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('recommendation_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['account_id', 'product_id'], ['products.account_id', 'products.product_id'], name='fk_interaction_product', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id', 'user_id'], ['end_users.account_id', 'end_users.user_id'], name='fk_interaction_user', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_interaction_account_product', 'interactions', ['account_id', 'product_id'], unique=False)
    op.create_index('idx_interaction_account_timestamp', 'interactions', ['account_id', 'timestamp'], unique=False)
    op.create_index('idx_interaction_account_user', 'interactions', ['account_id', 'user_id'], unique=False)
    op.create_table('model_metrics',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('model_metadata_id', sa.Integer(), nullable=False),
    sa.Column('metric_name', sa.String(length=100), nullable=False),
    sa.Column('metric_value', sa.Float(), nullable=False),
    sa.Column('metric_type', sa.String(length=50), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('details', sa.String(), nullable=True),
    sa.Column('ndcg', sa.Float(), nullable=True),
    sa.Column('diversity', sa.Float(), nullable=True),
    sa.Column('novelty', sa.Float(), nullable=True),
    sa.Column('coverage', sa.Float(), nullable=True),
    sa.Column('latency_ms', sa.Float(), nullable=True),
    sa.Column('throughput', sa.Float(), nullable=True),
    sa.Column('additional_metrics', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['account_id', 'model_metadata_id'], ['artifact_metadata.account_id', 'artifact_metadata.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_metric_model', 'model_metrics', ['account_id', 'model_metadata_id'], unique=False)
    op.create_index('idx_metric_name_type', 'model_metrics', ['account_id', 'metric_name', 'metric_type'], unique=False)
    op.create_index('idx_metric_timestamp', 'model_metrics', ['timestamp'], unique=False)
    op.create_index(op.f('ix_model_metrics_metric_name'), 'model_metrics', ['metric_name'], unique=False)
    op.create_table('orders',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_number', sa.String(length=50), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELED', 'REFUNDED', name='orderstatus'), nullable=False),
    sa.Column('total_amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('shipping_address', sa.Text(), nullable=True),
    sa.Column('billing_address', sa.Text(), nullable=True),
    sa.Column('order_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('confirmed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('shipped_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('canceled_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('tracking_number', sa.String(length=100), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.ForeignKeyConstraint(['user_id', 'account_id'], ['end_users.user_id', 'end_users.account_id'], name='fk_order_end_user', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    sa.UniqueConstraint('account_id', 'order_number', name='uq_order_account_number')
    )
    op.create_index('idx_order_account_date', 'orders', ['account_id', 'order_date'], unique=False)
    op.create_index('idx_order_account_status', 'orders', ['account_id', 'status'], unique=False)
    op.create_index(op.f('ix_orders_order_date'), 'orders', ['order_date'], unique=False)
    op.create_index(op.f('ix_orders_status'), 'orders', ['status'], unique=False)
    op.create_index(op.f('ix_orders_user_id'), 'orders', ['user_id'], unique=False)
    op.create_table('recommendations',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('score', sa.Float(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['account_id', 'product_id'], ['products.account_id', 'products.product_id'], name='fk_recommendation_product', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id', 'user_id'], ['end_users.account_id', 'end_users.user_id'], name='fk_recommendation_user', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_recommendation_created_at', 'recommendations', ['created_at'], unique=False)
    op.create_index('idx_recommendation_product', 'recommendations', ['account_id', 'product_id'], unique=False)
    op.create_index('idx_recommendation_user', 'recommendations', ['account_id', 'user_id'], unique=False)
    op.create_table('role_permissions',
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('permission_id', sa.Integer(), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id', 'permission_id'], ['permissions.account_id', 'permissions.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id', 'role_id'], ['roles.account_id', 'roles.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('role_id', 'permission_id', 'account_id')
    )
    op.create_table('searches',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('query', sa.Text(), nullable=True),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['account_id', 'user_id'], ['end_users.account_id', 'end_users.user_id'], name='fk_search_user', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_search_account_timestamp', 'searches', ['account_id', 'timestamp'], unique=False)
    op.create_index('idx_search_account_user', 'searches', ['account_id', 'user_id'], unique=False)
    op.create_table('system_user_roles',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('system_user_id', sa.Integer(), nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['account_id', 'role_id'], ['roles.account_id', 'roles.id'], name='fk_system_user_role_role', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id', 'system_user_id'], ['system_users.account_id', 'system_users.id'], name='fk_system_user_role_system_user', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'system_user_id', 'role_id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_system_user_role_role', 'system_user_roles', ['account_id', 'role_id'], unique=False)
    op.create_index('idx_system_user_role_user', 'system_user_roles', ['account_id', 'system_user_id'], unique=False)
    op.create_table('training_jobs',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('artifact_metadata_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', name='trainingjobstatus'), nullable=True),
    sa.Column('started_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('task_id', sa.String(length=255), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.Column('metrics', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['account_id', 'artifact_metadata_id'], ['artifact_metadata.account_id', 'artifact_metadata.id'], name='fk_training_job_artifact_metadata', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_job_account_metadata', 'training_jobs', ['account_id', 'artifact_metadata_id'], unique=False)
    op.create_index('idx_job_account_status', 'training_jobs', ['account_id', 'status'], unique=False)
    op.create_index('idx_training_jobs_task_id', 'training_jobs', ['task_id'], unique=False)
    op.create_index(op.f('ix_training_jobs_task_id'), 'training_jobs', ['task_id'], unique=False)
    op.create_table('training_metrics',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), sa.Identity(always=False), nullable=False),
    sa.Column('model_id', sa.Integer(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('accuracy', sa.Float(), nullable=True),
    sa.Column('precision', sa.Float(), nullable=True),
    sa.Column('recall', sa.Float(), nullable=True),
    sa.Column('f1', sa.Float(), nullable=True),
    sa.Column('training_time', sa.Float(), nullable=True),
    sa.Column('model_size_mb', sa.Float(), nullable=True),
    sa.Column('additional_metrics', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['account_id', 'model_id'], ['artifact_metadata.account_id', 'artifact_metadata.id'], name='fk_training_metrics_model', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.PrimaryKeyConstraint('account_id', 'id'),
    postgresql_partition_by='RANGE (account_id)'
    )
    op.create_index('idx_training_metrics_model', 'training_metrics', ['account_id', 'model_id'], unique=False)
    op.create_index('idx_training_metrics_timestamp', 'training_metrics', ['timestamp'], unique=False)
    op.create_table('order_items',
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('unit_price', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('total_price', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['account_id', 'order_id'], ['orders.account_id', 'orders.id'], name='fk_order_item_order', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ),
    sa.ForeignKeyConstraint(['product_id', 'account_id'], ['products.product_id', 'products.account_id'], name='fk_order_item_product', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('account_id', 'id')
    )
    op.create_index('idx_order_item_account_order', 'order_items', ['account_id', 'order_id'], unique=False)
    op.create_index('idx_order_item_account_product', 'order_items', ['account_id', 'product_id'], unique=False)
    op.create_index(op.f('ix_order_items_order_id'), 'order_items', ['order_id'], unique=False)
    op.create_index(op.f('ix_order_items_product_id'), 'order_items', ['product_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_order_items_product_id'), table_name='order_items')
    op.drop_index(op.f('ix_order_items_order_id'), table_name='order_items')
    op.drop_index('idx_order_item_account_product', table_name='order_items')
    op.drop_index('idx_order_item_account_order', table_name='order_items')
    op.drop_table('order_items')
    op.drop_index('idx_training_metrics_timestamp', table_name='training_metrics')
    op.drop_index('idx_training_metrics_model', table_name='training_metrics')
    op.drop_table('training_metrics')
    op.drop_index(op.f('ix_training_jobs_task_id'), table_name='training_jobs')
    op.drop_index('idx_training_jobs_task_id', table_name='training_jobs')
    op.drop_index('idx_job_account_status', table_name='training_jobs')
    op.drop_index('idx_job_account_metadata', table_name='training_jobs')
    op.drop_table('training_jobs')
    op.drop_index('idx_system_user_role_user', table_name='system_user_roles')
    op.drop_index('idx_system_user_role_role', table_name='system_user_roles')
    op.drop_table('system_user_roles')
    op.drop_index('idx_search_account_user', table_name='searches')
    op.drop_index('idx_search_account_timestamp', table_name='searches')
    op.drop_table('searches')
    op.drop_table('role_permissions')
    op.drop_index('idx_recommendation_user', table_name='recommendations')
    op.drop_index('idx_recommendation_product', table_name='recommendations')
    op.drop_index('idx_recommendation_created_at', table_name='recommendations')
    op.drop_table('recommendations')
    op.drop_index(op.f('ix_orders_user_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_status'), table_name='orders')
    op.drop_index(op.f('ix_orders_order_date'), table_name='orders')
    op.drop_index('idx_order_account_status', table_name='orders')
    op.drop_index('idx_order_account_date', table_name='orders')
    op.drop_table('orders')
    op.drop_index(op.f('ix_model_metrics_metric_name'), table_name='model_metrics')
    op.drop_index('idx_metric_timestamp', table_name='model_metrics')
    op.drop_index('idx_metric_name_type', table_name='model_metrics')
    op.drop_index('idx_metric_model', table_name='model_metrics')
    op.drop_table('model_metrics')
    op.drop_index('idx_interaction_account_user', table_name='interactions')
    op.drop_index('idx_interaction_account_timestamp', table_name='interactions')
    op.drop_index('idx_interaction_account_product', table_name='interactions')
    op.drop_table('interactions')
    op.drop_index('idx_system_users_account_last_login', table_name='system_users')
    op.drop_index('idx_system_user_email', table_name='system_users')
    op.drop_index('idx_system_user_account', table_name='system_users')
    op.drop_table('system_users')
    op.drop_index(op.f('ix_subscriptions_plan_type'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_mercadopago_subscription_id'), table_name='subscriptions')
    op.drop_table('subscriptions')
    op.drop_index('idx_role_name', table_name='roles')
    op.drop_index('idx_role_account', table_name='roles')
    op.drop_table('roles')
    op.drop_index('idx_products_account_last_interaction', table_name='products')
    op.drop_index('idx_product_search_vector', table_name='products', postgresql_using='gin')
    op.drop_index('idx_product_name_trgm', table_name='products', postgresql_using='gin')
    op.drop_index('idx_product_description_trgm', table_name='products', postgresql_using='gin')
    op.drop_index('idx_product_account_name', table_name='products')
    op.drop_index('idx_product_account_category', table_name='products')
    op.drop_table('products')
    op.drop_index('idx_permission_name', table_name='permissions')
    op.drop_index('idx_permission_account', table_name='permissions')
    op.drop_table('permissions')
    op.drop_index('idx_notification_account', table_name='notifications')
    op.drop_table('notifications')
    op.drop_table('endpoint_metrics')
    op.drop_index('idx_end_user_price_range', table_name='end_users')
    op.drop_index('idx_end_user_preferred_categories', table_name='end_users', postgresql_using='gin')
    op.drop_table('end_users')
    op.drop_index(op.f('ix_batch_ingestion_jobs_status'), table_name='batch_ingestion_jobs')
    op.drop_index(op.f('ix_batch_ingestion_jobs_id'), table_name='batch_ingestion_jobs')
    op.drop_index('idx_batch_ingestion_job_status', table_name='batch_ingestion_jobs')
    op.drop_index('idx_batch_ingestion_job_created_at', table_name='batch_ingestion_jobs')
    op.drop_table('batch_ingestion_jobs')
    op.drop_index('idx_audit_account_timestamp', table_name='audit_logs')
    op.drop_table('audit_logs')
    op.drop_index(op.f('ix_artifact_metadata_artifact_name'), table_name='artifact_metadata')
    op.drop_index('idx_artifact_account_name_version', table_name='artifact_metadata')
    op.drop_index('idx_artifact_account_date', table_name='artifact_metadata')
    op.drop_table('artifact_metadata')
    op.drop_table('api_keys')
    op.drop_table('account_usage_metrics')
    op.drop_index(op.f('ix_accounts_mercadopago_customer_id'), table_name='accounts')
    op.drop_table('accounts')
    
    # Drop extensions (only if not in use by other objects)
    op.execute("DROP EXTENSION IF EXISTS btree_gin CASCADE;")
    op.execute("DROP EXTENSION IF EXISTS pg_trgm CASCADE;")
    # ### end Alembic commands ###
