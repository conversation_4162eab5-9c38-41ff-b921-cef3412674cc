"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  getMe,
  loginUser,
  registerUser,
  ApiError,
  logout as logoutApi,
  requestEmailVerification
} from './api';
import { toast } from "sonner";
import InitialApiKeyModal from '@/components/auth/InitialApiKeyModal';

// Interfaz para los datos del usuario que obtendremos de /system-users/me
interface User {
  id: number;
  email: string;
  account_id: number;
  is_admin: boolean;
  is_active: boolean;
}

interface AuthContextType {
  user: User | null;
  token: string | null; // Token JWT del SystemUser
  apiKey: string | null; // API Key de la cuenta (para identificar tenant)
  setApiKey: (apiKey: string) => void; // Función para actualizar la API Key
  login: (email: string, password: string) => Promise<boolean>; // Devuelve true/false para éxito/fallo
  register: (accountName: string, email: string, password: string) => Promise<any>; // Registra un nuevo usuario
  logout: () => void;
  isLoading: boolean; // Estado de carga inicial y durante login/fetch
  emailVerificationError: {
    email: string;
    password: string;
    message: string;
  } | null; // Error de verificación de email
  requestNewVerificationEmail: () => Promise<boolean>; // Función para solicitar un nuevo email de verificación
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// --- AuthProvider ---
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [apiKey, setApiKey] = useState<string | null>(null); // API Key de la cuenta
  const [isLoading, setIsLoading] = useState(true); // Carga inicial
  const [showApiKeyModal, setShowApiKeyModal] = useState(false); // Estado para el modal
  const [initialApiKeyToShow, setInitialApiKeyToShow] = useState<string | null>(null); // Key a mostrar
  const router = useRouter();
  const pathname = usePathname();

  // Estado para manejar el error de email no verificado
  const [emailVerificationError, setEmailVerificationError] = useState<{
    email: string;
    password: string;
    message: string;
  } | null>(null);

  // Función para limpiar el estado y localStorage
  const clearAuthData = useCallback(() => {
    localStorage.removeItem('rayuela-token');
    localStorage.removeItem('rayuela-apiKey');
    setUser(null);
    setToken(null);
    setApiKey(null);
  }, []);

  // Función para validar token y obtener datos del usuario
  const fetchUserData = useCallback(async (currentToken: string, currentApiKey?: string | null) => {
    setIsLoading(true);
    try {
      // Llama a /system-users/me usando el token (y opcionalmente la API Key)
      const userData = await getMe(currentToken, currentApiKey);

      if (!userData.is_active) {
        throw new Error("User account is inactive.");
      }

      setUser(userData);
      setToken(currentToken);
      if (currentApiKey) {
        setApiKey(currentApiKey);
      }
      console.log("User data fetched successfully:", userData);
      return true; // Indica éxito

    } catch (error: any) {
      console.error("Token validation/fetch user data failed:", error);
      clearAuthData(); // Limpiar datos si el token es inválido o hay error
      // Si estamos en una ruta protegida y falla la validación, redirigir a login
      if (pathname?.startsWith('/dashboard')) { // Ajusta '/dashboard' si tu ruta es diferente
        toast.error("Tu sesión ha expirado o es inválida. Por favor, inicia sesión de nuevo.");
        router.push('/login');
      }
      return false; // Indica fallo
    } finally {
      setIsLoading(false);
    }
  }, [router, pathname, clearAuthData]);

  // Efecto para cargar estado inicial desde localStorage y validar
  useEffect(() => {
    console.log("AuthProvider Mounted. Checking localStorage...");
    const storedToken = localStorage.getItem('rayuela-token');
    const storedApiKey = localStorage.getItem('rayuela-apiKey');
    if (storedToken && storedApiKey) {
      console.log("Found token and apiKey in localStorage. Validating...");
      fetchUserData(storedToken, storedApiKey);
    } else {
      console.log("No token or apiKey found in localStorage.");
      setIsLoading(false); // No hay token, terminamos la carga inicial
    }
  }, [fetchUserData]);

  // Función para solicitar un nuevo email de verificación
  const requestNewVerificationEmail = useCallback(async () => {
    if (!token) {
      toast.error("No hay sesión activa. Por favor, inicia sesión de nuevo.");
      return false;
    }

    try {
      const response = await requestEmailVerification(token);
      toast.success("Email de verificación enviado. Por favor, revisa tu bandeja de entrada.");
      return true;
    } catch (error) {
      console.error("Error al solicitar email de verificación:", error);
      if (ApiError.isApiError(error)) {
        toast.error(error.message || "Error al solicitar email de verificación.");
      } else {
        toast.error("Error inesperado al solicitar email de verificación.");
      }
      return false;
    }
  }, [token]);

  // Función de Login
  const login = useCallback(async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    // Limpiar cualquier error previo de verificación de email
    setEmailVerificationError(null);

    try {
      // Preparar los datos para la API
      const credentials = { email, password };

      // Llamar directamente a la API de login
      const response = await loginUser(credentials);

      if (response.access_token) {
        // Guardar token en localStorage
        localStorage.setItem('rayuela-token', response.access_token);
        setToken(response.access_token);

        // El endpoint /auth/token solo devuelve JWT, no API Key
        // Intentar cargar API Key existente de localStorage
        const storedApiKey = localStorage.getItem('rayuela-apiKey');
        if (storedApiKey) {
          setApiKey(storedApiKey);
        }

        // Validar el token y obtener datos del usuario (con o sin API Key)
        const fetchSuccess = await fetchUserData(response.access_token, storedApiKey);

        if (fetchSuccess) {
          toast.success("Login exitoso!");
          router.push('/dashboard'); // Redirigir al dashboard
          return true; // Login exitoso
        } else {
          // fetchUserData ya manejó el error y limpió el estado
          return false; // Login fallido (validación post-login falló)
        }
      } else {
        throw new Error("No se recibió token de acceso.");
      }
    } catch (error: any) {
      console.error("Login processing failed:", error);

      // Verificar si es un error de email no verificado
      if (error.error_code === "EMAIL_NOT_VERIFIED") {
        // Guardar la información para poder reenviar el email de verificación
        setEmailVerificationError({
          email,
          password,
          message: error.message || "Por favor, verifica tu email para continuar."
        });

        // No limpiar los datos de autenticación, ya que necesitamos el token para solicitar un nuevo email
        setIsLoading(false);
        return false;
      }

      // Para otros errores, mostrar mensaje y limpiar datos
      toast.error(error.message || "Error al iniciar sesión. Verifica tus credenciales.");
      clearAuthData(); // Asegurarse de limpiar en caso de error
      setIsLoading(false); // Terminar carga en caso de error
      return false; // Login fallido
    }
  }, [fetchUserData, router, clearAuthData]);

  // Función de registro
  const register = useCallback(async (accountName: string, email: string, password: string) => {
    setIsLoading(true);

    try {
      // Llamar a la API de registro
      const response = await registerUser(accountName, email, password);

      if (response.access_token) {
        // Guardar token y API Key en localStorage
        localStorage.setItem('rayuela-token', response.access_token);
        localStorage.setItem('rayuela-apiKey', response.api_key);

        // Actualizar el estado
        setToken(response.access_token);
        setApiKey(response.api_key);

        // Validar el token y obtener datos del usuario
        await fetchUserData(response.access_token, response.api_key);

        // Retornar la API Key para mostrarla en el modal
        return {
          success: true,
          apiKey: response.api_key
        };
      } else {
        throw new Error("No se recibió token de acceso ni API Key");
      }
    } catch (error: any) {
      console.error("Register processing failed:", error);

      // Mostrar mensaje de error
      if (error instanceof ApiError) {
        toast.error(error.message || "Error al registrarse");
      } else {
        toast.error("Error inesperado al registrarse");
      }

      // Limpiar datos
      clearAuthData();

      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  }, [fetchUserData, clearAuthData]);

  // Función para manejar el cierre del modal de API Key
  const handleModalClose = useCallback(async () => {
    // Guardar los valores actuales para evitar problemas de concurrencia
    const currentToken = token;
    const currentApiKey = apiKey;

    // Ocultar el modal inmediatamente para mejorar UX
    setShowApiKeyModal(false);
    setInitialApiKeyToShow(null);

    // Mostrar toast de carga
    const loadingToast = toast.loading("Configurando tu cuenta...");
    setIsLoading(true);

    try {
      // Validar que tenemos los datos necesarios
      if (!currentToken || !currentApiKey) {
        throw new ApiError(
          "Token o API Key no disponibles",
          401,
          "AUTH_REQUIRED",
          null
        );
      }

      // Intentar obtener los datos del usuario con reintentos
      let fetchSuccess = false;
      let attempts = 0;
      const maxAttempts = 3;
      let lastError: ApiError | null = null;

      while (!fetchSuccess && attempts < maxAttempts) {
        try {
          await fetchUserData(currentToken, currentApiKey);
          fetchSuccess = true;
        } catch (e) {
          lastError = e as ApiError;
          attempts++;
          if (attempts < maxAttempts) {
            // Esperar 500ms antes de reintentar
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      if (!fetchSuccess && lastError) {
        throw lastError;
      }

      // Si llegamos aquí, es que todo salió bien
      toast.dismiss(loadingToast);
      toast.success("¡Cuenta configurada correctamente!");
      router.push('/dashboard');

    } catch (error) {
      toast.dismiss(loadingToast);
      if (error instanceof ApiError) {
        toast.error(error.message || "Error al inicializar la cuenta");
      } else {
        toast.error("Error inesperado al configurar la cuenta");
      }
      // No hacer logout aquí, dejar que el usuario intente de nuevo
    } finally {
      setIsLoading(false);
    }
  }, [token, apiKey, fetchUserData, router]);

  // Función Logout
  const logout = useCallback(async () => {
    try {
      // Solo llamar a la API si hay token
      if (token) {
        await logoutApi(token);
      }

      // Limpiar estado y localStorage
      clearAuthData();

      // Redirigir al login
      router.push('/login');
      toast.success("Sesión cerrada correctamente");
    } catch (error) {
      console.error("Logout error:", error);
      // Limpiar de todas formas
      clearAuthData();
      router.push('/login');
    }
  }, [token, clearAuthData, router]);

  // Renderizado del proveedor de contexto
  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        apiKey,
        setApiKey,
        login,
        register,
        logout,
        isLoading,
        emailVerificationError,
        requestNewVerificationEmail,
      }}
    >
      {children}

      {/* Modal para mostrar la API Key inicial */}
      {showApiKeyModal && initialApiKeyToShow && (
        <InitialApiKeyModal
          apiKey={initialApiKeyToShow}
          onClose={handleModalClose}
        />
      )}
    </AuthContext.Provider>
  );
};

// Hook para usar el contexto de autenticación
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 