#!/usr/bin/env python
"""
Script para ejecutar todos los tipos de tests de Rayuela localmente.
Este script permite ejecutar tests unitarios, de integración y de carga.
"""

import os
import sys
import click
import subprocess
import time
import asyncio
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

# Importar configuración
from src.core.config import settings


@click.group()
def cli():
    """Comandos para ejecutar tests del sistema de recomendación Rayuela"""
    pass


@cli.command()
@click.option("--coverage", is_flag=True, help="Ejecutar con cobertura de código")
@click.option("--verbose", "-v", is_flag=True, help="Mostrar salida detallada")
@click.option("--pattern", "-p", default=None, help="Patrón para filtrar tests (ej: 'test_api_key')")
def unit(coverage, verbose, pattern):
    """Ejecutar tests unitarios"""
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["pytest", "tests/unit"]

    if verbose:
        cmd.append("-v")

    if pattern:
        cmd.append(f"-k {pattern}")

    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=term",
            "--cov-report=html",
        ])

    click.echo("Ejecutando tests unitarios...")
    result = subprocess.run(cmd, env=env)
    sys.exit(result.returncode)


@cli.command()
@click.option("--coverage", is_flag=True, help="Ejecutar con cobertura de código")
@click.option("--verbose", "-v", is_flag=True, help="Mostrar salida detallada")
@click.option("--pattern", "-p", default=None, help="Patrón para filtrar tests (ej: 'test_multi_tenancy')")
@click.option("--setup-db", is_flag=True, help="Configurar base de datos de prueba antes de ejecutar tests")
def integration(coverage, verbose, pattern, setup_db):
    """Ejecutar tests de integración"""
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    if setup_db:
        click.echo("Configurando base de datos de prueba...")
        setup_test_database()

    cmd = ["pytest", "tests/integration"]

    if verbose:
        cmd.append("-v")

    if pattern:
        cmd.append(f"-k {pattern}")

    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=term",
            "--cov-report=html",
        ])

    click.echo("Ejecutando tests de integración...")
    result = subprocess.run(cmd, env=env)
    sys.exit(result.returncode)


@cli.command()
@click.option("--host", default="localhost", help="Host para el servidor")
@click.option("--port", default=8001, help="Puerto para el servidor")
@click.option("--users", default=10, help="Número de usuarios concurrentes")
@click.option("--spawn-rate", default=1, help="Tasa de creación de usuarios por segundo")
@click.option("--time", default=60, help="Tiempo de ejecución en segundos")
@click.option("--tags", default=None, help="Tags para filtrar tests (separados por comas)")
@click.option("--headless", is_flag=True, help="Ejecutar en modo headless (sin interfaz web)")
def load(host, port, users, spawn_rate, time, tags, headless):
    """Ejecutar tests de carga con Locust"""
    # Configurar el entorno para que locust pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = [
        "locust",
        "-f",
        "tests/load/locustfile.py",
        "--host",
        f"http://{host}:{port}",
    ]

    if headless:
        cmd.extend([
            "--headless",
            "-u", str(users),
            "-r", str(spawn_rate),
            "-t", f"{time}s",
            "--html", "load_test_report.html",
        ])

    if tags:
        cmd.extend(["--tags", tags])

    click.echo(f"Ejecutando tests de carga contra {host}:{port}...")
    result = subprocess.run(cmd, env=env)

    if headless:
        click.echo(f"Reporte generado en load_test_report.html")

    sys.exit(result.returncode)


@cli.command()
@click.option("--coverage", is_flag=True, help="Ejecutar con cobertura de código")
@click.option("--verbose", "-v", is_flag=True, help="Mostrar salida detallada")
@click.option("--setup-db", is_flag=True, help="Configurar base de datos de prueba antes de ejecutar tests")
def all(coverage, verbose, setup_db):
    """Ejecutar todos los tests (unitarios e integración)"""
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    if setup_db:
        click.echo("Configurando base de datos de prueba...")
        setup_test_database()

    cmd = ["pytest", "tests/unit", "tests/integration"]

    if verbose:
        cmd.append("-v")

    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=term",
            "--cov-report=html",
        ])

    click.echo("Ejecutando todos los tests...")
    result = subprocess.run(cmd, env=env)
    sys.exit(result.returncode)


@cli.command()
@click.option("--verbose", "-v", is_flag=True, help="Mostrar salida detallada")
def e2e(verbose):
    """Ejecutar tests end-to-end"""
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["pytest", "tests/e2e"]

    if verbose:
        cmd.append("-v")

    click.echo("Ejecutando tests end-to-end...")
    result = subprocess.run(cmd, env=env)
    sys.exit(result.returncode)


def setup_test_database():
    """Configurar base de datos de prueba"""
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    # Crear base de datos de prueba si no existe
    try:
        # Usar el script simplificado para crear la base de datos
        subprocess.run([
            "python", "-m", "scripts.init_db_simple",
            "--db-name", "rayuela_test"
        ], check=True, env=env)

        click.echo("Base de datos de prueba configurada correctamente.")
    except subprocess.CalledProcessError as e:
        click.echo(f"Error al configurar la base de datos de prueba: {e}")
        sys.exit(1)


if __name__ == "__main__":
    cli()
