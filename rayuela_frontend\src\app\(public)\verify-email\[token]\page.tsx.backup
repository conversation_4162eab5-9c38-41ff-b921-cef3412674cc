"use client";

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { verifyEmail } from '@/lib/api';
import { toast } from 'sonner';
import { CheckCircle2Icon, XCircleIcon, Loader2Icon } from 'lucide-react';
import Link from 'next/link';

export default function VerifyEmailPage() {
  const params = useParams();
  const router = useRouter();
  const [isVerifying, setIsVerifying] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const verifyUserEmail = async () => {
      if (!params.token) {
        setIsVerifying(false);
        setErrorMessage("Token de verificación no proporcionado.");
        return;
      }

      try {
        const token = Array.isArray(params.token) ? params.token[0] : params.token;
        const response = await verifyEmail(token);
        setIsSuccess(true);
        toast.success("Email verificado correctamente.");
      } catch (error: any) {
        console.error("Error al verificar email:", error);
        setErrorMessage(error.message || "Error al verificar el email. El token podría ser inválido o haber expirado.");
        toast.error("Error al verificar el email.");
      } finally {
        setIsVerifying(false);
      }
    };

    verifyUserEmail();
  }, [params.token]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Verificación de Email</CardTitle>
          <CardDescription>
            {isVerifying
              ? "Verificando tu dirección de email..."
              : isSuccess
                ? "Tu email ha sido verificado correctamente."
                : "Error en la verificación de email."}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-6">
          {isVerifying ? (
            <div className="flex flex-col items-center">
              <Loader2Icon className="h-16 w-16 text-blue-500 animate-spin mb-4" />
              <p className="text-gray-600">Estamos verificando tu email...</p>
            </div>
          ) : isSuccess ? (
            <div className="flex flex-col items-center">
              <CheckCircle2Icon className="h-16 w-16 text-green-500 mb-4" />
              <p className="text-gray-600 text-center">
                Tu email ha sido verificado correctamente. Ahora puedes iniciar sesión en tu cuenta.
              </p>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <XCircleIcon className="h-16 w-16 text-red-500 mb-4" />
              <p className="text-gray-600 text-center">
                {errorMessage || "Ha ocurrido un error al verificar tu email."}
              </p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          {!isVerifying && (
            <Button
              onClick={() => router.push('/login')}
              className="w-full max-w-xs"
            >
              {isSuccess ? "Ir a iniciar sesión" : "Volver a intentar"}
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
