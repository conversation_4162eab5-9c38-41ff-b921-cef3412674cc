#!/bin/bash

# Script para resolver la generación de OpenAPI en Rayuela
# Diagnostica problemas y regenera la especificación OpenAPI completa

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Solucionador de OpenAPI para Rayuela${NC}"
echo "==========================================="
echo ""

# Function to check if backend is running
check_backend_status() {
    echo -e "${BLUE}1. Verificando estado del backend...${NC}"
    
    # Kill any existing backend processes
    echo "🛑 Deteniendo procesos existentes..."
    pkill -f "main.py" 2>/dev/null || true
    pkill -f "uvicorn" 2>/dev/null || true
    sleep 2
    
    # Check if port 8000 is available
    if netstat -tuln | grep ":8000 " >/dev/null; then
        echo -e "${YELLOW}⚠️ Puerto 8000 aún en uso, esperando...${NC}"
        sleep 5
    fi
    
    echo -e "${GREEN}✅ Backend preparado para iniciarse${NC}"
    echo ""
}

# Function to start backend and verify OpenAPI
start_backend_and_verify() {
    echo -e "${BLUE}2. Iniciando backend y verificando OpenAPI...${NC}"
    
    # Navigate to backend directory
    cd rayuela_backend
    
    # Activate virtual environment if it exists
    if [ -f "../.venv/bin/activate" ]; then
        source ../.venv/bin/activate
        echo "📦 Entorno virtual activado"
    fi
    
    # Start backend in background
    echo "🚀 Iniciando backend..."
    python main.py &
    BACKEND_PID=$!
    echo "📋 Backend PID: $BACKEND_PID"
    
    # Wait for backend to start
    echo "⏳ Esperando que el backend esté listo..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Backend está corriendo${NC}"
            break
        fi
        echo "⏳ Intento $i/30..."
        sleep 2
        
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ Timeout esperando el backend${NC}"
            kill $BACKEND_PID 2>/dev/null || true
            exit 1
        fi
    done
    
    # Test OpenAPI endpoint
    echo "🔍 Verificando endpoint OpenAPI..."
    OPENAPI_RESPONSE=$(curl -s http://localhost:8000/api/openapi.json)
    OPENAPI_SIZE=${#OPENAPI_RESPONSE}
    
    echo "📊 Tamaño de respuesta OpenAPI: $OPENAPI_SIZE caracteres"
    
    if [ $OPENAPI_SIZE -lt 100 ]; then
        echo -e "${RED}❌ OpenAPI está vacío o muy pequeño${NC}"
        echo "📋 Respuesta recibida:"
        echo "$OPENAPI_RESPONSE" | head -10
        
        # Save current response for debugging
        echo "$OPENAPI_RESPONSE" > /tmp/openapi_debug.json
        echo "💾 Respuesta guardada en /tmp/openapi_debug.json"
        
        kill $BACKEND_PID 2>/dev/null || true
        return 1
    else
        echo -e "${GREEN}✅ OpenAPI parece estar funcionando${NC}"
        
        # Check for paths
        PATHS_COUNT=$(echo "$OPENAPI_RESPONSE" | jq '.paths | length' 2>/dev/null || echo "0")
        echo "📋 Número de paths encontrados: $PATHS_COUNT"
        
        if [ "$PATHS_COUNT" -gt "0" ]; then
            echo -e "${GREEN}✅ OpenAPI contiene paths!${NC}"
            
            # Save the working OpenAPI spec
            echo "$OPENAPI_RESPONSE" > ../rayuela_frontend/src/lib/openapi/openapi.json
            echo -e "${GREEN}✅ OpenAPI actualizado en frontend${NC}"
        else
            echo -e "${YELLOW}⚠️ OpenAPI no contiene paths${NC}"
        fi
        
        kill $BACKEND_PID 2>/dev/null || true
        return 0
    fi
}

# Function to diagnose backend configuration
diagnose_backend_config() {
    echo -e "${BLUE}3. Diagnosticando configuración del backend...${NC}"
    
    cd rayuela_backend
    
    # Check FastAPI app configuration
    echo "🔍 Verificando configuración de FastAPI..."
    python -c "
from main import app
print(f'Title: {app.title}')
print(f'Version: {app.version}')
print(f'OpenAPI URL: {app.openapi_url}')
print(f'Docs URL: {app.docs_url}')

# Count routes
route_count = len(app.routes)
print(f'Total routes: {route_count}')

# Check if routers are included
for route in app.routes:
    if hasattr(route, 'path'):
        print(f'Route: {route.path} - {getattr(route, \"methods\", [])}')
"
    
    echo ""
    echo "🔍 Verificando rutas de API..."
    python -c "
from src.api.v1.api import public_router, private_router
print(f'Public router routes: {len(public_router.routes)}')
print(f'Private router routes: {len(private_router.routes)}')

print('\\nPublic routes:')
for route in public_router.routes:
    if hasattr(route, 'path'):
        print(f'  {route.path} - {getattr(route, \"methods\", [])}')

print('\\nPrivate routes:')
for route in private_router.routes:
    if hasattr(route, 'path'):
        print(f'  {route.path} - {getattr(route, \"methods\", [])}')
" || echo "❌ Error verificando rutas"
    
    cd ..
}

# Function to regenerate frontend API client
regenerate_frontend_client() {
    echo -e "${BLUE}4. Regenerando cliente API del frontend...${NC}"
    
    cd rayuela_frontend
    
    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm no está disponible${NC}"
        return 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Instalando dependencias de npm..."
        npm install
    fi
    
    # Set backend URL for fetching
    export NEXT_PUBLIC_API_URL="http://localhost:8000"
    
    # Fetch OpenAPI spec
    echo "📥 Obteniendo especificación OpenAPI..."
    if npm run fetch-openapi; then
        echo -e "${GREEN}✅ OpenAPI obtenido exitosamente${NC}"
    else
        echo -e "${YELLOW}⚠️ Error obteniendo OpenAPI, usando archivo actual${NC}"
    fi
    
    # Generate API client
    echo "🔄 Generando cliente API..."
    if npm run generate-api; then
        echo -e "${GREEN}✅ Cliente API generado exitosamente${NC}"
        
        # Check generated files
        if [ -f "src/lib/generated/rayuelaAPI.ts" ]; then
            API_SIZE=$(wc -l < src/lib/generated/rayuelaAPI.ts)
            echo "📊 Tamaño del cliente generado: $API_SIZE líneas"
            
            if [ $API_SIZE -gt 10 ]; then
                echo -e "${GREEN}✅ Cliente API parece estar poblado${NC}"
            else
                echo -e "${YELLOW}⚠️ Cliente API muy pequeño${NC}"
            fi
        else
            echo -e "${RED}❌ Cliente API no generado${NC}"
        fi
    else
        echo -e "${RED}❌ Error generando cliente API${NC}"
        return 1
    fi
    
    cd ..
}

# Function to validate generated files
validate_generated_files() {
    echo -e "${BLUE}5. Validando archivos generados...${NC}"
    
    # Check OpenAPI file
    OPENAPI_FILE="rayuela_frontend/src/lib/openapi/openapi.json"
    if [ -f "$OPENAPI_FILE" ]; then
        OPENAPI_SIZE=$(wc -c < "$OPENAPI_FILE")
        echo "📄 OpenAPI file size: $OPENAPI_SIZE bytes"
        
        # Check if it has paths
        if command -v jq &> /dev/null; then
            PATHS_COUNT=$(jq '.paths | length' "$OPENAPI_FILE" 2>/dev/null || echo "0")
            echo "📋 Paths in OpenAPI: $PATHS_COUNT"
            
            if [ "$PATHS_COUNT" -gt "0" ]; then
                echo -e "${GREEN}✅ OpenAPI contiene rutas${NC}"
                
                # Show sample paths
                echo "📋 Rutas de ejemplo:"
                jq -r '.paths | keys | .[:5][]' "$OPENAPI_FILE" 2>/dev/null | sed 's/^/  /'
            else
                echo -e "${RED}❌ OpenAPI no contiene rutas${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ Archivo OpenAPI no encontrado${NC}"
    fi
    
    # Check generated API client
    CLIENT_FILE="rayuela_frontend/src/lib/generated/rayuelaAPI.ts"
    if [ -f "$CLIENT_FILE" ]; then
        CLIENT_SIZE=$(wc -l < "$CLIENT_FILE")
        echo "📄 Generated client size: $CLIENT_SIZE lines"
        
        if [ $CLIENT_SIZE -gt 50 ]; then
            echo -e "${GREEN}✅ Cliente API generado correctamente${NC}"
        else
            echo -e "${YELLOW}⚠️ Cliente API muy pequeño${NC}"
        fi
    else
        echo -e "${RED}❌ Cliente API no encontrado${NC}"
    fi
}

# Function to provide next steps
provide_next_steps() {
    echo -e "${BLUE}6. Próximos pasos recomendados${NC}"
    echo "================================"
    echo ""
    
    echo -e "${YELLOW}📋 Para completar la migración a cliente generado:${NC}"
    echo ""
    
    echo "1. 🔍 Verificar que el cliente generado funciona:"
    echo "   cd rayuela_frontend"
    echo "   npm run type-check"
    echo ""
    
    echo "2. 🔄 Refactorizar código para usar cliente generado:"
    echo "   # Reemplazar imports manuales con cliente generado"
    echo "   # Actualizar src/lib/api.ts y src/lib/api-wrapper.ts"
    echo ""
    
    echo "3. 🧪 Ejecutar tests:"
    echo "   cd rayuela_frontend"
    echo "   npm run test"
    echo ""
    
    echo "4. 🚀 Configurar CI/CD para generar automáticamente:"
    echo "   # Agregar step de generación en pipeline"
    echo ""
    
    echo -e "${GREEN}💡 El OpenAPI y cliente API deberían estar funcionando ahora${NC}"
}

# Main execution
echo "🚀 Iniciando diagnóstico y reparación de OpenAPI..."
echo ""

# Check if we're in the right directory
if [ ! -d "rayuela_backend" ] || [ ! -d "rayuela_frontend" ]; then
    echo -e "${RED}❌ Error: Execute this script from the project root directory${NC}"
    echo "Expected structure:"
    echo "  project-root/"
    echo "  ├── rayuela_backend/"
    echo "  └── rayuela_frontend/"
    exit 1
fi

# Navigate to project root
cd "$(dirname "$0")/.." || exit 1

# Execute steps
check_backend_status

if start_backend_and_verify; then
    echo -e "${GREEN}✅ Backend y OpenAPI funcionando correctamente${NC}"
    regenerate_frontend_client
    validate_generated_files
    provide_next_steps
else
    echo -e "${RED}❌ Problemas con OpenAPI, ejecutando diagnóstico...${NC}"
    diagnose_backend_config
    
    echo ""
    echo -e "${YELLOW}💡 Posibles soluciones:${NC}"
    echo "1. Verificar que todos los routers estén correctamente incluidos"
    echo "2. Revisar que los endpoints tengan decoradores FastAPI correctos"
    echo "3. Verificar imports en src/api/v1/api.py"
    echo "4. Comprobar que no hay errores de sintaxis en los endpoints"
fi

echo ""
echo "======================================================"
echo -e "${BLUE}🔧 Diagnóstico de OpenAPI completado${NC}"
echo "======================================================" 