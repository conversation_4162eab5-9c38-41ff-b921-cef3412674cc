import { defineConfig } from 'orval';
import path from 'path';

const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export default defineConfig({
  rayuelaApi: {
    output: {
      mode: 'single',
      target: 'src/lib/generated/rayuelaAPI.ts',
      client: 'axios',
      prettier: true,
      override: {
        operations: {
          // Personalizar nombres de operaciones si es necesario
        },
        mock: false, // Desactivar generación de mocks
      },
    },
    input: {
      target: path.resolve(process.cwd(), 'src/lib/openapi/openapi.json'),
    },
    hooks: {
      afterAllFilesWrite: 'prettier --write src/lib/generated',
    },
  },
}); 