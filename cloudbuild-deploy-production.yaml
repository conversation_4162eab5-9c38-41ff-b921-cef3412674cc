steps:
  # 1. Setup and basic validation
  - name: 'python:3.12-slim'
    id: 'setup-environment'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🚀 Setting up build environment..."
        apt-get update && apt-get install -y git curl
        echo "✅ Environment setup complete"

  # 2. Build Backend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-backend'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:latest'
      - '-f'
      - 'rayuela_backend/Dockerfile'
      - 'rayuela_backend'

  # 3. Build Frontend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-frontend'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:latest'
      - '-f'
      - 'rayuela_frontend/Dockerfile'
      - 'rayuela_frontend'

  # 4. Push Backend image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
    waitFor: ['build-backend']

  # 5. Push Frontend image  
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-frontend'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'
    waitFor: ['build-frontend']

  # 6. CRITICAL: Run Alembic Migrations (BEFORE deployment)
  - name: 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
    id: 'run-migrations'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "⚙️ Running Alembic migrations for production database..."

        # Configure environment variables from Secret Manager
        echo "🔐 Loading database configuration from Secret Manager..."
        export POSTGRES_USER=$$(gcloud secrets versions access latest --secret="POSTGRES_USER" --project=$PROJECT_ID)
        export POSTGRES_PASSWORD=$$(gcloud secrets versions access latest --secret="POSTGRES_PASSWORD" --project=$PROJECT_ID)
        export POSTGRES_SERVER=$$(gcloud secrets versions access latest --secret="POSTGRES_SERVER" --project=$PROJECT_ID)
        export POSTGRES_PORT=$$(gcloud secrets versions access latest --secret="POSTGRES_PORT" --project=$PROJECT_ID)
        export POSTGRES_DB=$$(gcloud secrets versions access latest --secret="POSTGRES_DB" --project=$PROJECT_ID)
        export SECRET_KEY=$$(gcloud secrets versions access latest --secret="SECRET_KEY" --project=$PROJECT_ID)
        export GCP_PROJECT_ID=$PROJECT_ID
        export GCP_REGION=us-central1
        export ENV=production

        echo "📋 Database connection: $$POSTGRES_SERVER:$$POSTGRES_PORT/$$POSTGRES_DB"

        # Test database connectivity
        echo "🔍 Testing database connectivity..."
        python -c "
        import asyncio
        import asyncpg
        import os

        async def test_connection():
            try:
                conn = await asyncpg.connect(
                    user=os.getenv('POSTGRES_USER'),
                    password=os.getenv('POSTGRES_PASSWORD'),
                    database=os.getenv('POSTGRES_DB'),
                    host=os.getenv('POSTGRES_SERVER'),
                    port=int(os.getenv('POSTGRES_PORT'))
                )
                await conn.close()
                print('✅ Database connection successful')
                return True
            except Exception as e:
                print(f'❌ Database connection failed: {e}')
                return False

        if not asyncio.run(test_connection()):
            exit(1)
        "

        # Run migrations using the dedicated Python script
        echo "🚀 Executing Alembic migrations..."

        # First, try using the dedicated migration script if available
        if [ -f "scripts/migrations/run_migration.py" ]; then
          echo "📝 Using dedicated migration script..."
          python scripts/migrations/run_migration.py
          MIGRATION_EXIT_CODE=$?
        else
          echo "📝 Using direct Alembic command..."
          alembic -c alembic.ini upgrade head
          MIGRATION_EXIT_CODE=$?
        fi

        if [ $MIGRATION_EXIT_CODE -eq 0 ]; then
          echo "✅ Alembic migrations completed successfully"
        else
          echo "❌ Alembic migrations failed with exit code: $MIGRATION_EXIT_CODE"
          exit 1
        fi

        # Verify migration status
        echo "📊 Current migration status:"
        alembic -c alembic.ini current

        echo "✅ Database migration step completed"
    waitFor: ['push-backend']

  # 7. Deploy Backend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-backend'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-backend'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=4Gi'
      - '--cpu=2'
      - '--min-instances=0'
      - '--max-instances=10'
      - '--timeout=300s'
      - '--concurrency=80'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO'
      - '--set-env-vars=ALLOWED_HOSTS=["rayuela-backend-lrw7xazcbq-uc.a.run.app"]'
      - '--set-secrets=POSTGRES_PASSWORD=DB_PASSWORD:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest'
      - '--vpc-connector=rayuela-vpc-connector'
    waitFor: ['run-migrations']

  # 8. Get Backend URL for Frontend
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'get-backend-url'
    entrypoint: bash
    args:
      - '-c'
      - |
        BACKEND_URL=$$(gcloud run services describe rayuela-backend --region=us-central1 --format="value(status.url)")
        echo "Backend URL: $$BACKEND_URL"
        echo "$$BACKEND_URL" > /workspace/backend_url.txt
    waitFor: ['deploy-backend']

  # 9. Deploy Frontend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-frontend'
    entrypoint: bash
    args:
      - '-c'
      - |
        BACKEND_URL=$$(cat /workspace/backend_url.txt)
        echo "Deploying frontend with backend URL: $$BACKEND_URL"
        gcloud run deploy rayuela-frontend \
          --image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID \
          --region=us-central1 \
          --platform=managed \
          --allow-unauthenticated \
          --memory=1Gi \
          --cpu=1 \
          --min-instances=0 \
          --max-instances=5 \
          --timeout=300s \
          --set-env-vars=NEXT_PUBLIC_API_BASE_URL="$$BACKEND_URL",NODE_ENV=production \
          --vpc-connector=rayuela-vpc-connector
    waitFor: ['get-backend-url', 'push-frontend']

  # 10. Deploy Celery Worker for Maintenance Tasks
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-worker-maintenance'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-worker-maintenance'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=1Gi'
      - '--cpu=1'
      - '--min-instances=1'
      - '--max-instances=2'
      - '--timeout=3600s'  # 1 hour timeout for long-running maintenance tasks
      - '--concurrency=1'
      - '--command=celery'
      - '--args=-A,src.workers.celery_app,worker,--loglevel=info,--concurrency=1,--max-tasks-per-child=10,--queues=maintenance,--hostname=maintenance@%h'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO'
      - '--set-secrets=POSTGRES_PASSWORD=DB_PASSWORD:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest'
      - '--vpc-connector=rayuela-vpc-connector'
    waitFor: ['push-backend']

  # 11. Deploy Celery Beat Scheduler
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-celery-beat'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-beat'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=512Mi'
      - '--cpu=0.5'
      - '--min-instances=1'
      - '--max-instances=1'
      - '--timeout=3600s'
      - '--concurrency=1'
      - '--command=celery'
      - '--args=-A,src.workers.celery_app,beat,--loglevel=info'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO'
      - '--set-secrets=POSTGRES_PASSWORD=DB_PASSWORD:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest'
      - '--vpc-connector=rayuela-vpc-connector'
    waitFor: ['push-backend']

  # 12. Health check
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'health-check'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🏥 Performing health checks..."

        BACKEND_URL=$$(cat /workspace/backend_url.txt)
        FRONTEND_URL=$$(gcloud run services describe rayuela-frontend --region=us-central1 --format="value(status.url)")

        echo "Backend URL: $$BACKEND_URL"
        echo "Frontend URL: $$FRONTEND_URL"

        # Test Backend
        echo "Testing backend health..."
        curl -f "$$BACKEND_URL/health" || echo "Backend health check failed"

        # Verify Celery services are running
        echo "🔧 Verifying Celery services..."

        # Check worker status
        WORKER_STATUS=$$(gcloud run services describe rayuela-worker-maintenance --region=us-central1 --format="value(status.conditions[0].status)" 2>/dev/null || echo "Unknown")
        echo "Worker Maintenance Status: $$WORKER_STATUS"

        # Check beat scheduler status
        BEAT_STATUS=$$(gcloud run services describe rayuela-beat --region=us-central1 --format="value(status.conditions[0].status)" 2>/dev/null || echo "Unknown")
        echo "Celery Beat Status: $$BEAT_STATUS"

        echo "✅ Deployment completed successfully!"
        echo "🌐 Frontend: $$FRONTEND_URL"
        echo "🔧 Backend API: $$BACKEND_URL"
        echo "⚙️ Worker Maintenance: $$WORKER_STATUS"
        echo "⏰ Celery Beat: $$BEAT_STATUS (manages partition tasks daily)"
    waitFor: ['deploy-frontend', 'deploy-worker-maintenance', 'deploy-celery-beat']

# Images to store in registry
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:latest'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:latest'

# Timeout
timeout: '2400s'

options:
  logging: CLOUD_LOGGING_ONLY 