// src/lib/useApiKeys.ts
import useS<PERSON>, { SWRResponse } from 'swr';
import {
  listApi<PERSON>eys,
  createApi<PERSON>ey,
  updateApi<PERSON>ey,
  revokeApiKey,
  NewApiKeyResponse,
  ApiKey,
  ApiKeyListResponse,
  ApiKeyCreate,
  ApiKeyUpdate,
  ApiError
} from '@/lib/api';
import { useAuth } from '@/lib/auth';
import { useState } from 'react';

// Legacy interfaces for backward compatibility
interface ApiKeyState {
  data: ApiKey | null;
  error: ApiError | null;
  isLoading: boolean;
  isValidating: boolean;
  mutate: () => Promise<ApiKey | undefined>;
  dataUpdatedAt: number;
}

interface ApiKeyOperations {
  regenerateApiKey: () => Promise<NewApiKeyResponse | null>;
  isRegenerating: boolean;
  operationError: ApiError | null;
  getFormattedApiKey: () => string | null;
}

// New interfaces for multi-API key management
interface MultiApiKeyState {
  data: ApiKeyListResponse | null;
  error: ApiError | null;
  isLoading: boolean;
  isValidating: boolean;
  mutate: () => Promise<ApiKeyListResponse | undefined>;
  dataUpdatedAt: number;
}

interface MultiApiKeyOperations {
  createApiKey: (data: ApiKeyCreate) => Promise<NewApiKeyResponse | null>;
  updateApiKey: (id: number, data: ApiKeyUpdate) => Promise<ApiKey | null>;
  revokeApiKey: (id: number) => Promise<boolean>;
  isCreating: boolean;
  isUpdating: boolean;
  isRevoking: boolean;
  operationError: ApiError | null;
  getFormattedApiKey: (apiKey: ApiKey) => string | null;
}

/**
 * Hook para gestionar la API Key del usuario.
 *
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de la API Key, estado de carga, errores y funciones de utilidad
 */
export function useApiKeys(options: {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
} = {}): ApiKeyState & ApiKeyOperations {
  const { token, apiKey } = useAuth();
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [operationError, setOperationError] = useState<ApiError | null>(null);

  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt
  } = useSWR<ApiKey, ApiError>(
    token && apiKey ? ['api-key', token, apiKey] : null,
    async ([_, tokenValue, apiKeyValue]) => await getApiKey(tokenValue, apiKeyValue),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? true,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 60000,
      errorRetryCount: options.errorRetryCount ?? 3,
      onError: (err) => {
        if (ApiError.isApiError(err)) {
          console.error('Error fetching API key:', err.message, err.details);
        } else {
          console.error('Unknown error fetching API key:', err);
        }
      }
    }
  );

  /**
   * Regenera la API Key
   * @returns La nueva API Key generada o null si ocurre un error
   */
  const regenerateKey = async (): Promise<NewApiKeyResponse | null> => {
    if (!token || !apiKey) {
      setOperationError(new ApiError(
        'No hay token o API Key disponible',
        401,
        'AUTH_REQUIRED',
        null
      ));
      return null;
    }

    setIsRegenerating(true);
    setOperationError(null);

    try {
      const response = await createApiKey(token, apiKey);
      await mutate();
      return response;
    } catch (err) {
      const apiError = ApiError.isApiError(err) ? err : new ApiError(
        'Error al regenerar API Key',
        500,
        'REGENERATE_API_KEY_ERROR',
        null
      );
      setOperationError(apiError);
      return null;
    } finally {
      setIsRegenerating(false);
    }
  };

  /**
   * Formatea la API Key para mostrar (prefijo + asteriscos + sufijo)
   * @returns API Key formateada o null si no se encuentra
   */
  const getFormattedApiKey = (): string | null => {
    if (!data || !data.prefix || !data.last_chars) return null;
    return `${data.prefix}••••••••${data.last_chars}`;
  };

  return {
    data: data ?? null,
    error: error ?? null,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt: dataUpdatedAt ?? 0,
    regenerateApiKey: regenerateKey,
    isRegenerating,
    operationError,
    getFormattedApiKey
  };
}

/**
 * Hook para gestionar múltiples API Keys del usuario.
 *
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de las API Keys, estado de carga, errores y funciones de utilidad
 */
export function useMultiApiKeys(options: {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
} = {}): MultiApiKeyState & MultiApiKeyOperations {
  const { token, apiKey } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRevoking, setIsRevoking] = useState(false);
  const [operationError, setOperationError] = useState<ApiError | null>(null);

  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt
  } = useSWR<ApiKeyListResponse, ApiError>(
    token && apiKey ? ['api-keys-list', token, apiKey] : null,
    async ([_, tokenValue, apiKeyValue]) => await listApiKeys(tokenValue, apiKeyValue),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? true,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 60000,
      errorRetryCount: options.errorRetryCount ?? 3,
      onError: (err) => {
        if (ApiError.isApiError(err)) {
          console.error('Error fetching API keys:', err.message, err.details);
        } else {
          console.error('Unknown error fetching API keys:', err);
        }
      }
    }
  );

  /**
   * Crea una nueva API Key
   * @param data Datos para crear la API Key
   * @returns La nueva API Key generada o null si ocurre un error
   */
  const createNewApiKey = async (data: ApiKeyCreate): Promise<NewApiKeyResponse | null> => {
    if (!token || !apiKey) {
      setOperationError(new ApiError(
        'No hay token o API Key disponible',
        401,
        'AUTH_REQUIRED',
        null
      ));
      return null;
    }

    setIsCreating(true);
    setOperationError(null);

    try {
      const response = await createApiKey(token, apiKey, data);
      await mutate();
      return response;
    } catch (err) {
      const apiError = ApiError.isApiError(err) ? err : new ApiError(
        'Error al crear API Key',
        500,
        'CREATE_API_KEY_ERROR',
        null
      );
      setOperationError(apiError);
      return null;
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Actualiza una API Key existente
   * @param id ID de la API Key a actualizar
   * @param data Datos a actualizar
   * @returns La API Key actualizada o null si ocurre un error
   */
  const updateExistingApiKey = async (id: number, data: ApiKeyUpdate): Promise<ApiKey | null> => {
    if (!token || !apiKey) {
      setOperationError(new ApiError(
        'No hay token o API Key disponible',
        401,
        'AUTH_REQUIRED',
        null
      ));
      return null;
    }

    setIsUpdating(true);
    setOperationError(null);

    try {
      const response = await updateApiKey(token, apiKey, id, data);
      await mutate();
      return response;
    } catch (err) {
      const apiError = ApiError.isApiError(err) ? err : new ApiError(
        'Error al actualizar API Key',
        500,
        'UPDATE_API_KEY_ERROR',
        null
      );
      setOperationError(apiError);
      return null;
    } finally {
      setIsUpdating(false);
    }
  };

  /**
   * Revoca una API Key específica
   * @param id ID de la API Key a revocar
   * @returns true si se revocó exitosamente, false si ocurre un error
   */
  const revokeExistingApiKey = async (id: number): Promise<boolean> => {
    if (!token || !apiKey) {
      setOperationError(new ApiError(
        'No hay token o API Key disponible',
        401,
        'AUTH_REQUIRED',
        null
      ));
      return false;
    }

    setIsRevoking(true);
    setOperationError(null);

    try {
      await revokeApiKey(token, apiKey, id);
      await mutate();
      return true;
    } catch (err) {
      const apiError = ApiError.isApiError(err) ? err : new ApiError(
        'Error al revocar API Key',
        500,
        'REVOKE_API_KEY_ERROR',
        null
      );
      setOperationError(apiError);
      return false;
    } finally {
      setIsRevoking(false);
    }
  };

  /**
   * Formatea una API Key para mostrar (prefijo + asteriscos + sufijo)
   * @param apiKey La API Key a formatear
   * @returns API Key formateada o null si no se puede formatear
   */
  const getFormattedApiKey = (apiKey: ApiKey): string | null => {
    if (!apiKey.prefix || !apiKey.last_chars) return null;
    return `${apiKey.prefix}••••••••${apiKey.last_chars}`;
  };

  return {
    data: data ?? null,
    error: error ?? null,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt: dataUpdatedAt ?? 0,
    createApiKey: createNewApiKey,
    updateApiKey: updateExistingApiKey,
    revokeApiKey: revokeExistingApiKey,
    isCreating,
    isUpdating,
    isRevoking,
    operationError,
    getFormattedApiKey
  };
}
