// src/lib/useUsageSummary.ts
import useSWR from 'swr';
import { getAccountUsage, AccountUsage } from '@/lib/api';
import { useAuth } from '@/lib/auth';
import { usePlans } from '@/lib/usePlans';

/**
 * Hook para obtener y gestionar la información de uso de la cuenta.
 * Utiliza SWR para cachear los datos y proporcionar revalidación automática.
 * 
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de uso, estado de carga, errores y funciones de utilidad
 */
export function useUsageSummary(options: {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
} = {}) {
  const { token, apiKey } = useAuth();
  const { plans } = usePlans();
  
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt
  } = useSWR<AccountUsage>(
    token && apiKey ? ['account-usage', token, apiKey] : null,
    async ([_, tokenValue, apiKeyValue]) => await getAccountUsage(tokenValue, apiKeyValue),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? false,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 60000, // 1 minuto por defecto
      errorRetryCount: options.errorRetryCount ?? 3
    }
  );

  // Función para obtener el porcentaje de uso de API calls
  const getApiCallsPercentage = () => {
    if (!data) return 0;
    return data.api_calls.percentage;
  };

  // Función para obtener el porcentaje de uso de almacenamiento
  const getStoragePercentage = () => {
    if (!data) return 0;
    return data.storage.percentage;
  };

  // Función para verificar si se puede entrenar ahora
  const canTrainNow = () => {
    if (!data) return false;
    return data.training.can_train_now;
  };

  // Función para obtener la fecha del próximo entrenamiento disponible
  const getNextTrainingDate = () => {
    if (!data || !data.training.next_training_available) return null;
    return new Date(data.training.next_training_available);
  };

  // Función para obtener la fecha de la última medición de almacenamiento
  const getLastStorageMeasurement = () => {
    if (!data || !data.storage.last_measured) return null;
    return new Date(data.storage.last_measured);
  };

  // Función para obtener la fecha del próximo reset de API calls
  const getNextResetDate = () => {
    if (!data || !data.api_calls.next_reset) return null;
    return new Date(data.api_calls.next_reset);
  };

  // Función para verificar si se ha excedido el límite de API calls
  const hasExceededApiCallsLimit = () => {
    if (!data) return false;
    return data.api_calls.percentage >= 100;
  };

  // Función para verificar si se ha excedido el límite de almacenamiento
  const hasExceededStorageLimit = () => {
    if (!data) return false;
    return data.storage.percentage >= 100;
  };

  // Función para obtener el uso de almacenamiento formateado
  const getFormattedStorageUsage = () => {
    if (!data) return '0 B';
    
    const bytes = data.storage.used_bytes;
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Función para obtener el límite de almacenamiento formateado
  const getFormattedStorageLimit = () => {
    if (!data) return '0 B';
    
    const bytes = data.storage.limit_bytes;
    if (bytes === 0) return 'Ilimitado';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Función para obtener el uso de API calls formateado
  const getFormattedApiCallsUsage = () => {
    if (!data) return '0';
    return data.api_calls.used.toLocaleString();
  };

  // Función para obtener el límite de API calls formateado
  const getFormattedApiCallsLimit = () => {
    if (!data) return '0';
    if (data.api_calls.limit === 0) return 'Ilimitado';
    return data.api_calls.limit.toLocaleString();
  };

  // Función para obtener los modelos disponibles
  const getAvailableModels = () => {
    if (!data) return [];
    return data.features.available_models;
  };

  // Función para obtener el límite de solicitudes por minuto
  const getRequestsPerMinuteLimit = () => {
    if (!data) return 0;
    return data.features.max_requests_per_minute;
  };

  return {
    usageData: data,
    error,
    isLoading,
    isValidating,
    refresh: mutate,
    lastUpdated: dataUpdatedAt ? new Date(dataUpdatedAt) : null,
    getApiCallsPercentage,
    getStoragePercentage,
    canTrainNow,
    getNextTrainingDate,
    getLastStorageMeasurement,
    getNextResetDate,
    hasExceededApiCallsLimit,
    hasExceededStorageLimit,
    getFormattedStorageUsage,
    getFormattedStorageLimit,
    getFormattedApiCallsUsage,
    getFormattedApiCallsLimit,
    getAvailableModels,
    getRequestsPerMinuteLimit
  };
}
