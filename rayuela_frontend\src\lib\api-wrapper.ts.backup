/**
 * Este archivo muestra cómo refactorizar gradualmente las funciones API existentes
 * para utilizar las generadas por Orval sin romper la compatibilidad con el código existente.
 */

// Importamos los tipos antiguos para mantener compatibilidad
import { 
  AccountInfo,
  AccountUsage,
  ApiKey,
  AuthTokenResponse,
  CheckoutSessionResponse,
  NewApiKeyResponse,
  RegisterResponse,
  UsageHistoryPoint,
  UserInfo
} from './api';

// Importamos las funciones y tipos generados (ejemplos que se generarán con Orval)
// Nota: Las rutas exactas dependerán de cómo genere Orval los archivos
import { getUsersMe } from './generated/users'; // ejemplo
import { getAccountsCurrent } from './generated/accounts'; // ejemplo
import { postAuthLogin } from './generated/auth'; // ejemplo
import { convertAuthOptions } from './generated/migration-helper';

/**
 * Versión refactorizada de la función loginUser que internamente usa el código generado
 * con la nueva firma JSON.
 */
export const loginUser = (credentials: { email: string; password: string }): Promise<AuthTokenResponse> => {
  // Usamos la función generada por Orval con el formato correcto
  return postAuthLogin(credentials)
    .then(response => response.data); // Extraemos solo los datos
};

/**
 * Versión refactorizada de getMe
 */
export const getMe = (token: string, apiKey?: string | null): Promise<UserInfo> => {
  return getUsersMe({}, convertAuthOptions(token, apiKey))
    .then(response => response.data);
};

/**
 * Versión refactorizada de getMyAccount
 */
export const getMyAccount = (token: string, apiKey?: string): Promise<AccountInfo> => {
  return getAccountsCurrent({}, convertAuthOptions(token, apiKey))
    .then(response => response.data);
};

/**
 * Notas para la migración completa:
 * 
 * 1. Crear este archivo con las mismas funciones que en api.ts pero usando el código generado
 * 2. Migrar las importaciones en los componentes de manera gradual:
 *    - De: import { getMe } from '@/lib/api';
 *    - A: import { getMe } from '@/lib/api-wrapper';
 * 3. Una vez que todos los componentes usen api-wrapper.ts, renombrar este archivo a api.ts
 */ 