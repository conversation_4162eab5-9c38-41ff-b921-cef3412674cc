"""
Este archivo es un wrapper para mantener compatibilidad con scripts existentes.
Para inicialización de base de datos en producción, usar SIEMPRE Alembic:
    alembic upgrade head

Este script solo debe usarse para la configuración inicial o entornos de desarrollo local.
"""

import asyncio
from src.utils.base_logger import logger

# Importar las funciones de db_manage_utils
from scripts.db_manage_utils import (
    _create_database_if_not_exists,
    _run_alembic_upgrade,
    _insert_seed_data,
    initialize_database_schema_and_data,
)


async def initialize_database_and_data():
    """
    Inicializa la base de datos y carga datos iniciales.
    Esta función es un wrapper para mantener compatibilidad.
    
    ADVERTENCIA: En producción, usar SIEMPRE Alembic directamente:
        alembic upgrade head
    """
    logger.warning(
        "ADVERTENCIA: Este script es solo para desarrollo local o configuración inicial."
    )
    logger.warning(
        "En producción, usar SIEMPRE 'alembic upgrade head' para migraciones."
    )
    
    try:
        # Usar la función de db_manage_utils
        await initialize_database_schema_and_data()
        return True
    except Exception as e:
        logger.error(f"Error inicializando la base de datos: {e}")
        return False


# Función para reset de base de datos
async def reset_database():
    """
    Elimina y recrea la base de datos.
    ADVERTENCIA: Solo para desarrollo local.
    """
    logger.warning("ADVERTENCIA: Esta función elimina TODOS los datos.")
    logger.warning("Solo debe usarse en entornos de desarrollo local.")
    
    from scripts.db_manage_utils import _reset_database
    
    try:
        await _reset_database()
        await _run_alembic_upgrade()
        return True
    except Exception as e:
        logger.error(f"Error reseteando la base de datos: {e}")
        return False


if __name__ == "__main__":
    # Si se ejecuta directamente, inicializar la base de datos
    logger.info("Inicializando base de datos...")
    result = asyncio.run(initialize_database_and_data())
    if result:
        logger.info("Base de datos inicializada correctamente.")
    else:
        logger.error("Error inicializando la base de datos.")
