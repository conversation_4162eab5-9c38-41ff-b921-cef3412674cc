# Configuración de Seguridad VPC para Rayuela

Esta guía completa te ayudará a configurar la seguridad de red para tu aplicación Rayuela, asegurando que Cloud SQL y Redis solo sean accesibles a través de red privada.

## 🎯 Objetivo

Eliminar la **vulnerabilidad crítica** de exposición de base de datos identificada en el análisis de seguridad, configurando todas las conexiones para usar red privada únicamente.

## 📋 Requisitos Previos

- Proyecto GCP configurado
- gcloud CLI instalado y autenticado
- Permisos de administrador del proyecto
- Variables de entorno configuradas:
  ```bash
  export GCP_PROJECT_ID="tu-proyecto"
  export GCP_REGION="us-central1"
  ```

## 🔧 Scripts Disponibles

Los siguientes scripts automatizan la configuración completa:

### 1. 🔍 Verificar Configuración Actual
```bash
./scripts/verify-vpc-security.sh
```
**Propósito**: Audita la configuración actual de VPC, Cloud SQL, Redis y Cloud Run.

### 2. 🔗 Configurar VPC Connector
```bash
./scripts/setup-vpc-connector.sh
```
**Propósito**: Crea el VPC Connector necesario para que Cloud Run acceda a servicios privados.

### 3. 🔒 Configurar Cloud SQL Privado
```bash
./scripts/configure-sql-private.sh
```
**Propósito**: Configura Cloud SQL para usar solo IP privada.

### 4. 🔒 Configurar Redis Privado
```bash
./scripts/configure-redis-private.sh
```
**Propósito**: Verifica/configura Cloud Memorystore (Redis) para red privada.

## 🚀 Proceso de Configuración Paso a Paso

### Paso 1: Verificación Inicial
```bash
./scripts/verify-vpc-security.sh
```

Este script te mostrará el estado actual y qué necesita configuración.

### Paso 2: Configurar VPC Connector
```bash
./scripts/setup-vpc-connector.sh
```

**Qué hace**:
- Habilita APIs requeridas
- Crea VPC Connector `rayuela-vpc-connector`
- Configura rango IP: `********/28`
- Establece instancias mínimas/máximas

**Tiempo estimado**: 5-10 minutos

### Paso 3: Configurar Cloud SQL
```bash
./scripts/configure-sql-private.sh
```

**Qué hace**:
- Configura Private Service Access
- Deshabilita IP pública en instancias Cloud SQL
- Configura red privada VPC

**⚠️ IMPORTANTE**: Este paso deshabilitará el acceso público a la base de datos.

### Paso 4: Configurar Redis
```bash
./scripts/configure-redis-private.sh
```

**Qué hace**:
- Verifica configuración de instancias Redis existentes
- Opcionalmente crea instancia de ejemplo
- Valida que usen red privada

### Paso 5: Actualizar Secretos
Después de configurar los servicios, actualiza los secretos con las IPs privadas:

```bash
# Obtener IP privada de Cloud SQL
SQL_PRIVATE_IP=$(gcloud sql instances describe INSTANCE_NAME \
  --format="value(ipAddresses[].ipAddress)" \
  --filter="ipAddresses.type=PRIVATE")

# Actualizar secreto
gcloud secrets versions add POSTGRES_SERVER \
  --data-file=- <<< "$SQL_PRIVATE_IP"

# Obtener IP privada de Redis
REDIS_HOST=$(gcloud redis instances describe INSTANCE_NAME \
  --region=us-central1 --format='value(host)')

# Actualizar secreto
gcloud secrets versions add REDIS_HOST \
  --data-file=- <<< "$REDIS_HOST"
```

### Paso 6: Desplegar con Nueva Configuración
```bash
gcloud builds submit --config cloudbuild-deploy-production.yaml
```

Los archivos `cloudbuild.yaml` y `cloudbuild-deploy-production.yaml` ya están configurados para usar VPC Connector.

### Paso 7: Verificación Final
```bash
./scripts/verify-vpc-security.sh
```

## 🛡️ Configuración Implementada

### VPC Connector
- **Nombre**: `rayuela-vpc-connector`
- **Región**: `us-central1`
- **Red**: `default`
- **Rango IP**: `********/28`
- **Instancias**: 2-3

### Cloud Run Services
Todos los servicios ahora usan `--vpc-connector=rayuela-vpc-connector`:
- ✅ `rayuela-backend`
- ✅ `rayuela-frontend`
- ✅ `rayuela-worker-maintenance`
- ✅ `rayuela-beat`

### Cloud SQL
- ✅ IP pública deshabilitada
- ✅ Solo red privada VPC
- ✅ Private Service Access configurado

### Cloud Memorystore (Redis)
- ✅ Solo red VPC autorizada
- ✅ IP privada únicamente

## 🔍 Validación de Seguridad

### Verificaciones Automáticas
El script `verify-vpc-security.sh` valida:

1. **VPC Connector existe y está activo**
2. **Cloud SQL sin IP pública**
3. **Redis en red privada**
4. **Servicios Cloud Run usando VPC Connector**

### Verificaciones Manuales

#### Test de Conectividad
```bash
# Desde Cloud Shell o instancia en VPC
gcloud sql connect INSTANCE_NAME --user=postgres
redis-cli -h REDIS_PRIVATE_IP ping
```

#### Test de Aislamiento
```bash
# Desde fuera de VPC (debería fallar)
psql -h SQL_PUBLIC_IP -U postgres  # No debería conectar
redis-cli -h REDIS_PRIVATE_IP ping  # No debería conectar
```

## 🚨 Resolución de Problemas

### Error: VPC Connector no se crea
```bash
# Verificar APIs habilitadas
gcloud services list --enabled --filter="vpcaccess.googleapis.com"

# Verificar permisos
gcloud projects get-iam-policy $GCP_PROJECT_ID
```

### Error: Cloud SQL no se conecta
```bash
# Verificar Private Service Access
gcloud services vpc-peerings list --network=default

# Verificar instancia
gcloud sql instances describe INSTANCE_NAME
```

### Error: Redis no accesible
```bash
# Verificar red autorizada
gcloud redis instances describe INSTANCE_NAME --region=us-central1
```

## 📊 Métricas de Seguridad

Después de la implementación:

- 🔒 **0** servicios con acceso público a DB
- 🔒 **0** IPs públicas en Cloud SQL
- 🔗 **4** servicios usando VPC Connector
- ✅ **100%** tráfico por red privada

## 🎉 Beneficios Implementados

### Seguridad
- **Eliminación de superficie de ataque**: DB no accesible desde Internet
- **Defensa en profundidad**: Múltiples capas de seguridad de red
- **Aislamiento de tráfico**: Todo el tráfico DB por red privada

### Cumplimiento
- **Estándares de seguridad**: Cumple mejores prácticas de GCP
- **Auditoría**: Configuración trazable y reproducible
- **Documentación**: Proceso completamente documentado

### Mantenibilidad
- **Scripts automatizados**: Configuración reproducible
- **Verificación continua**: Scripts de validación
- **Reversibilidad**: Proceso documentado para cambios

## 🔄 Mantenimiento

### Verificación Mensual
```bash
# Ejecutar verificación de seguridad
./scripts/verify-vpc-security.sh

# Revisar logs de VPC Connector
gcloud logging read "resource.type=vpc_access_connector"
```

### Actualizaciones
- VPC Connector se mantiene automáticamente
- Cloud SQL y Redis managed por GCP
- Scripts se pueden re-ejecutar de forma segura

## 📚 Referencias

- [GCP VPC Connector Documentation](https://cloud.google.com/vpc/docs/configure-serverless-vpc-access)
- [Cloud SQL Private IP](https://cloud.google.com/sql/docs/postgres/private-ip)
- [Cloud Memorystore Security](https://cloud.google.com/memorystore/docs/redis/networking)
- [Cloud Run VPC Access](https://cloud.google.com/run/docs/configuring/vpc-connector)

---

**⚠️ Importante**: Una vez implementada esta configuración, solo será posible acceder a Cloud SQL y Redis desde servicios dentro de la VPC. Para acceso administrativo, utiliza Cloud Shell o una instancia en la misma VPC. 