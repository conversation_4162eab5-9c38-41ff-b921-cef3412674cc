"""
Helper functions for cache invalidation in UsageMeterMiddleware.
"""
from src.core.cache_manager import CacheManager
from src.utils.base_logger import log_info, log_warning


class UsageMeterCacheInvalidator:
    """Helper class for invalidating UsageMeterMiddleware cache entries."""
    
    def __init__(self):
        self._cache_manager = CacheManager()
    
    async def invalidate_account_cache(self, api_key: str) -> bool:
        """
        Invalidate account cache for a specific API key.
        
        Args:
            api_key: The API key whose account cache should be invalidated
            
        Returns:
            bool: True if invalidation was successful
        """
        try:
            cache_key = f"account:{api_key}"
            success = await self._cache_manager.delete(cache_key)
            if success:
                log_info(f"Invalidated account cache for API key: {api_key[:8]}...")
            return success
        except Exception as e:
            log_warning(f"Error invalidating account cache for API key {api_key[:8]}...: {str(e)}")
            return False
    
    async def invalidate_subscription_cache(self, account_id: int) -> bool:
        """
        Invalidate subscription cache for a specific account.
        
        Args:
            account_id: The account ID whose subscription cache should be invalidated
            
        Returns:
            bool: True if invalidation was successful
        """
        try:
            cache_key = f"subscription:{account_id}"
            success = await self._cache_manager.delete(cache_key)
            if success:
                log_info(f"Invalidated subscription cache for account: {account_id}")
            return success
        except Exception as e:
            log_warning(f"Error invalidating subscription cache for account {account_id}: {str(e)}")
            return False
    
    async def invalidate_all_account_caches(self) -> bool:
        """
        Invalidate all account caches.
        
        Returns:
            bool: True if invalidation was successful
        """
        try:
            pattern = "account:*"
            success = await self._cache_manager.delete_pattern(pattern)
            if success:
                log_info("Invalidated all account caches")
            return success
        except Exception as e:
            log_warning(f"Error invalidating all account caches: {str(e)}")
            return False
    
    async def invalidate_all_subscription_caches(self) -> bool:
        """
        Invalidate all subscription caches.
        
        Returns:
            bool: True if invalidation was successful
        """
        try:
            pattern = "subscription:*"
            success = await self._cache_manager.delete_pattern(pattern)
            if success:
                log_info("Invalidated all subscription caches")
            return success
        except Exception as e:
            log_warning(f"Error invalidating all subscription caches: {str(e)}")
            return False


# Singleton instance for easy access
_cache_invalidator = None

async def get_cache_invalidator() -> UsageMeterCacheInvalidator:
    """Get the singleton cache invalidator instance."""
    global _cache_invalidator
    if _cache_invalidator is None:
        _cache_invalidator = UsageMeterCacheInvalidator()
    return _cache_invalidator
