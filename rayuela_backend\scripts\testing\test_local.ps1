# Script simplificado para probar Rayuela localmente en Windows
# Este script configura y ejecuta los componentes esenciales para probar Rayuela

# Función para limpiar procesos al salir
function Cleanup {
    Write-Host "`nLimpiando procesos..." -ForegroundColor Yellow

    # Detener contenedores Docker
    if ($dockerStarted) {
        Write-Host "Deteniendo contenedores Docker..." -ForegroundColor Yellow
        docker-compose -f docker-compose.simple.yml down
    }

    # Detener procesos
    foreach ($process in $processes) {
        if (-not $process.HasExited) {
            Write-Host "Deteniendo proceso $($process.Id)..." -ForegroundColor Yellow
            Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
        }
    }

    Write-Host "Limpieza completada." -ForegroundColor Green
}

# Inicializar variables
$rootDir = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$processes = @()
$dockerStarted = $false

# Cambiar al directorio raíz
Set-Location $rootDir

# Parsear argumentos
param(
    [switch]$SkipDocker,
    [switch]$SkipDbInit,
    [switch]$SkipApi
)

try {
    # Iniciar servicios de Docker
    if (-not $SkipDocker) {
        Write-Host "Iniciando servicios de Docker..." -ForegroundColor Cyan

        # Verificar si docker-compose.simple.yml existe
        if (Test-Path "docker-compose.simple.yml") {
            docker-compose -f docker-compose.simple.yml up -d
        } else {
            docker-compose up -d db redis
        }

        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error al iniciar servicios de Docker." -ForegroundColor Red
            exit 1
        }

        $dockerStarted = $true
        Write-Host "Servicios de Docker iniciados correctamente." -ForegroundColor Green
        Start-Sleep -Seconds 5  # Esperar a que los servicios estén listos
    }

    # Activar entorno virtual si existe
    if (Test-Path "venv\Scripts\Activate.ps1") {
        Write-Host "Activando entorno virtual..." -ForegroundColor Cyan
        & .\venv\Scripts\Activate.ps1
    }

    # Inicializar base de datos
    if (-not $SkipDbInit) {
        Write-Host "Inicializando base de datos..." -ForegroundColor Cyan

        # Usar el script simplificado para inicializar la base de datos
        Write-Host "Inicializando base de datos con script simplificado..." -ForegroundColor Cyan
        python -m scripts.init_db_simple

        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error al inicializar la base de datos." -ForegroundColor Red
            exit 1
        }

        Write-Host "Base de datos inicializada correctamente." -ForegroundColor Green
    }

    # Iniciar servidor API
    if (-not $SkipApi) {
        Write-Host "Iniciando servidor API simplificado..." -ForegroundColor Cyan
        $apiProcess = Start-Process -FilePath "python" -ArgumentList "-m scripts.run_api_simple" -PassThru -WindowStyle Normal
        $processes += $apiProcess

        Write-Host "Servidor API iniciado correctamente (PID: $($apiProcess.Id))." -ForegroundColor Green
        Start-Sleep -Seconds 3  # Esperar a que el servidor esté listo
    }

    Write-Host "`nTodo el proceso se completó correctamente." -ForegroundColor Green
    Write-Host "La API está disponible en http://localhost:8001" -ForegroundColor Cyan
    Write-Host "Documentación de la API: http://localhost:8001/api/docs" -ForegroundColor Cyan
    Write-Host "Presiona Ctrl+C para detener todos los servicios." -ForegroundColor Yellow

    # Mantener el script en ejecución hasta que el usuario lo detenga
    while ($true) {
        Start-Sleep -Seconds 1
    }
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
}
finally {
    # Limpiar al salir
    Cleanup
}
