"use client";

import { useState, useEffect } from 'react';
import { format, parseISO, subDays, formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import {
  useAuth,
  usePlans,
  useAccountInfo,
  useUsageSummary,
  useUsageHistory
} from '@/lib/hooks';
import {
  AccountUsage,
  AccountInfo,
  UsageHistoryPoint
} from '@/lib/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { toast } from 'sonner';
import { handleApiError } from '@/lib/error-handler';
import {
  BarChart3Icon,
  DatabaseIcon,
  RefreshCwIcon,
  ClockIcon,
  TrendingUpIcon,
  InfoIcon,
  BrainIcon,
  ArrowUpIcon,
  AlertCircleIcon,
  HelpCircleIcon
} from 'lucide-react';
import { BillingButton } from '@/components/dashboard/BillingButton';
import { BillingPortalButton } from '@/components/dashboard/BillingPortalButton';
import UsageChart from '@/components/dashboard/UsageChart';
import { DateRangeSelector, DateRange } from '@/components/dashboard/DateRangeSelector';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

// Función para formatear bytes a una unidad legible
function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Función para formatear fechas
function formatDate(dateString: string | null): string {
  if (!dateString) return 'No disponible';
  try {
    return format(parseISO(dateString), "d 'de' MMMM 'de' yyyy, HH:mm", { locale: es });
  } catch (error) {
    console.error("Error al formatear fecha:", error);
    return 'Formato de fecha inválido';
  }
}

// Función para formatear números grandes
function formatNumber(num: number): string {
  return new Intl.NumberFormat().format(num);
}

// Interfaz para los datos de uso históricos
interface UsageDataPoint {
  date: string;
  apiCalls: number;
  storage: number;
}

export default function UsageDashboard() {
  const { token, apiKey } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [filteredData, setFilteredData] = useState<UsageDataPoint[]>([]);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 30),
    to: new Date()
  });

  // Usar los hooks personalizados para obtener datos
  const { plans, isLoading: plansLoading, error: plansError, getPlanById } = usePlans();

  // Obtener información de la cuenta
  const {
    accountData,
    error: accountError,
    isLoading: isAccountLoading,
  } = useAccountInfo();

  // Obtener datos de uso
  const {
    usageData: usageSummary,
    error: usageError,
    isLoading: isUsageLoading,
    isValidating: isValidatingUsage,
    refresh: mutateUsage,
    lastUpdated: usageUpdatedAt
  } = useUsageSummary({
    revalidateOnFocus: false,
    dedupingInterval: 60000, // 1 minuto
    errorRetryCount: 3
  });

  // Obtener historial de uso
  const {
    historyData,
    error: historyError,
    isLoading: isHistoryLoading,
    isValidating: isValidatingHistory,
    refresh: mutateHistory,
    lastUpdated: historyUpdatedAt
  } = useUsageHistory(
    dateRange.from,
    dateRange.to,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minuto
      errorRetryCount: 3
    }
  );

  // Convertir los datos históricos al formato esperado por el componente UsageChart
  useEffect(() => {
    if (historyData) {
      const chartData: UsageDataPoint[] = historyData.map(item => ({
        date: item.date,
        apiCalls: item.api_calls,
        storage: item.storage
      }));
      setFilteredData(chartData);
    }
  }, [historyData]);

  // Función para obtener la última actualización de datos
  const getLastUpdateTime = (): string => {
    // Obtener la fecha de actualización más reciente
    const timestamps = [usageUpdatedAt, usageUpdatedAt, historyUpdatedAt].filter(Boolean);

    if (timestamps.length === 0) return 'No disponible';

    const mostRecentUpdate = Math.max(...timestamps);

    if (mostRecentUpdate) {
      try {
        return formatDistanceToNow(new Date(mostRecentUpdate), {
          addSuffix: true,
          locale: es
        });
      } catch (error) {
        console.error("Error al formatear tiempo de actualización:", error);
        return 'Hace unos momentos';
      }
    }

    return 'No disponible';
  };

  // Función para refrescar los datos
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([mutateUsage(), mutateHistory()]);
      toast.success('Datos de uso actualizados');
    } catch (error: any) {
      handleApiError(error, 'Error al actualizar los datos de uso');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Función para manejar el cambio de rango de fechas
  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
  };

  // Calcular totales para el rango seleccionado
  const calculateTotals = () => {
    if (!filteredData.length) return { apiCalls: 0, storage: 0 };

    // Para las llamadas a la API, sumamos todas las llamadas en el rango
    const totalApiCalls = filteredData.reduce((sum, item) => sum + item.apiCalls, 0);

    // Para el almacenamiento, tomamos el valor más reciente
    const latestStorage = filteredData[filteredData.length - 1]?.storage || 0;

    return { apiCalls: totalApiCalls, storage: latestStorage };
  };

  const totals = calculateTotals();

  // Estado de carga combinado
  const isLoading = isUsageLoading || isAccountLoading || isHistoryLoading;

  // Mostrar un mensaje de carga mientras se obtienen los datos
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Uso de API</h1>
          <Skeleton className="h-9 w-24" />
        </div>

        {/* Skeletons para las tarjetas de métricas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="transition-all duration-300 hover:shadow-md">
              <CardHeader className="pb-2">
                <div className="flex items-center">
                  <Skeleton className="h-5 w-5 mr-2 rounded-md" />
                  <CardTitle><Skeleton className="h-6 w-3/4" /></CardTitle>
                </div>
                <CardDescription><Skeleton className="h-4 w-1/2" /></CardDescription>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-10 w-1/2 mx-auto" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Skeleton para el gráfico */}
        <Card className="transition-all duration-300 hover:shadow-md">
          <CardHeader>
            <CardTitle><Skeleton className="h-6 w-1/3" /></CardTitle>
            <CardDescription><Skeleton className="h-4 w-1/2" /></CardDescription>
            <div className="mt-2">
              <Skeleton className="h-10 w-full rounded-md" />
            </div>
          </CardHeader>
          <CardContent className="h-80">
            <Skeleton className="h-full w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Función para determinar si hay errores críticos que impiden mostrar datos útiles
  const hasCriticalErrors = (): boolean => {
    // Si no tenemos datos de cuenta o de uso, consideramos que es un error crítico
    return (!!accountError && !accountData) || (!!usageError && !usageSummary);
  };

  // Mostrar un mensaje de error si ocurre algún problema crítico
  if (hasCriticalErrors()) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Uso de API</h1>
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500">
              Último intento: {getLastUpdateTime()}
            </span>
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              disabled={isRefreshing}
              className="transition-all duration-200 hover:bg-red-50 hover:text-red-600 hover:border-red-200 focus:ring-2 focus:ring-red-200 focus:ring-offset-2"
            >
              {isRefreshing ? (
                <>
                  <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                  Actualizando...
                </>
              ) : (
                <>
                  <RefreshCwIcon className="h-4 w-4 mr-2" />
                  Reintentar
                </>
              )}
            </Button>
          </div>
        </div>

        <Alert variant="destructive" className="mb-8">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>Error al cargar los datos de uso</AlertTitle>
          <AlertDescription>
            <p className="mb-2">No se pudieron obtener las métricas de uso de la API. Por favor, intenta de nuevo más tarde.</p>
            <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800 text-sm">
              <strong>Detalles del error:</strong><br />
              {accountError && <p>Error de cuenta: {accountError.message}</p>}
              {usageError && <p>Error de uso: {usageError.message}</p>}
              {historyError && <p>Error de historial: {historyError.message}</p>}
              {!accountError && !usageError && !historyError && <p>Ocurrió un error desconocido</p>}
            </div>
          </AlertDescription>
        </Alert>

        {/* Aún mostramos el gráfico con datos de ejemplo */}
        <UsageChart
          error={new Error("No se pudieron cargar los datos reales. Mostrando datos de ejemplo.")}
          title="Historial de Uso (Datos de Ejemplo)"
          description="Los datos mostrados son ejemplos y no reflejan el uso real"
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      {/* Encabezado con información del plan y botones de facturación */}
      <Card className="mb-8 transition-all duration-300 hover:shadow-md">
        <CardHeader className="pb-2">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <CardTitle className="flex items-center text-lg">
                <InfoIcon className="h-5 w-5 mr-2 text-gray-500" />
                Plan Actual: {' '}
                <Badge variant="default" className="ml-2 text-sm">
                  {(() => {
                    // Intentamos obtener el plan primero desde accountData, luego desde usageSummary
                    const planType = accountData?.subscription?.plan || 
                                    (usageSummary ? usageSummary.subscription.plan : null);
                    
                    return plans && planType ? plans[planType]?.name || planType : 'Desconocido';
                  })()}
                </Badge>
              </CardTitle>
              <CardDescription>
                Estado: {usageSummary && usageSummary.subscription.is_active ?
                  <span className="text-green-600">Activo</span> :
                  <span className="text-red-600">Inactivo</span>}
              </CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
              <BillingPortalButton
                className="transition-all duration-300 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 focus:ring-2 focus:ring-blue-200 focus:ring-offset-2"
              />
              {usageSummary && usageSummary.billing && usageSummary.billing.upgrade_available && (
                <BillingButton
                  priceId={plans && plans["PRO"]?.mercadopago_price_id || ""}
                  planName="Pro"
                  variant="default"
                  className="transition-all duration-300 hover:shadow-md focus:ring-2 focus:ring-blue-200 focus:ring-offset-2"
                >
                  <ArrowUpIcon className="h-4 w-4 mr-1" />
                  Mejorar Plan
                </BillingButton>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Mostrar alertas para errores no críticos */}
      {(accountError || usageError || historyError) && !hasCriticalErrors() && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>Algunos datos no pudieron cargarse correctamente</AlertTitle>
          <AlertDescription>
            <p>Estamos mostrando la información disponible, pero algunos datos podrían estar incompletos o desactualizados.</p>
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              className="mt-2"
              disabled={isRefreshing}
            >
              {isRefreshing ? "Actualizando..." : "Reintentar"}
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h1 className="text-2xl font-bold">Uso de API</h1>
        <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
          <div className="flex items-center gap-2 text-xs text-gray-500 mr-2">
            <ClockIcon className="h-3.5 w-3.5" />
            <span>
              Actualizado: {getLastUpdateTime()}
            </span>
          </div>
          <DateRangeSelector
            onChange={handleDateRangeChange}
            className="flex-1"
          />
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            disabled={isRefreshing}
            className="transition-all duration-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 focus:ring-2 focus:ring-blue-200 focus:ring-offset-2"
          >
            {isRefreshing ? (
              <>
                <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                Actualizando...
              </>
            ) : (
              <>
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Actualizar
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Tarjeta de Llamadas a la API */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-blue-100 dark:hover:border-blue-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <BarChart3Icon className="h-5 w-5 mr-2 text-blue-500" />
              Llamadas a la API
              {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && plans[usageSummary.subscription.plan]?.limits.api_calls && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="ml-2 cursor-help">
                        Límite: {formatNumber(plans[usageSummary.subscription.plan]?.limits.api_calls || 0)}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Límite máximo de llamadas a la API según tu plan actual</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </CardTitle>
            <CardDescription>
              {dateRange.from && dateRange.to ? (
                <>Solicitudes entre {format(dateRange.from, 'dd/MM/yy')} y {format(dateRange.to, 'dd/MM/yy')}</>
              ) : (
                'Total de solicitudes realizadas'
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-center py-2 text-blue-600 dark:text-blue-400">
              {totals.apiCalls.toLocaleString()}
            </div>
            {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && (
              <div className="mt-2">
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Total del periodo</span>
                  <div className="flex items-center">
                    <span className="font-medium">{totals.apiCalls.toLocaleString()}</span>
                    {plans && plans[usageSummary.subscription.plan]?.limits.api_calls && (
                      <>
                        <span className="mx-1">/</span>
                        <span>{formatNumber(plans[usageSummary.subscription.plan]?.limits.api_calls)}</span>
                      </>
                    )}
                    {(!plans || !plans[usageSummary.subscription.plan]?.limits.api_calls) && (
                      <span className="ml-1">/ ∞</span>
                    )}
                  </div>
                </div>
                <Progress
                  value={(() => {
                    if (!plans || !usageSummary || !usageSummary.subscription.plan) return 0;
                    const limit = plans[usageSummary.subscription.plan]?.limits.api_calls;
                    if (!limit || limit <= 0) return 0;
                    return Math.min((totals.apiCalls / limit) * 100, 100);
                  })()}
                  className="h-1.5"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-2">
                  <span>Ciclo actual</span>
                  <div className="flex items-center">
                    <span className="font-medium">{formatNumber(usageSummary.api_calls.used)}</span>
                    {usageSummary.api_calls.limit > 0 && (
                      <>
                        <span className="mx-1">/</span>
                        <span>{formatNumber(usageSummary.api_calls.limit)}</span>
                      </>
                    )}
                    {usageSummary.api_calls.limit <= 0 && (
                      <span className="ml-1">/ ∞</span>
                    )}
                  </div>
                </div>
                <Progress
                  value={(() => {
                    if (!usageSummary.api_calls.limit || usageSummary.api_calls.limit <= 0) return 0;
                    return Math.min((usageSummary.api_calls.used / usageSummary.api_calls.limit) * 100, 100);
                  })()}
                  className="h-1.5"
                />
                <div className="text-xs text-gray-500 mt-2 text-right">
                  Próximo reinicio: {formatDate(usageSummary.api_calls.next_reset)}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tarjeta de Almacenamiento Utilizado */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-green-100 dark:hover:border-green-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <DatabaseIcon className="h-5 w-5 mr-2 text-green-500" />
              Almacenamiento
              {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && plans[usageSummary.subscription.plan]?.limits.storage_bytes && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="ml-2 cursor-help">
                        Límite: {formatBytes(plans[usageSummary.subscription.plan]?.limits.storage_bytes || 0)}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Límite máximo de almacenamiento según tu plan actual</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </CardTitle>
            <CardDescription>
              Espacio utilizado al {dateRange.to ? format(dateRange.to, 'dd/MM/yy') : 'final del periodo'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-center py-2 text-green-600 dark:text-green-400">
              {formatBytes(usageSummary.storage.used_bytes || 0)}
            </div>
            {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && (
              <div className="mt-2">
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Uso actual</span>
                  <div className="flex items-center">
                    <span className="font-medium">{formatBytes(usageSummary.storage.used_bytes)}</span>
                    {plans && plans[usageSummary.subscription.plan]?.limits.storage_bytes > 0 && (
                      <>
                        <span className="mx-1">/</span>
                        <span>{formatBytes(plans[usageSummary.subscription.plan]?.limits.storage_bytes)}</span>
                      </>
                    )}
                    {(!plans || !plans[usageSummary.subscription.plan]?.limits.storage_bytes || plans[usageSummary.subscription.plan]?.limits.storage_bytes <= 0) && (
                      <span className="ml-1">/ ∞</span>
                    )}
                  </div>
                </div>
                <Progress
                  value={(() => {
                    if (!plans || !usageSummary || !usageSummary.subscription.plan) return 0;
                    const limit = plans[usageSummary.subscription.plan]?.limits.storage_bytes;
                    if (!limit || limit <= 0) return 0;
                    return Math.min((usageSummary.storage.used_bytes / limit) * 100, 100);
                  })()}
                  className="h-1.5"
                />
                <div className="flex justify-between items-center text-xs text-gray-500 mt-2">
                  <div className="flex items-center">
                    <ClockIcon className="h-3 w-3 mr-1" />
                    <span>Última medición:</span>
                  </div>
                  <span className="font-medium">{usageSummary.storage.last_measured ? format(parseISO(usageSummary.storage.last_measured), 'dd/MM/yy HH:mm') : 'No disponible'}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tarjeta de Entrenamiento */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-purple-100 dark:hover:border-purple-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <BrainIcon className="h-5 w-5 mr-2 text-purple-500" />
              Entrenamiento
              {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && plans[usageSummary.subscription.plan]?.limits.training_frequency && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="ml-2 cursor-help">
                        {plans[usageSummary.subscription.plan]?.limits.training_frequency}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Frecuencia de entrenamiento permitida según tu plan</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </CardTitle>
            <CardDescription>
              Información sobre el entrenamiento del modelo
            </CardDescription>
          </CardHeader>
          <CardContent>
            {usageSummary && usageSummary.training && (
              <>
                <div className="flex flex-col space-y-3 py-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <ClockIcon className="h-4 w-4 mr-1.5 text-purple-500" />
                      <span className="text-gray-600 dark:text-gray-400">Último entrenamiento:</span>
                    </div>
                    <span className="font-medium text-sm">
                      {usageSummary.training.last_training_date ?
                        format(parseISO(usageSummary.training.last_training_date), 'dd/MM/yy HH:mm') :
                        'Nunca'}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <TrendingUpIcon className="h-4 w-4 mr-1.5 text-purple-500" />
                      <span className="text-gray-600 dark:text-gray-400">Frecuencia:</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-sm">{usageSummary.training.frequency_limit || 'Manual'}</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircleIcon className="h-3.5 w-3.5 ml-1 text-gray-400 cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">Frecuencia con la que puedes entrenar tu modelo</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <ClockIcon className="h-4 w-4 mr-1.5 text-purple-500" />
                      <span className="text-gray-600 dark:text-gray-400">Próximo disponible:</span>
                    </div>
                    <span className="font-medium text-sm">
                      {usageSummary.training.next_training_available ?
                        format(parseISO(usageSummary.training.next_training_available), 'dd/MM/yy HH:mm') :
                        'Ahora'}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <DatabaseIcon className="h-4 w-4 mr-1.5 text-purple-500" />
                      <span className="text-gray-600 dark:text-gray-400">Límite de datos:</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-sm">
                        {usageSummary.training.training_data_limit_formatted || 'Ilimitado'}
                      </span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircleIcon className="h-3.5 w-3.5 ml-1 text-gray-400 cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">Cantidad máxima de datos que puedes usar para entrenar</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <InfoIcon className="h-4 w-4 mr-1.5 text-purple-500" />
                      <span className="text-gray-600 dark:text-gray-400">Estado:</span>
                    </div>
                    <Badge
                      variant={usageSummary.training.can_train_now ? "default" : "outline"}
                      className={usageSummary.training.can_train_now ? "bg-green-500" : ""}
                    >
                      {usageSummary.training.can_train_now ? 'Disponible' : 'No disponible'}
                    </Badge>
                  </div>
                </div>
              </>
            )}
            {(!usageSummary || !usageSummary.training) && (
              <div className="text-center py-4 text-gray-500">
                <InfoIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Información de entrenamiento no disponible</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Información del Plan */}
      <Card className="mb-8 transition-all duration-300 hover:shadow-md">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center text-lg">
            <InfoIcon className="h-5 w-5 mr-2 text-gray-500" />
            Detalles del Plan
          </CardTitle>
          <CardDescription>
            Características y límites de tu suscripción actual
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm text-gray-500 dark:text-gray-400">Modelos Disponibles</div>
              <div className="text-lg font-semibold mt-1">
                {usageSummary && usageSummary.features && usageSummary.features.available_models ?
                  usageSummary.features.available_models.length :
                  (plans && plans[usageSummary.subscription.plan]?.limits.available_models?.length || 0)}
              </div>
              {usageSummary && usageSummary.features && usageSummary.features.available_models && (
                <div className="text-xs text-gray-500 mt-1">
                  {usageSummary.features.available_models.join(', ')}
                </div>
              )}
            </div>
            <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm text-gray-500 dark:text-gray-400">Solicitudes/Minuto</div>
              <div className="text-lg font-semibold mt-1">
                {usageSummary && usageSummary.features ?
                  usageSummary.features.max_requests_per_minute :
                  (plans && plans[usageSummary.subscription.plan]?.limits.max_requests_per_minute || 'Ilimitado')}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Solicitudes concurrentes: {usageSummary && usageSummary.features ?
                  usageSummary.features.max_concurrent_requests :
                  (plans && plans[usageSummary.subscription.plan]?.limits.max_concurrent_requests || 'N/A')}
              </div>
            </div>
            <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm text-gray-500 dark:text-gray-400">Máximo de Productos</div>
              <div className="text-lg font-semibold mt-1">
                {usageSummary && usageSummary.features ?
                  (usageSummary.features.max_items === 0 ? 'Ilimitado' : formatNumber(usageSummary.features.max_items)) :
                  (plans && plans[usageSummary.subscription.plan]?.limits.max_items_formatted || 'Ilimitado')}
              </div>
            </div>
            <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm text-gray-500 dark:text-gray-400">Máximo de Usuarios</div>
              <div className="text-lg font-semibold mt-1">
                {usageSummary && usageSummary.features ?
                  (usageSummary.features.max_users === 0 ? 'Ilimitado' : formatNumber(usageSummary.features.max_users)) :
                  (plans && plans[usageSummary.subscription.plan]?.limits.max_users_formatted || 'Ilimitado')}
              </div>
            </div>
          </div>

          {/* Información de facturación */}
          {usageSummary && usageSummary.billing && (
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <h3 className="text-sm font-medium mb-2">Información de Facturación</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="text-sm">
                  <span className="text-gray-500">Estado de pago: </span>
                  <Badge variant={usageSummary.billing.has_payment_method ? "default" : "outline"} className={usageSummary.billing.has_payment_method ? "bg-green-500" : ""}>
                    {usageSummary.billing.has_payment_method ? 'Configurado' : 'No configurado'}
                  </Badge>
                </div>
                {usageSummary.subscription && usageSummary.subscription.expires_at && (
                  <div className="text-sm">
                    <span className="text-gray-500">Próxima renovación: </span>
                    <span>{formatDate(usageSummary.subscription.expires_at)}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Gráfico de uso */}
      <UsageChart
        data={filteredData}
        isLoading={isHistoryLoading}
        error={historyError}
        title="Historial de Uso"
        description={dateRange.from && dateRange.to ?
          `Estadísticas de uso entre ${format(dateRange.from, 'dd/MM/yy')} y ${format(dateRange.to, 'dd/MM/yy')}` :
          "Estadísticas de uso en el periodo seleccionado"}
        apiCallsLimit={usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans ? plans[usageSummary.subscription.plan]?.limits.api_calls : undefined}
        storageLimit={usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans ? plans[usageSummary.subscription.plan]?.limits.storage_bytes : undefined}
      />

      {/* Tendencias e Insights */}
      {filteredData.length > 0 && (
        <Card className="mb-8 transition-all duration-300 hover:shadow-md">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <TrendingUpIcon className="h-5 w-5 mr-2 text-indigo-500" />
              Tendencias e Insights
            </CardTitle>
            <CardDescription>
              Análisis de patrones de uso y recomendaciones
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Tendencia de uso */}
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Tendencia de Uso</h3>
                {(() => {
                  // Calcular tendencia comparando primera y última semana
                  if (filteredData.length >= 14) {
                    const firstWeek = filteredData.slice(0, 7);
                    const lastWeek = filteredData.slice(-7);

                    const firstWeekCalls = firstWeek.reduce((sum, item) => sum + item.apiCalls, 0);
                    const lastWeekCalls = lastWeek.reduce((sum, item) => sum + item.apiCalls, 0);

                    const percentChange = firstWeekCalls > 0
                      ? ((lastWeekCalls - firstWeekCalls) / firstWeekCalls) * 100
                      : 0;

                    const isIncreasing = percentChange > 0;
                    const isSignificant = Math.abs(percentChange) > 10;

                    return (
                      <div className="flex items-center">
                        <div className={`p-2 rounded-full ${isIncreasing ? 'bg-green-100 text-green-600' : 'bg-amber-100 text-amber-600'} mr-3`}>
                          {isIncreasing ? <ArrowUpIcon className="h-5 w-5" /> : <TrendingUpIcon className="h-5 w-5" />}
                        </div>
                        <div>
                          <p className="text-sm">
                            {isIncreasing
                              ? `Aumento del ${Math.abs(percentChange).toFixed(1)}% en llamadas API`
                              : `Reducción del ${Math.abs(percentChange).toFixed(1)}% en llamadas API`}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            Comparando la primera y última semana del periodo seleccionado
                          </p>
                        </div>
                      </div>
                    );
                  }

                  return (
                    <p className="text-sm text-gray-500">
                      Se necesitan al menos 14 días de datos para mostrar tendencias
                    </p>
                  );
                })()}
              </div>

              {/* Recomendaciones */}
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Recomendaciones</h3>
                {(() => {
                  // Generar recomendaciones basadas en el uso
                  if (usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans) {
                    const plan = plans[usageSummary.subscription.plan];
                    const apiUsagePercent = (usageSummary.api_calls.used / usageSummary.api_calls.limit) * 100;
                    const storageUsagePercent = (usageSummary.storage.used_bytes / plan?.limits.storage_bytes) * 100;

                    if (apiUsagePercent > 80) {
                      return (
                        <div className="text-sm">
                          <p className="text-amber-600 font-medium">Alto uso de API</p>
                          <p className="text-gray-600 dark:text-gray-400 mt-1">
                            Estás utilizando más del 80% de tu límite de llamadas API. Considera actualizar tu plan para evitar interrupciones.
                          </p>
                        </div>
                      );
                    } else if (storageUsagePercent > 80) {
                      return (
                        <div className="text-sm">
                          <p className="text-amber-600 font-medium">Alto uso de almacenamiento</p>
                          <p className="text-gray-600 dark:text-gray-400 mt-1">
                            Estás utilizando más del 80% de tu límite de almacenamiento. Considera actualizar tu plan o revisar tus datos.
                          </p>
                        </div>
                      );
                    } else {
                      return (
                        <div className="text-sm">
                          <p className="text-green-600 font-medium">Uso óptimo</p>
                          <p className="text-gray-600 dark:text-gray-400 mt-1">
                            Tu uso actual está dentro de los límites de tu plan. Continúa monitoreando para asegurar un rendimiento óptimo.
                          </p>
                        </div>
                      );
                    }
                  }

                  return (
                    <p className="text-sm text-gray-500">
                      No hay suficientes datos para generar recomendaciones
                    </p>
                  );
                })()}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Nota informativa */}
      <div className="mt-8 text-sm text-gray-500 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg border border-gray-100 dark:border-gray-700">
        <div className="flex items-start">
          <InfoIcon className="h-5 w-5 mr-2 text-blue-500 shrink-0 mt-0.5" />
          <div>
            <p className="mb-1 font-medium text-gray-700 dark:text-gray-300">Acerca de estos datos</p>
            <p>
              Estos datos representan el uso actual de tu cuenta. Las métricas se actualizan periódicamente.
              Para reportes históricos más detallados o análisis personalizados, contacta con nuestro equipo de soporte.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
