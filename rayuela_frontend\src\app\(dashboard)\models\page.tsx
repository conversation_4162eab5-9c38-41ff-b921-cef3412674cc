"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, InfoIcon, ExternalLinkIcon, CheckIcon, ZapIcon, BarChartIcon, TrendingUpIcon } from "lucide-react";
import Link from "next/link";
import { useAuth, usePlans, useUsageSummary } from "@/lib/hooks";
import { AccountUsage } from '@/lib/api';

export default function ModelsPage() {
  const { token, apiKey } = useAuth();
  const { plans, isLoading: plansLoading } = usePlans();

  // Usar el hook personalizado para obtener datos de uso
  const {
    usageData: usage,
    error: usageError,
    isLoading
  } = useUsageSummary();

  // Estado para el error
  const [error, setError] = useState<string | null>(null);

  // Actualizar el estado de error cuando cambia usageError
  useEffect(() => {
    if (usageError) {
      console.error("Error al obtener datos de uso:", usageError);
      setError(usageError.message || "Error al cargar los datos de uso");
    } else {
      setError(null);
    }
  }, [usageError]);

  // Función para renderizar los modelos disponibles
  const renderAvailableModels = () => {
    if (isLoading || plansLoading) {
      return (
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-red-500 p-4 border border-red-200 rounded-md bg-red-50 dark:bg-red-900/20 dark:border-red-800">
          {error}
        </div>
      );
    }

    if (!usage || !usage.features || !usage.features.available_models) {
      return (
        <div className="text-gray-500 dark:text-gray-400 p-4 border border-gray-200 rounded-md dark:border-gray-700">
          No se encontraron modelos disponibles para tu plan.
        </div>
      );
    }

    // Definir información de los modelos
    const modelInfo = {
      "similares": {
        name: "Similares",
        description: "Recomienda productos similares basados en características y comportamiento de usuario",
        icon: <ZapIcon className="h-5 w-5 text-blue-500" />
      },
      "mas_vistos": {
        name: "Más Vistos",
        description: "Recomienda productos populares basados en vistas y engagement",
        icon: <BarChartIcon className="h-5 w-5 text-green-500" />
      },
      "tendencias": {
        name: "Tendencias",
        description: "Identifica productos con crecimiento rápido en popularidad",
        icon: <TrendingUpIcon className="h-5 w-5 text-purple-500" />
      }
    };

    return (
      <div className="space-y-4">
        {usage.features.available_models.map((modelId) => {
          const model = modelInfo[modelId as keyof typeof modelInfo];
          if (!model) return null;

          return (
            <div key={modelId} className="p-4 border border-gray-200 dark:border-gray-700 rounded-md flex items-start hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div className="mr-4 mt-1">
                {model.icon}
              </div>
              <div className="flex-grow">
                <h3 className="font-medium text-gray-900 dark:text-white flex items-center">
                  {model.name}
                  <Badge className="ml-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                    Activo
                  </Badge>
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {model.description}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Función para mostrar información del plan actual
  const renderPlanInfo = () => {
    if (isLoading || plansLoading) {
      return <Skeleton className="h-6 w-48 mb-2" />;
    }

    if (!usage || !usage.subscription) {
      return null;
    }

    const planName = plans && plans[usage.subscription.plan]?.name || usage.subscription.plan;

    return (
      <div className="mb-4">
        <div className="flex items-center">
          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Plan actual:</span>
          <Badge className="ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
            {planName}
          </Badge>
        </div>
      </div>
    );
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800 dark:text-white">
        Models
      </h1>
      <p className="text-gray-600 dark:text-gray-400 mb-4">
        Manage and configure your recommendation models.
      </p>

      <Alert className="mb-8 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800">
        <InfoIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
        <AlertTitle className="text-blue-800 dark:text-blue-300">Próximamente</AlertTitle>
        <AlertDescription className="text-blue-700 dark:text-blue-400">
          <p>La gestión avanzada de modelos estará disponible en nuestra próxima actualización, prevista para el Q3 2023.</p>
          <p className="mt-2">Esta sección te permitirá:</p>
          <ul className="list-disc list-inside mt-1 ml-2 space-y-1">
            <li>Entrenar modelos personalizados con tus propios datos</li>
            <li>Ajustar parámetros de recomendación</li>
            <li>Monitorear el rendimiento de tus modelos</li>
            <li>Implementar A/B testing entre diferentes modelos</li>
          </ul>
        </AlertDescription>
      </Alert>

      {/* Placeholder content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Modelos Disponibles</CardTitle>
            <CardDescription>
              Modelos que puedes usar para generar recomendaciones
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderPlanInfo()}
            {renderAvailableModels()}
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" size="sm">
              <a
                href="https://docs.rayuela.ai/models/overview"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center"
              >
                <ExternalLinkIcon className="mr-2 h-4 w-4" />
                Ver documentación de modelos
              </a>
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Custom Models</CardTitle>
            <CardDescription>
              Your trained custom recommendation models
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center h-40 border-2 border-dashed rounded-md border-gray-300 dark:border-gray-700 p-4">
              <CalendarIcon className="h-8 w-8 text-gray-400 dark:text-gray-500 mb-2" />
              <p className="text-gray-500 dark:text-gray-400 text-center">
                Entrenamiento de modelos personalizados disponible en Q3 2023.
              </p>
              <Button variant="outline" size="sm" className="mt-4" asChild>
                <Link href="https://docs.rayuela.ai/models" target="_blank" rel="noopener noreferrer">
                  <ExternalLinkIcon className="mr-2 h-4 w-4" />
                  Ver documentación
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
