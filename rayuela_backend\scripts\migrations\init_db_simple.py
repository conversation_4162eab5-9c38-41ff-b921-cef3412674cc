#!/usr/bin/env python
"""
Script simplificado para inicializar la base de datos.
Este script crea la base de datos si no existe y ejecuta las migraciones.
"""

import os
import sys
import asyncio
import asyncpg
import argparse
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.core.config import settings
from src.utils.base_logger import logger


async def create_database_if_not_exists():
    """Crea la base de datos principal si no existe."""
    conn = None
    try:
        # Conectar a la base de datos postgres para poder crear otras bases de datos
        conn = await asyncpg.connect(
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD,
            host=settings.POSTGRES_SERVER,
            port=settings.POSTGRES_PORT,
            database="postgres",  # Conectar a 'postgres' para crear otra DB
        )

        # Verificar si la base de datos ya existe
        exists = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1", settings.POSTGRES_DB
        )

        if not exists:
            logger.info(f"Database '{settings.POSTGRES_DB}' not found. Creating...")
            await conn.execute(f'CREATE DATABASE "{settings.POSTGRES_DB}"')
            logger.info(f"Database '{settings.POSTGRES_DB}' created successfully.")
        else:
            logger.info(f"Database '{settings.POSTGRES_DB}' already exists.")

        return True
    except Exception as e:
        logger.error(f"Error creating database: {e}", exc_info=True)
        return False
    finally:
        if conn:
            await conn.close()


async def run_migrations():
    """Ejecuta las migraciones manualmente."""
    try:
        # Crear conexión a la base de datos
        engine = create_async_engine(settings.database_url)

        # Lista de scripts SQL para crear las tablas básicas
        # Esto es una versión simplificada y no reemplaza a Alembic
        # pero puede ser útil para pruebas locales rápidas

        # Eliminar tablas existentes
        drop_interactions = "DROP TABLE IF EXISTS interactions CASCADE"
        drop_end_users = "DROP TABLE IF EXISTS end_users CASCADE"
        drop_products = "DROP TABLE IF EXISTS products CASCADE"
        drop_accounts = "DROP TABLE IF EXISTS accounts CASCADE"

        # Crear tabla de cuentas
        accounts_table = """
        CREATE TABLE IF NOT EXISTS accounts (
            account_id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        );
        """

        # Crear tabla de productos (versión simplificada)
        products_table = """
        CREATE TABLE IF NOT EXISTS products (
            product_id VARCHAR(255) NOT NULL,
            account_id INTEGER NOT NULL,
            name VARCHAR(255) NOT NULL,
            category VARCHAR(255),
            description TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (product_id, account_id),
            FOREIGN KEY (account_id) REFERENCES accounts(account_id)
        )
        """

        # Crear índices para productos
        products_name_index = """
        CREATE INDEX IF NOT EXISTS idx_product_name ON products(name)
        """

        products_category_index = """
        CREATE INDEX IF NOT EXISTS idx_product_category ON products(category)
        """

        # Crear tabla de usuarios finales
        end_users_table = """
        CREATE TABLE IF NOT EXISTS end_users (
            user_id VARCHAR(255) NOT NULL,
            account_id INTEGER NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, account_id),
            FOREIGN KEY (account_id) REFERENCES accounts(account_id)
        );
        """

        # Crear tabla de interacciones
        interactions_table = """
        CREATE TABLE IF NOT EXISTS interactions (
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            account_id INTEGER NOT NULL,
            interaction_type VARCHAR(50) NOT NULL,
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (account_id) REFERENCES accounts(account_id),
            FOREIGN KEY (product_id, account_id) REFERENCES products(product_id, account_id),
            FOREIGN KEY (user_id, account_id) REFERENCES end_users(user_id, account_id)
        );
        """

        # Ejecutar los scripts SQL
        async with engine.begin() as conn:
            # Eliminar tablas existentes
            logger.info("Dropping existing tables...")
            await conn.execute(text(drop_interactions))
            await conn.execute(text(drop_end_users))
            await conn.execute(text(drop_products))
            await conn.execute(text(drop_accounts))

            logger.info("Creating accounts table...")
            await conn.execute(text(accounts_table))

            logger.info("Creating products table...")
            await conn.execute(text(products_table))

            # Crear índices para productos
            logger.info("Creating product indexes...")
            await conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_product_name ON products(name)
            """))

            await conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_product_category ON products(category)
            """))

            logger.info("Creating end_users table...")
            await conn.execute(text(end_users_table))

            logger.info("Creating interactions table...")
            await conn.execute(text(interactions_table))

        logger.info("Database tables created successfully.")
        return True
    except Exception as e:
        logger.error(f"Error running migrations: {e}", exc_info=True)
        return False
    finally:
        await engine.dispose()


async def insert_test_data():
    """Inserta datos de prueba en la base de datos."""
    try:
        # Crear conexión a la base de datos
        engine = create_async_engine(settings.database_url)

        # Insertar cuenta de prueba
        insert_account = """
        INSERT INTO accounts (name, email)
        VALUES ('Test Company', '<EMAIL>')
        ON CONFLICT (email) DO NOTHING
        RETURNING account_id;
        """

        async with engine.begin() as conn:
            logger.info("Inserting test account...")
            result = await conn.execute(text(insert_account))
            account_id = result.scalar()

            if not account_id:
                # Si no se insertó una nueva cuenta, obtener el ID de la cuenta existente
                get_account_id = """
                SELECT account_id FROM accounts WHERE email = '<EMAIL>';
                """
                result = await conn.execute(text(get_account_id))
                account_id = result.scalar()

            logger.info(f"Test account ID: {account_id}")

            # Insertar productos de prueba
            insert_products = f"""
            INSERT INTO products (product_id, account_id, name, category)
            VALUES
                ('p1', {account_id}, 'Product 1', 'Category A'),
                ('p2', {account_id}, 'Product 2', 'Category B'),
                ('p3', {account_id}, 'Product 3', 'Category A')
            ON CONFLICT (product_id, account_id) DO NOTHING;
            """

            logger.info("Inserting test products...")
            await conn.execute(text(insert_products))

            # Insertar usuarios finales de prueba
            insert_users = f"""
            INSERT INTO end_users (user_id, account_id)
            VALUES
                ('u1', {account_id}),
                ('u2', {account_id}),
                ('u3', {account_id})
            ON CONFLICT (user_id, account_id) DO NOTHING;
            """

            logger.info("Inserting test users...")
            await conn.execute(text(insert_users))

            # Insertar interacciones de prueba
            insert_interactions = f"""
            INSERT INTO interactions (user_id, product_id, account_id, interaction_type)
            VALUES
                ('u1', 'p1', {account_id}, 'view'),
                ('u1', 'p2', {account_id}, 'purchase'),
                ('u2', 'p1', {account_id}, 'view'),
                ('u2', 'p3', {account_id}, 'view'),
                ('u3', 'p2', {account_id}, 'purchase')
            ON CONFLICT DO NOTHING;
            """

            logger.info("Inserting test interactions...")
            await conn.execute(text(insert_interactions))

        logger.info("Test data inserted successfully.")
        return True
    except Exception as e:
        logger.error(f"Error inserting test data: {e}", exc_info=True)
        return False
    finally:
        await engine.dispose()


async def initialize_database():
    """Inicializa la base de datos y carga datos de prueba."""
    try:
        # Crear la base de datos si no existe
        db_created = await create_database_if_not_exists()
        if not db_created:
            logger.error("Failed to create database.")
            return False

        # Ejecutar migraciones
        migrations_success = await run_migrations()
        if not migrations_success:
            logger.error("Failed to run migrations.")
            return False

        # Insertar datos de prueba
        data_inserted = await insert_test_data()
        if not data_inserted:
            logger.warning("Failed to insert test data, but continuing...")

        logger.info("Database initialization completed successfully.")
        return True
    except Exception as e:
        logger.error(f"Error initializing database: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    # Parsear argumentos de línea de comandos
    parser = argparse.ArgumentParser(description="Inicializar base de datos para Rayuela")
    parser.add_argument("--db-name", type=str, default=None, help="Nombre de la base de datos a crear")
    args = parser.parse_args()

    # Si se especificó un nombre de base de datos, actualizar la configuración
    if args.db_name:
        settings.POSTGRES_DB = args.db_name
        logger.info(f"Usando base de datos: {args.db_name}")

    logger.info("Initializing database...")
    success = asyncio.run(initialize_database())
    if success:
        logger.info("Database initialization completed successfully.")
        sys.exit(0)
    else:
        logger.error("Database initialization failed.")
        sys.exit(1)
