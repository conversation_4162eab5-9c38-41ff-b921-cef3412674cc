"""
Tests comprehensivos para el TenantMiddleware.
Verifica que el middleware establece correctamente el account_id en el contexto y en la sesión de DB.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from fastapi import FastAPI, Request, Response, HTTPException
from fastapi.testclient import TestClient
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import contextvars
from sqlalchemy.ext.asyncio import AsyncSession

from src.middleware.tenant import (
    TenantMiddleware,
)
from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id
from src.db.session import get_current_account_id, set_current_account_id, get_db
from src.core.deps import get_current_account
from src.db.models import Account, SystemUser
from main import app

# Mock current_tenant_ctx
current_tenant_ctx = MagicMock()

class TestTenantMiddlewareComprehensive:
    """Tests comprehensivos para validar el TenantMiddleware."""

    @pytest.fixture
    def test_app(self):
        """Crear aplicación FastAPI de prueba con TenantMiddleware."""
        app = FastAPI()
        app.add_middleware(TenantMiddleware)

        @app.get("/test-endpoint")
        async def test_endpoint():
            return {
                "tenant_id": get_current_tenant_id(),
                "account_id": get_current_account_id(),
                "message": "success",
            }

        @app.get("/api/v1/products")
        async def protected_endpoint():
            return {
                "tenant_id": get_current_tenant_id(),
                "account_id": get_current_account_id(),
                "products": [],
            }

        @app.get("/api/v1/auth/login")
        async def auth_endpoint():
            return {"message": "auth endpoint"}

        @app.get("/api/v1/health")
        async def health_endpoint():
            return {"status": "healthy"}

        @app.get("/docs")
        async def docs_endpoint():
            return {"message": "documentation"}

        return app

    @pytest.fixture
    def test_account(self):
        """Crear cuenta de prueba."""
        return Account(
            account_id=123,
            name="Test Account",
            email="<EMAIL>",
            status="active",
        )

    async def test_middleware_sets_tenant_context_with_valid_account(
        self, test_app, test_account
    ):
        """Test que el middleware establece el contexto de tenant con una cuenta válida."""
        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            mock_get_account.return_value = test_account

            with TestClient(test_app) as client:
                response = client.get("/test-endpoint")

                assert response.status_code == 200
                data = response.json()
                # Nota: En el cliente de prueba, el contexto se pierde después de la request
                # pero podemos verificar que el middleware fue llamado
                mock_get_account.assert_called_once()

    async def test_middleware_clears_tenant_context_without_account(self, test_app):
        """Test que el middleware limpia el contexto cuando no hay cuenta."""
        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            mock_get_account.return_value = None

            with TestClient(test_app) as client:
                response = client.get("/test-endpoint")

                assert response.status_code == 200
                mock_get_account.assert_called_once()

    async def test_middleware_skips_health_and_docs_endpoints(self, test_app):
        """Test que el middleware omite endpoints de health y documentación."""
        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            with TestClient(test_app) as client:
                # Test health endpoint
                response = client.get("/api/v1/health")
                assert response.status_code == 200
                mock_get_account.assert_not_called()

                # Test docs endpoint
                response = client.get("/docs")
                assert response.status_code == 200
                mock_get_account.assert_not_called()

    async def test_middleware_processes_auth_endpoints(self, test_app):
        """Test que el middleware procesa endpoints de auth pero no requiere cuenta."""
        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            mock_get_account.return_value = None

            with TestClient(test_app) as client:
                response = client.get("/api/v1/auth/login")
                assert response.status_code == 200
                mock_get_account.assert_called_once()

    async def test_middleware_logs_requests_with_tenant_id(
        self, test_app, test_account
    ):
        """Test que el middleware registra requests con tenant_id."""
        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            with patch("src.middleware.tenant.log_info") as mock_log_info:
                mock_get_account.return_value = test_account

                with TestClient(test_app) as client:
                    response = client.get("/api/v1/products")

                    assert response.status_code == 200
                    # Verificar que se loggea el establecimiento del tenant_id
                    mock_log_info.assert_called()

                    # Verificar que al menos una llamada incluye el tenant_id
                    log_calls = mock_log_info.call_args_list
                    tenant_logged = any(
                        "Tenant ID establecido a 123" in str(call) for call in log_calls
                    )
                    assert tenant_logged

    async def test_middleware_logs_warnings_for_protected_routes_without_account(
        self, test_app
    ):
        """Test que el middleware registra warnings para rutas protegidas sin cuenta."""
        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            with patch("src.middleware.tenant.log_warning") as mock_log_warning:
                mock_get_account.return_value = None

                with TestClient(test_app) as client:
                    response = client.get("/api/v1/products")

                    assert response.status_code == 200
                    mock_log_warning.assert_called()

                    # Verificar que el warning menciona la ruta protegida
                    warning_calls = mock_log_warning.call_args_list
                    protected_route_warning = any(
                        "ruta protegida" in str(call) for call in warning_calls
                    )
                    assert protected_route_warning

    async def test_middleware_handles_exceptions_gracefully(self, test_app):
        """Test que el middleware maneja excepciones gracefully."""
        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            with patch("src.middleware.tenant.log_error") as mock_log_error:
                # Simular excepción en get_current_account
                mock_get_account.side_effect = Exception("Account service error")

                with TestClient(test_app) as client:
                    # El middleware debería capturar la excepción y limpiar el contexto
                    with pytest.raises(Exception):
                        client.get("/api/v1/products")

                    mock_log_error.assert_called()

    async def test_context_variable_thread_isolation(self):
        """Test que las variables de contexto están aisladas por thread/request."""

        async def task_1():
            current_tenant_ctx.set(100)
            return get_current_tenant_id()

        async def task_2():
            current_tenant_ctx.set(200)
            return get_current_tenant_id()

        # Ejecutar tareas concurrentemente
        import asyncio

        results = await asyncio.gather(task_1(), task_2())

        # Cada tarea debería ver su propio valor
        assert 100 in results
        assert 200 in results

    async def test_get_current_tenant_id_returns_correct_value(self):
        """Test que get_current_tenant_id retorna el valor correcto."""
        # Sin valor establecido
        assert get_current_tenant_id() is None

        # Con valor establecido
        current_tenant_ctx.set(456)
        assert get_current_tenant_id() == 456

        # Limpiar contexto
        current_tenant_ctx.set(None)
        assert get_current_tenant_id() is None

    async def test_middleware_cleanup_in_finally_block(self, test_app, test_account):
        """Test que el middleware limpia el contexto en el bloque finally."""
        original_dispatch = TenantMiddleware.dispatch
        cleanup_called = False

        async def mock_dispatch(self, request, call_next):
            nonlocal cleanup_called
            try:
                # Establecer tenant_id
                current_tenant_ctx.set(test_account.account_id)
                set_current_account_id(test_account.account_id)

                response = await call_next(request)
                return response
            finally:
                # Verificar que se llama la limpieza
                set_current_account_id(None)
                current_tenant_ctx.set(None)
                cleanup_called = True

        with patch.object(TenantMiddleware, "dispatch", mock_dispatch):
            with TestClient(test_app) as client:
                response = client.get("/test-endpoint")
                assert response.status_code == 200
                assert cleanup_called

    async def test_middleware_tenant_propagation_in_request_cycle(
        self, test_app, test_account
    ):
        """Test que el tenant_id se propaga correctamente durante todo el ciclo de request."""
        captured_tenant_ids = []

        def capture_tenant_id():
            captured_tenant_ids.append(get_current_tenant_id())

        # Patch el endpoint para capturar tenant_id en diferentes momentos
        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            mock_get_account.return_value = test_account

            # Mock para interceptar cuando se establece el tenant_id
            with patch(
                "src.middleware.tenant.set_current_account_id"
            ) as mock_set_account:
                with patch(
                    "src.middleware.tenant.current_tenant_ctx.set"
                ) as mock_set_ctx:

                    with TestClient(test_app) as client:
                        response = client.get("/api/v1/products")

                        assert response.status_code == 200

                        # Verificar que se llamaron las funciones de establecimiento
                        mock_set_account.assert_called()
                        mock_set_ctx.assert_called()

                        # Verificar que se estableció el account_id correcto
                        calls = mock_set_account.call_args_list
                        account_id_calls = [call[0][0] for call in calls if call[0]]
                        assert test_account.account_id in account_id_calls

    async def test_middleware_concurrent_requests_isolation(self, test_app):
        """Test que requests concurrentes mantienen aislamiento de tenant."""

        # Crear múltiples cuentas para simular requests concurrentes
        accounts = [
            Account(
                account_id=100,
                name="Account 100",
                email="<EMAIL>",
                status="active",
            ),
            Account(
                account_id=200,
                name="Account 200",
                email="<EMAIL>",
                status="active",
            ),
            Account(
                account_id=300,
                name="Account 300",
                email="<EMAIL>",
                status="active",
            ),
        ]

        async def simulate_request(account):
            with patch("src.middleware.tenant.get_current_account") as mock_get_account:
                mock_get_account.return_value = account

                with TestClient(test_app) as client:
                    response = client.get("/test-endpoint")
                    return response.status_code

        # Ejecutar requests concurrentes
        import asyncio

        tasks = [simulate_request(account) for account in accounts]
        results = await asyncio.gather(*tasks)

        # Todos deberían completarse exitosamente
        assert all(status == 200 for status in results)

    async def test_middleware_db_session_integration(self, test_app, test_account):
        """Test integración del middleware con sesiones de base de datos."""

        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            with patch("src.db.session.set_current_account_id") as mock_set_db_account:
                with patch(
                    "src.db.session.get_current_account_id"
                ) as mock_get_db_account:

                    mock_get_account.return_value = test_account
                    mock_get_db_account.return_value = test_account.account_id

                    with TestClient(test_app) as client:
                        response = client.get("/api/v1/products")

                        assert response.status_code == 200

                        # Verificar que se estableció el account_id en la sesión de DB
                        mock_set_db_account.assert_called_with(test_account.account_id)

    async def test_middleware_respects_endpoint_patterns(self, test_app):
        """Test que el middleware respeta los patrones de endpoints configurados."""
        test_cases = [
            ("/api/v1/health", False),  # No debería procesar
            ("/docs", False),  # No debería procesar
            ("/api/v1/auth/login", True),  # Debería procesar
            ("/api/v1/products", True),  # Debería procesar
            ("/test-endpoint", True),  # Debería procesar
        ]

        with patch("src.middleware.tenant.get_current_account") as mock_get_account:
            mock_get_account.return_value = None

            for endpoint, should_process in test_cases:
                mock_get_account.reset_mock()

                with TestClient(test_app) as client:
                    response = client.get(endpoint)

                    if should_process:
                        mock_get_account.assert_called_once()
                    else:
                        mock_get_account.assert_not_called()
