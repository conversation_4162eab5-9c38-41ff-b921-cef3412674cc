#!/usr/bin/env python
"""
Script simplificado para probar Rayuela localmente.
Este script configura y ejecuta los componentes esenciales para probar Rayuela.
"""

import os
import sys
import subprocess
import time
import argparse
import atexit

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

# Lista para almacenar procesos que deben cerrarse al salir
processes = []

def cleanup():
    """Limpia los procesos al salir."""
    print("\nLimpiando procesos...")
    for p in processes:
        if p.poll() is None:  # Si el proceso sigue en ejecución
            try:
                p.terminate()
                time.sleep(0.5)
                if p.poll() is None:
                    p.kill()
            except Exception as e:
                print(f"Error al cerrar proceso: {e}")
    print("Limpieza completada.")

# Registrar la función de limpieza para ejecutarse al salir
atexit.register(cleanup)

def run_command(cmd, cwd=None, env=None, wait=True):
    """Ejecuta un comando y devuelve el proceso."""
    print(f"Ejecutando: {' '.join(cmd)}")

    # Usar el entorno actual si no se proporciona uno
    if env is None:
        env = os.environ.copy()

    # Ejecutar el comando
    process = subprocess.Popen(
        cmd,
        cwd=cwd or root_dir,
        env=env,
        stdout=subprocess.PIPE if wait else None,
        stderr=subprocess.PIPE if wait else None,
        text=True,
        shell=isinstance(cmd, str)
    )

    # Si wait es True, esperar a que termine el proceso
    if wait:
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            print(f"Error al ejecutar {cmd[0]}:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return None
        return stdout
    else:
        # Si wait es False, agregar el proceso a la lista para limpieza
        processes.append(process)
        return process

def start_docker_services():
    """Inicia los servicios de Docker."""
    print("Iniciando servicios de Docker...")

    # Verificar si docker-compose.simple.yml existe
    if os.path.exists(os.path.join(root_dir, "docker-compose.simple.yml")):
        cmd = ["docker-compose", "-f", "docker-compose.simple.yml", "up", "-d"]
    else:
        cmd = ["docker-compose", "up", "-d", "db", "redis"]

    result = run_command(cmd)
    if result is None:
        print("Error al iniciar servicios de Docker.")
        sys.exit(1)

    print("Servicios de Docker iniciados correctamente.")
    time.sleep(5)  # Esperar a que los servicios estén listos

def init_database():
    """Inicializa la base de datos."""
    print("Inicializando base de datos...")

    # Usar el script simplificado para inicializar la base de datos
    cmd = ["python", "-m", "scripts.init_db_simple"]
    result = run_command(cmd)
    if result is None:
        print("Error al inicializar la base de datos.")
        sys.exit(1)

    print("Base de datos inicializada correctamente.")

def start_api_server():
    """Inicia el servidor API."""
    print("Iniciando servidor API simplificado...")
    cmd = ["python", "-m", "scripts.run_api_simple"]
    process = run_command(cmd, wait=False)
    if process is None:
        print("Error al iniciar el servidor API.")
        sys.exit(1)
    print("Servidor API iniciado correctamente.")
    time.sleep(3)  # Esperar a que el servidor esté listo

def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Probar Rayuela localmente")
    parser.add_argument("--skip-docker", action="store_true", help="Omitir inicio de servicios Docker")
    parser.add_argument("--skip-db-init", action="store_true", help="Omitir inicialización de base de datos")
    parser.add_argument("--skip-api", action="store_true", help="Omitir inicio de servidor API")

    args = parser.parse_args()

    try:
        # Iniciar servicios de Docker
        if not args.skip_docker:
            start_docker_services()

        # Inicializar base de datos
        if not args.skip_db_init:
            init_database()

        # Iniciar servidor API
        if not args.skip_api:
            start_api_server()

        print("\nTodo el proceso se completó correctamente.")
        print("La API está disponible en http://localhost:8001")
        print("Documentación de la API: http://localhost:8001/api/docs")
        print("Presiona Ctrl+C para detener todos los servicios.")

        # Mantener el script en ejecución hasta que el usuario lo detenga
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\nDetención solicitada por el usuario.")
    finally:
        cleanup()

if __name__ == "__main__":
    main()
