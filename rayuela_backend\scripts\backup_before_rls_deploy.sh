#!/bin/bash

# Script de Backup Automático antes del Despliegue RLS
# =====================================================
# Este script realiza un backup completo de la base de datos
# antes de desplegar las políticas RLS en producción.

set -euo pipefail

# Configuración
BACKUP_DIR="/tmp/rayuela_backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="rayuela_pre_rls_backup_${TIMESTAMP}.sql"
SCHEMA_FILE="rayuela_schema_pre_rls_${TIMESTAMP}.sql"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Función para obtener configuración de base de datos
get_db_config() {
    # Obtener configuración desde el entorno de Python
    python3 -c "
from src.core.config import settings
import urllib.parse as urlparse

url = urlparse.urlparse(str(settings.database_url))
print(f'DB_HOST={url.hostname}')
print(f'DB_PORT={url.port or 5432}')
print(f'DB_NAME={url.path[1:]}')  # Remove leading /
print(f'DB_USER={url.username}')
print(f'DB_PASSWORD={url.password}')
"
}

# Función principal de backup
main() {
    log_info "🚀 Iniciando backup pre-RLS de base de datos..."
    
    # Crear directorio de backup si no existe
    mkdir -p "${BACKUP_DIR}"
    
    # Obtener configuración de base de datos
    log_info "📋 Obteniendo configuración de base de datos..."
    eval $(get_db_config)
    
    if [[ -z "${DB_HOST:-}" || -z "${DB_NAME:-}" || -z "${DB_USER:-}" ]]; then
        log_error "❌ No se pudo obtener la configuración de la base de datos"
        exit 1
    fi
    
    log_info "📊 Configuración de BD: ${DB_USER}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
    
    # Verificar conectividad
    log_info "🔍 Verificando conectividad con la base de datos..."
    if ! PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT 1;" > /dev/null 2>&1; then
        log_error "❌ No se puede conectar a la base de datos"
        exit 1
    fi
    
    log_info "✅ Conectividad verificada"
    
    # Backup completo de datos
    log_info "💾 Realizando backup completo de datos..."
    if PGPASSWORD="${DB_PASSWORD}" pg_dump \
        -h "${DB_HOST}" \
        -p "${DB_PORT}" \
        -U "${DB_USER}" \
        -d "${DB_NAME}" \
        --verbose \
        --no-owner \
        --no-privileges \
        --format=custom \
        --file="${BACKUP_DIR}/${BACKUP_FILE}"; then
        log_info "✅ Backup de datos completado: ${BACKUP_DIR}/${BACKUP_FILE}"
    else
        log_error "❌ Error en backup de datos"
        exit 1
    fi
    
    # Backup solo del esquema (para referencia rápida)
    log_info "📋 Realizando backup del esquema..."
    if PGPASSWORD="${DB_PASSWORD}" pg_dump \
        -h "${DB_HOST}" \
        -p "${DB_PORT}" \
        -U "${DB_USER}" \
        -d "${DB_NAME}" \
        --schema-only \
        --no-owner \
        --no-privileges \
        --file="${BACKUP_DIR}/${SCHEMA_FILE}"; then
        log_info "✅ Backup de esquema completado: ${BACKUP_DIR}/${SCHEMA_FILE}"
    else
        log_warning "⚠️ Error en backup de esquema (continuando...)"
    fi
    
    # Verificar estado actual de RLS
    log_info "🔍 Verificando estado actual de RLS..."
    RLS_COUNT=$(PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c \
        "SELECT COUNT(*) FROM pg_tables WHERE schemaname = 'public' AND rowsecurity = true;")
    
    log_info "📊 Tablas con RLS actualmente: ${RLS_COUNT// /}"
    
    # Verificar políticas RLS existentes
    POLICIES_COUNT=$(PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c \
        "SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public';")
    
    log_info "📊 Políticas RLS existentes: ${POLICIES_COUNT// /}"
    
    # Generar reporte de backup
    cat > "${BACKUP_DIR}/backup_report_${TIMESTAMP}.txt" << EOF
Rayuela RLS Pre-Deployment Backup Report
========================================

Fecha: $(date)
Base de datos: ${DB_HOST}:${DB_PORT}/${DB_NAME}
Usuario: ${DB_USER}

Archivos de backup:
- Datos completos: ${BACKUP_FILE}
- Esquema: ${SCHEMA_FILE}

Estado pre-RLS:
- Tablas con RLS: ${RLS_COUNT// /}
- Políticas RLS: ${POLICIES_COUNT// /}

Comandos de restauración:
========================

# Restaurar backup completo:
pg_restore -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} --clean --if-exists "${BACKUP_DIR}/${BACKUP_FILE}"

# Restaurar solo esquema:
psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} -f "${BACKUP_DIR}/${SCHEMA_FILE}"

IMPORTANTE: Este backup fue creado ANTES del despliegue RLS.
           Use solo en caso de emergencia para rollback.
EOF
    
    # Mostrar resumen
    log_info "📊 Resumen del backup:"
    echo "  📁 Directorio: ${BACKUP_DIR}"
    echo "  📄 Datos: ${BACKUP_FILE}"
    echo "  📋 Esquema: ${SCHEMA_FILE}"
    echo "  📝 Reporte: backup_report_${TIMESTAMP}.txt"
    
    # Verificar tamaños de archivos
    if [[ -f "${BACKUP_DIR}/${BACKUP_FILE}" ]]; then
        BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_FILE}" | cut -f1)
        log_info "💾 Tamaño del backup: ${BACKUP_SIZE}"
    fi
    
    log_info "🎉 Backup pre-RLS completado exitosamente!"
    log_info "🔒 Ahora es seguro proceder con el despliegue RLS"
    
    # Instrucciones para el usuario
    echo ""
    log_warning "📋 INSTRUCCIONES IMPORTANTES:"
    echo "   1. Guarde estos archivos en un lugar seguro"
    echo "   2. Verifique que el backup sea válido antes de continuar"
    echo "   3. En caso de problemas, use los comandos de restauración del reporte"
    echo ""
}

# Verificar dependencias
command -v psql >/dev/null 2>&1 || { log_error "psql no está instalado"; exit 1; }
command -v pg_dump >/dev/null 2>&1 || { log_error "pg_dump no está instalado"; exit 1; }

# Ejecutar función principal
main "$@" 