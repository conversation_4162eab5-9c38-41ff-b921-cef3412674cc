{"openapi": "3.1.0", "info": {"title": "<PERSON>uel<PERSON>", "version": "v1"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/auth/send-verification-email": {"post": {"tags": ["auth"], "summary": "Send Verification Email", "description": "Envía un email de verificación al usuario actual.", "operationId": "send_verification_email_api_v1_auth_send_verification_email_post", "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/auth/verify-email": {"get": {"tags": ["auth"], "summary": "<PERSON><PERSON><PERSON>", "description": "Verifica el email de un usuario usando un token.", "operationId": "verify_email_api_v1_auth_verify_email_get", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string", "title": "Token"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/register": {"post": {"tags": ["auth"], "summary": "Register a new account", "description": "Creates a new account with global email uniqueness validation and returns a JWT token and the first API Key", "operationId": "register_api_v1_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/token": {"post": {"tags": ["auth"], "summary": "Login to obtain JWT token", "description": "Authenticates a user and returns a JWT token for dashboard access. Accepts JSON payload with email and password.", "operationId": "login_api_v1_auth_token_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Optional API Key for account/tenant identification. If provided, it will be used to identify the account.", "title": "X-Api-Key"}, "description": "Optional API Key for account/tenant identification. If provided, it will be used to identify the account."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/logout": {"post": {"tags": ["auth"], "summary": "Logout", "description": "Revoca el token JWT actual.", "operationId": "logout_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/accounts/": {"get": {"tags": ["accounts"], "summary": "List Accounts", "description": "List all accounts.", "operationId": "list_accounts_api_v1_accounts__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AccountResponse"}, "type": "array", "title": "Response List Accounts Api V1 Accounts  Get"}}}}}}}, "/api/v1/accounts/accounts": {"post": {"tags": ["accounts"], "summary": "Create Account", "description": "Crea una nueva cuenta.\n\nUtiliza la columna Identity para generar automáticamente el ID de la cuenta.", "operationId": "create_account_api_v1_accounts_accounts_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/accounts/{account_id}": {"get": {"tags": ["accounts"], "summary": "Get Account", "description": "Get account by ID.", "operationId": "get_account_api_v1_accounts__account_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Account Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/accounts/{account_id}/deactivate": {"patch": {"tags": ["accounts"], "summary": "Deactivate Account", "operationId": "deactivate_account_api_v1_accounts__account_id__deactivate_patch", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Account Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/accounts/{account_id}/activate": {"patch": {"tags": ["accounts"], "summary": "Activate Account", "description": "Activate an account.", "operationId": "activate_account_api_v1_accounts__account_id__activate_patch", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Account Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/accounts/current": {"get": {"tags": ["accounts"], "summary": "Get Account Info", "description": "Get information about the current account.", "operationId": "get_account_info_api_v1_accounts_current_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}, "put": {"tags": ["accounts"], "summary": "Update Current Account", "description": "Update the current account information.\n\nThis endpoint allows administrators to update their own account details,\nsuch as the account name.", "operationId": "update_current_account_api_v1_accounts_current_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}, "patch": {"tags": ["accounts"], "summary": "<PERSON> Current Account", "description": "Partially update the current account information.\n\nThis endpoint allows administrators to update specific fields of their own account,\nsuch as the account name, without having to provide all fields.", "operationId": "patch_current_account_api_v1_accounts_current_patch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/accounts/{account_id}/audit-logs": {"get": {"tags": ["accounts"], "summary": "Get Audit Logs", "description": "Get audit logs with optional filters for a specific account.", "operationId": "get_audit_logs_api_v1_accounts__account_id__audit_logs_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Account Id"}}, {"name": "action", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action"}}, {"name": "artifact_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Artifact Name"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLog"}, "title": "Response Get Audit Logs Api V1 Accounts  Account Id  Audit Logs Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/accounts/usage": {"get": {"tags": ["accounts"], "summary": "Get Api Usage", "description": "Get API usage statistics for the current account.", "operationId": "get_api_usage_api_v1_accounts_usage_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageStats"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/plans/": {"get": {"tags": ["plans"], "summary": "Get Available Plans", "description": "Get all available subscription plans with their details.\n\nReturns:\n    Dict with plan information for all available plans.", "operationId": "get_available_plans_api_v1_plans__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"$ref": "#/components/schemas/PlanInfo"}, "type": "object", "title": "Response Get Available Plans Api V1 Plans  Get"}}}}}}}, "/api/v1/system-users/me": {"get": {"tags": ["system-users"], "summary": "Get Current User Info", "description": "Get information about the current authenticated user.\n\nThis endpoint only requires a valid JWT token, no API Key is needed.\nIt's useful for the initial login flow when the user hasn't confirmed\nthe API Key yet.", "operationId": "get_current_user_info_api_v1_system_users_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}, "put": {"tags": ["system-users"], "summary": "Update User Me", "description": "Update current user's information.", "operationId": "update_user_me_api_v1_system_users_me_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}, "delete": {"tags": ["system-users"], "summary": "Delete User Me", "description": "Delete current user.", "operationId": "delete_user_me_api_v1_system_users_me_delete", "responses": {"204": {"description": "Successful Response"}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/system-users/": {"post": {"tags": ["system-users"], "summary": "Create System User", "description": "Create a new system user.", "operationId": "create_system_user_api_v1_system_users__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/system-users/{user_id}": {"get": {"tags": ["system-users"], "summary": "Get System User", "description": "Get a system user by ID.", "operationId": "get_system_user_api_v1_system_users__user_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/system-users/roles/": {"post": {"tags": ["system-users"], "summary": "Create Role", "description": "Create a new role.", "operationId": "create_role_api_v1_system_users_roles__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/system-users/{user_id}/roles/{role_id}": {"post": {"tags": ["system-users"], "summary": "Assign Role", "description": "Assign a role to a system user.", "operationId": "assign_role_api_v1_system_users__user_id__roles__role_id__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["system-users"], "summary": "Remove Role", "description": "Remove a role from a system user.", "operationId": "remove_role_api_v1_system_users__user_id__roles__role_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/system-users/{user_id}/roles": {"get": {"tags": ["system-users"], "summary": "Get User Roles", "description": "Get all roles assigned to a system user.", "operationId": "get_user_roles_api_v1_system_users__user_id__roles_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}, "title": "Response Get User Roles Api V1 System Users  User Id  Roles Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/system-users/{user_id}/permissions": {"get": {"tags": ["system-users"], "summary": "Get User Permissions", "description": "Get all permissions assigned to a system user through their roles.", "operationId": "get_user_permissions_api_v1_system_users__user_id__permissions_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Get User Permissions Api V1 System Users  User Id  Permissions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/end-users/": {"post": {"tags": ["end-users"], "summary": "Create End User", "operationId": "create_end_user_api_v1_end_users__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EndUserCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EndUser"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["end-users"], "summary": "Read End Users", "operationId": "read_end_users_api_v1_end_users__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_EndUser_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/end-users/{user_id}": {"get": {"tags": ["end-users"], "summary": "Read End User", "description": "Get an end user by ID.", "operationId": "read_end_user_api_v1_end_users__user_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EndUser"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/products/": {"post": {"tags": ["products"], "summary": "Create Product", "operationId": "create_product_api_v1_products__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["products"], "summary": "Read Products", "description": "Obtener productos paginados para la cuenta actual", "operationId": "read_products_api_v1_products__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"type": "string", "title": "Category"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/products/{product_id}": {"get": {"tags": ["products"], "summary": "Get Product", "description": "Get a product by ID.", "operationId": "get_product_api_v1_products__product_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "product_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Product Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["products"], "summary": "Update Product", "operationId": "update_product_api_v1_products__product_id__put", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "product_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Product Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/products/{product_id}/inventory": {"patch": {"tags": ["products"], "summary": "Update Inventory", "description": "Update product inventory.", "operationId": "update_inventory_api_v1_products__product_id__inventory_patch", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "product_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Product Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/most-searched/": {"get": {"tags": ["recommendations"], "summary": "Get Most Searched", "operationId": "get_most_searched_api_v1_recommendations_most_searched__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "timeframe", "in": "query", "required": false, "schema": {"type": "string", "enum": ["day", "week", "month"], "default": "week", "title": "Timeframe"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/trending-searches/": {"get": {"tags": ["recommendations"], "summary": "Get Trending Searches", "operationId": "get_trending_searches_api_v1_recommendations_trending_searches__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Search_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/popular-trends/": {"get": {"tags": ["recommendations"], "summary": "Get Popular Trends", "operationId": "get_popular_trends_api_v1_recommendations_popular_trends__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "model_type", "in": "query", "required": false, "schema": {"type": "string", "description": "Model type to use for recommendations", "default": "standard", "title": "Model Type"}, "description": "Model type to use for recommendations"}, {"name": "include_explanation", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Incluir explicación de la recomendación", "default": false, "title": "Include Explanation"}, "description": "Incluir explicación de la recomendación"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/related-searches/{product_id}": {"get": {"tags": ["recommendations"], "summary": "Get Related Searches", "operationId": "get_related_searches_api_v1_recommendations_related_searches__product_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "product_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Product Id"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Search_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/most-sold/": {"get": {"tags": ["recommendations"], "summary": "Get Most Sold", "operationId": "get_most_sold_api_v1_recommendations_most_sold__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "timeframe", "in": "query", "required": false, "schema": {"type": "string", "enum": ["day", "week", "month"], "default": "week", "title": "Timeframe"}}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/top-rated/": {"get": {"tags": ["recommendations"], "summary": "Get Top Rated", "operationId": "get_top_rated_api_v1_recommendations_top_rated__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "min_ratings", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 10, "title": "<PERSON>"}}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category"}}, {"name": "model_type", "in": "query", "required": false, "schema": {"type": "string", "description": "Model type to use for recommendations", "default": "standard", "title": "Model Type"}, "description": "Model type to use for recommendations"}, {"name": "include_explanation", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Incluir explicación de la recomendación", "default": false, "title": "Include Explanation"}, "description": "Incluir explicación de la recomendación"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/category/{category}": {"get": {"tags": ["recommendations"], "summary": "Get Category Products", "operationId": "get_category_products_api_v1_recommendations_category__category__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string", "title": "Category"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "enum": ["popularity", "rating", "price_asc", "price_desc"], "default": "popularity", "title": "Sort By"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/related-categories/{category}": {"get": {"tags": ["recommendations"], "summary": "Get Related Categories", "operationId": "get_related_categories_api_v1_recommendations_related_categories__category__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string", "title": "Category"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Category_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/also-bought/{product_id}": {"get": {"tags": ["recommendations"], "summary": "Get Also Bought", "operationId": "get_also_bought_api_v1_recommendations_also_bought__product_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "product_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Product Id"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/products/{product_id}/similar": {"get": {"tags": ["recommendations"], "summary": "Get Similar Products", "description": "Obtiene productos similares al producto especificado basados en contenido.\n\nUtiliza vectores de características de productos para calcular similitud coseno\ny encontrar productos con características similares.\n\nParámetros:\n- product_id: ID del producto para el que se buscan similares\n- limit: Número máximo de productos similares a devolver\n- include_explanation: Si es True, incluye explicaciones de por qué los productos son similares\n- explanation_level: Nivel de detalle de la explicación (simple o detailed)\n\nRetorna:\n- Lista paginada de productos similares ordenados por similitud\n- Cada producto incluye un score de similitud\n- Si se solicita, incluye explicaciones de la similitud", "operationId": "get_similar_products_api_v1_recommendations_products__product_id__similar_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "product_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Product Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}, {"name": "include_explanation", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Incluir explicación de la similitud", "default": false, "title": "Include Explanation"}, "description": "Incluir explicación de la similitud"}, {"name": "explanation_level", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ExplanationLevel", "description": "Nivel de detalle de la explicación", "default": "simple"}, "description": "Nivel de detalle de la explicación"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/invalidate-cache/{user_id}": {"post": {"tags": ["recommendations"], "summary": "Invalidate User <PERSON>", "description": "Invalida la caché de recomendaciones para un usuario específico.\n\nArgs:\n    user_id: ID del usuario\n    account: Cuenta actual\n    db: Sesión de base de datos\n\nReturns:\n    Mensaje de confirmación", "operationId": "invalidate_user_cache_api_v1_recommendations_invalidate_cache__user_id__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "exclusiveMinimum": 0, "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/invalidate-cache": {"post": {"tags": ["recommendations"], "summary": "Invalidate Account <PERSON>", "description": "Invalida toda la caché de recomendaciones para una cuenta.", "operationId": "invalidate_account_cache_api_v1_recommendations_invalidate_cache_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/recommendations/personalized/query": {"post": {"tags": ["recommendations"], "summary": "Get personalized recommendations", "description": "Main endpoint for obtaining personalized recommendations with complex filters and structured context.", "operationId": "query_personalized_recommendations_api_v1_recommendations_personalized_query_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Product_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/recommendations/explain/{user_id}/{item_id}": {"get": {"tags": ["recommendations"], "summary": "Get Recommendation Explanation", "description": "Obtiene una explicación detallada de por qué un producto específico se recomienda a un usuario.\n\nEsta explicación incluye:\n- Razón principal de la recomendación\n- Razones secundarias\n- Nivel de confianza\n- Evidencia que respalda la recomendación (productos similares, categorías afines, etc.)\n- Explicación en texto plano\n\nArgs:\n    user_id: ID del usuario\n    item_id: ID del producto\n    account: Información de la cuenta autenticada\n    db: Sesión de base de datos\n    limit_service: Servicio de límites\n\nReturns:\n    Explicación detallada de la recomendación", "operationId": "get_recommendation_explanation_api_v1_recommendations_explain__user_id___item_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "exclusiveMinimum": 0, "title": "User Id"}}, {"name": "item_id", "in": "path", "required": true, "schema": {"type": "integer", "exclusiveMinimum": 0, "title": "Item Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailedExplanation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/confidence-metrics": {"get": {"tags": ["recommendations"], "summary": "Get Confidence Metrics", "description": "Obtiene métricas de confianza para las recomendaciones.\n\nEstas métricas incluyen:\n- Distribución de scores de confianza por tipo de modelo (colaborativo, contenido, híbrido)\n- Confianza promedio por categoría de producto\n- Factores que influyen en la confianza\n- Tendencias de confianza a lo largo del tiempo\n\nReturns:\n    Diccionario con métricas de confianza", "operationId": "get_confidence_metrics_api_v1_recommendations_confidence_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Confidence Metrics Api V1 Recommendations Confidence Metrics Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/recommendations/rollback/{artifact_version}": {"post": {"tags": ["recommendations"], "summary": "Rollback Model", "operationId": "rollback_model_api_v1_recommendations_rollback__artifact_version__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "artifact_version", "in": "path", "required": true, "schema": {"type": "string", "title": "Artifact Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/interactions/": {"post": {"tags": ["interactions"], "summary": "Create Interaction", "description": "Create a new interaction.", "operationId": "create_interaction_api_v1_interactions__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InteractionCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Interaction"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["interactions"], "summary": "Read Interactions", "description": "Get all interactions for the current account.", "operationId": "read_interactions_api_v1_interactions__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_Interaction_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/pipeline/train": {"post": {"tags": ["pipeline"], "summary": "Train Models", "description": "Inicia el entrenamiento de modelos usando Celery.\n\nVerifica los límites de API y la frecuencia de entrenamiento permitida según el plan de suscripción\nantes de crear el trabajo de entrenamiento y encolar la tarea.\n\nReturns:\n    TrainingResponse: Respuesta con el ID del trabajo y la tarea iniciada.\n    Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.", "operationId": "train_models_api_v1_pipeline_train_post", "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrainingResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/pipeline/status": {"get": {"tags": ["pipeline"], "summary": "Get Training Status", "description": "Obtiene el estado del último entrenamiento completado.\n\nEste endpoint devuelve las métricas del último modelo entrenado exitosamente,\nno el estado de un trabajo en curso. Para consultar el estado de un trabajo\nespecífico, use el endpoint /pipeline/jobs/{job_id}/status.", "operationId": "get_training_status_api_v1_pipeline_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrainingStatus"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/pipeline/jobs/{job_id}/status": {"get": {"tags": ["pipeline"], "summary": "Get Training Job Status", "description": "Consulta el estado de un trabajo de entrenamiento específico.\n\nArgs:\n    job_id: ID del trabajo de entrenamiento\n    account: Cuenta actual\n    db: Sesión de base de datos\n\nReturns:\n    Estado detallado del trabajo de entrenamiento", "operationId": "get_training_job_status_api_v1_pipeline_jobs__job_id__status_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Job Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrainingJobStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/pipeline/models": {"get": {"tags": ["pipeline"], "summary": "List Models", "description": "Lista todos los modelos entrenados para la cuenta.", "operationId": "list_models_api_v1_pipeline_models_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelMetadataResponse"}, "title": "Response List Models Api V1 Pipeline Models Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/pipeline/models/{model_id}/metrics": {"get": {"tags": ["pipeline"], "summary": "Get Model Metrics", "description": "Obtiene las métricas de un modelo específico.", "operationId": "get_model_metrics_api_v1_pipeline_models__model_id__metrics_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "model_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Model Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrainingStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/pipeline/invalidate-cache": {"post": {"tags": ["pipeline"], "summary": "Invalidate Cache", "description": "Invalida la caché de modelos y métricas.", "operationId": "invalidate_cache_api_v1_pipeline_invalidate_cache_post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "model_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Type"}}, {"name": "metric_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Metric Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/pipeline/train/{account_id}": {"post": {"tags": ["pipeline"], "summary": "Train Artifact For Account", "description": "Inicia un entrenamiento específico para la cuenta `account_id`.\nSolo para administradores del sistema.\n\nReturns:\n    Dict[str, Any]: Respuesta con el ID del trabajo y la tarea iniciada.\n    Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.", "operationId": "train_artifact_for_account_api_v1_pipeline_train__account_id__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Account Id"}}], "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Train Artifact For Account Api V1 Pipeline Train  Account Id  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/pipeline/process": {"post": {"tags": ["pipeline"], "summary": "Process Training Job", "description": "Endpoint para procesar trabajos de entrenamiento manualmente.\nEste endpoint es para uso administrativo o debugging.", "operationId": "process_training_job_api_v1_pipeline_process_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Payload"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Process Training Job Api V1 Pipeline Process Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/pipeline/callback/{job_id}": {"post": {"tags": ["pipeline"], "summary": "Training Callback", "description": "Endpoint de callback para notificaciones de finalización de entrenamiento.\nEste endpoint puede ser llamado por Celery o por sistemas externos.", "operationId": "training_callback_api_v1_pipeline_callback__job_id__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Job Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Payload"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Training Callback Api V1 Pipeline Callback  Job Id  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/cache/test-cache": {"get": {"tags": ["cache"], "summary": "Test Cache", "operationId": "test_cache_api_v1_cache_test_cache_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/cache/redis-health": {"get": {"tags": ["cache"], "summary": "<PERSON>", "operationId": "check_redis_api_v1_cache_redis_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/analytics/analytics/account": {"get": {"tags": ["analytics"], "summary": "Get Account Ana<PERSON><PERSON>", "description": "Get analytics data for the current account.\n\nIf account_id is provided and the user has admin privileges, returns data for that account.\nOtherwise, returns data for the current account.", "operationId": "get_account_analytics_api_v1_analytics_analytics_account_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Account Analytics Api V1 Analytics Analytics Account Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/analytics/endpoints": {"get": {"tags": ["analytics"], "summary": "Get Endpoint Analytics", "description": "Get analytics data for API endpoints.\n\nIf account_id is provided and the user has admin privileges, returns data for that account.\nOtherwise, returns data for the current account.\n\nCan be filtered by endpoint path and HTTP method.", "operationId": "get_endpoint_analytics_api_v1_analytics_analytics_endpoints_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "endpoint", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by specific endpoint", "title": "Endpoint"}, "description": "Filter by specific endpoint"}, {"name": "method", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by HTTP method", "title": "Method"}, "description": "Filter by HTTP method"}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Endpoint Analytics Api V1 Analytics Analytics Endpoints Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/analytics/recommendation_performance": {"get": {"tags": ["analytics"], "summary": "Get Recommendation Performance", "description": "Get performance metrics for recommendation models.\n\nReturns metrics such as:\n- Precision/Recall/NDCG/MAP: Accuracy of recommendations\n- Coverage: Percentage of catalog being recommended\n- Diversity: Variety in recommendations\n- Novelty: Measure of how unpopular/unknown the recommended items are (based on inverse popularity)\n- Serendipity: Measure of how surprising but relevant the recommendations are\n- CTR (Click-Through Rate): Percentage of recommended items that receive clicks\n- CVR (Conversion Rate): Percentage of recommended items that result in conversions\n- Training time: Time taken to train models\n- Inference time: Time taken to generate recommendations\n- System metrics: CPU/Memory usage, latency percentiles\n\nIf account_id is provided and the user has admin privileges, returns data for that account.\nOtherwise, returns data for the current account.", "operationId": "get_recommendation_performance_api_v1_analytics_analytics_recommendation_performance_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "model_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by specific model ID", "title": "Model Id"}, "description": "Filter by specific model ID"}, {"name": "metric_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by metric type (ndcg, map, catalog_coverage, etc.)", "title": "Metric Type"}, "description": "Filter by metric type (ndcg, map, catalog_coverage, etc.)"}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Recommendation Performance Api V1 Analytics Analytics Recommendation Performance Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/analytics/models/compare": {"get": {"tags": ["analytics"], "summary": "Compare Model Versions", "description": "Compara métricas entre diferentes versiones de modelos.\n\nEste endpoint permite comparar las métricas de rendimiento entre diferentes modelos\npara analizar mejoras o regresiones en el tiempo.\n\nIncluye métricas estándar de ML así como métricas proxy de negocio como:\n- Tasa de conversión en el conjunto de prueba\n- ROI estimado\n- Engagement estimado\n- Valor del cliente estimado\n\nSi se proporcionan model_ids, compara específicamente esos modelos.\nSi no, compara los últimos N modelos según el parámetro limit.\n\nReturns:\n    Diccionario con comparación detallada de métricas entre versiones de modelos", "operationId": "compare_model_versions_api_v1_analytics_analytics_models_compare_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "model_ids", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "integer"}, "description": "List of model IDs to compare", "title": "Model Ids"}, "description": "List of model IDs to compare"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Maximum number of models to compare if model_ids not provided", "default": 5, "title": "Limit"}, "description": "Maximum number of models to compare if model_ids not provided"}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Compare Model Versions Api V1 Analytics Analytics Models Compare Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/analytics/metrics/history": {"get": {"tags": ["analytics"], "summary": "Get Metrics History", "description": "Obtiene el historial de valores para una métrica específica.\n\nEste endpoint permite visualizar la evolución de una métrica a lo largo del tiempo\npara diferentes versiones de modelos.\n\nÚtil para análisis de tendencias y para evaluar el impacto de cambios en el modelo.\n\nArgs:\n    metric_name: Nombre de la métrica a consultar\n    limit: Número máximo de puntos de datos a devolver\n    account_id: ID opcional de la cuenta\n    \nReturns:\n    Lista con valores históricos de la métrica", "operationId": "get_metrics_history_api_v1_analytics_analytics_metrics_history_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "metric_name", "in": "query", "required": true, "schema": {"type": "string", "description": "Name of the metric to get history for", "title": "Metric Name"}, "description": "Name of the metric to get history for"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Maximum number of historical data points to return", "default": 10, "title": "Limit"}, "description": "Maximum number of historical data points to return"}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Metrics History Api V1 Analytics Analytics Metrics History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/billing/create-checkout-session": {"post": {"tags": ["billing"], "summary": "Create Checkout Session", "description": "Crea una sesión de checkout para suscribirse o cambiar de plan.\n\nRequiere autenticación con API Key y JWT.", "operationId": "create_checkout_session_api_v1_billing_create_checkout_session_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCheckoutSessionRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCheckoutSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/billing/create-portal-session": {"post": {"tags": ["billing"], "summary": "Create Portal Session", "description": "Crea una sesión del portal de facturación para gestionar la suscripción.\n\nRequiere autenticación con API Key y JWT.", "operationId": "create_portal_session_api_v1_billing_create_portal_session_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePortalSessionRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePortalSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/billing/webhook/mercadopago": {"post": {"tags": ["billing"], "summary": "Mercadopago Webhook", "description": "Webhook para recibir eventos de Mercado Pago.\n\nNo requiere autenticación, pero verifica la firma de Mercado Pago.", "operationId": "mercadopago_webhook_api_v1_billing_webhook_mercadopago_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/roles/": {"get": {"tags": ["roles"], "summary": "List Roles", "description": "List all roles available for the current account.\n\nRequires admin privileges.", "operationId": "list_roles_api_v1_roles__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Role"}, "type": "array", "title": "Response List Roles Api V1 Roles  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}, "post": {"tags": ["roles"], "summary": "Create Role", "description": "Create a new role.\n\nRequires admin privileges.", "operationId": "create_role_api_v1_roles__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/roles/{role_id}": {"get": {"tags": ["roles"], "summary": "Get Role", "description": "Get a specific role by ID.\n\nRequires admin privileges.", "operationId": "get_role_api_v1_roles__role_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["roles"], "summary": "Update Role", "description": "Update an existing role.\n\nRequires admin privileges.", "operationId": "update_role_api_v1_roles__role_id__put", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["roles"], "summary": "Delete Role", "description": "Delete a role.\n\nRequires admin privileges.", "operationId": "delete_role_api_v1_roles__role_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/roles/{role_id}/permissions": {"get": {"tags": ["roles"], "summary": "Get Role Permissions", "description": "Get all permissions assigned to a role.\n\nRequires admin privileges.", "operationId": "get_role_permissions_api_v1_roles__role_id__permissions_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Permission"}, "title": "Response Get Role Permissions Api V1 Roles  Role Id  Permissions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/roles/{role_id}/permissions/{permission_id}": {"post": {"tags": ["roles"], "summary": "Assign Permission To Role", "description": "Assign a permission to a role.\n\nRequires admin privileges.", "operationId": "assign_permission_to_role_api_v1_roles__role_id__permissions__permission_id__post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}, {"name": "permission_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Permission Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["roles"], "summary": "Remove Permission From Role", "description": "Remove a permission from a role.\n\nRequires admin privileges.", "operationId": "remove_permission_from_role_api_v1_roles__role_id__permissions__permission_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}, {"name": "permission_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Permission Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/permissions/": {"get": {"tags": ["permissions"], "summary": "List Permissions", "description": "List all permissions available for the current account.\n\nRequires admin privileges.", "operationId": "list_permissions_api_v1_permissions__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Permission"}, "type": "array", "title": "Response List Permissions Api V1 Permissions  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}, "post": {"tags": ["permissions"], "summary": "Create Permission", "description": "Create a new permission.\n\nRequires admin privileges.", "operationId": "create_permission_api_v1_permissions__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Permission"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/permissions/{permission_id}": {"get": {"tags": ["permissions"], "summary": "Get Permission", "description": "Get a specific permission by ID.\n\nRequires admin privileges.", "operationId": "get_permission_api_v1_permissions__permission_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "permission_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Permission Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Permission"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/permissions/{permission_id}/roles": {"get": {"tags": ["permissions"], "summary": "Get Roles With Permission", "description": "Get all roles that have a specific permission.\n\nRequires admin privileges.", "operationId": "get_roles_with_permission_api_v1_permissions__permission_id__roles_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "permission_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Permission Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}, "title": "Response Get Roles With Permission Api V1 Permissions  Permission Id  Roles Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/cleanup-audit-logs": {"post": {"tags": ["maintenance"], "summary": "Cleanup Audit Logs", "description": "Limpia logs de auditoría más antiguos que el período especificado.\n\nArgs:\n    days_to_keep: Número de días a mantener los logs (por defecto 90)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    run_async: Si es <PERSON>, ejecuta la limpieza en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea", "operationId": "cleanup_audit_logs_api_v1_maintenance_maintenance_cleanup_audit_logs_post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "days_to_keep", "in": "query", "required": false, "schema": {"type": "integer", "default": 90, "title": "Days To Keep"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}, {"name": "run_async", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Run Async"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Cleanup Audit Logs Api V1 Maintenance Maintenance Cleanup Audit Logs Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/cleanup-interactions": {"post": {"tags": ["maintenance"], "summary": "Cleanup Interactions", "description": "Limpia interacciones más antiguas que el período especificado.\n\nArgs:\n    days_to_keep: Número de días a mantener las interacciones (por defecto 180)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    batch_size: Tamaño del lote para eliminación (por defecto 10000)\n    run_async: Si es <PERSON>, ejecuta la limpieza en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea", "operationId": "cleanup_interactions_api_v1_maintenance_maintenance_cleanup_interactions_post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "days_to_keep", "in": "query", "required": false, "schema": {"type": "integer", "default": 180, "title": "Days To Keep"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}, {"name": "batch_size", "in": "query", "required": false, "schema": {"type": "integer", "default": 10000, "title": "<PERSON><PERSON> Si<PERSON>"}}, {"name": "run_async", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Run Async"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Cleanup Interactions Api V1 Maintenance Maintenance Cleanup Interactions Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/task/{task_id}": {"get": {"tags": ["maintenance"], "summary": "Get Task Status", "description": "Obtiene el estado de una tarea de mantenimiento.\n\nArgs:\n    task_id: ID de la tarea\n\nReturns:\n    Diccionario con información sobre la tarea", "operationId": "get_task_status_api_v1_maintenance_maintenance_task__task_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Task Status Api V1 Maintenance Maintenance Task  Task Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/cleanup-data-secure": {"post": {"tags": ["maintenance"], "summary": "Cleanup Data Secure Endpoint", "description": "Limpia datos antiguos de forma segura utilizando la función SECURITY DEFINER de PostgreSQL.\n\nEsta función proporciona una limpieza más segura con validación de parámetros y\nprotección contra inyección SQL. Requiere un account_id específico.\n\nArgs:\n    days_to_keep: Número de días a mantener los datos (por defecto 180)\n    account_id: ID de la cuenta (requerido)\n    run_async: Si es <PERSON>, ejecuta la limpieza en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea", "operationId": "cleanup_data_secure_endpoint_api_v1_maintenance_maintenance_cleanup_data_secure_post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "days_to_keep", "in": "query", "required": false, "schema": {"type": "integer", "default": 180, "title": "Days To Keep"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}, {"name": "run_async", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Run Async"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Cleanup Data Secure Endpoint Api V1 Maintenance Maintenance Cleanup Data Secure Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/archive-and-cleanup-audit-logs": {"post": {"tags": ["maintenance"], "summary": "Archive And Cleanup Audit Logs", "description": "Archiva y luego limpia logs de auditoría más antiguos que el período especificado.\n\nEsta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,\nproporcionando una estrategia de retención de datos costo-efectiva.\n\nArgs:\n    days_to_keep: Número de días a mantener los logs en Cloud SQL (por defecto 90)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    batch_size: Tamaño del lote para procesamiento (por defecto 10000)\n    run_async: Si es True, ejecuta el archivado en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea", "operationId": "archive_and_cleanup_audit_logs_api_v1_maintenance_maintenance_archive_and_cleanup_audit_logs_post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "days_to_keep", "in": "query", "required": false, "schema": {"type": "integer", "default": 90, "title": "Days To Keep"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}, {"name": "batch_size", "in": "query", "required": false, "schema": {"type": "integer", "default": 10000, "title": "<PERSON><PERSON> Si<PERSON>"}}, {"name": "run_async", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Run Async"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Archive And Cleanup Audit Logs Api V1 Maintenance Maintenance Archive And Cleanup Audit Logs Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/archive-and-cleanup-interactions": {"post": {"tags": ["maintenance"], "summary": "Archive And Cleanup Interactions", "description": "Archiva y luego limpia interacciones más antiguas que el período especificado.\n\nEsta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,\nproporcionando una estrategia de retención de datos costo-efectiva.\n\nArgs:\n    days_to_keep: Número de días a mantener las interacciones en Cloud SQL (por defecto 180)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    batch_size: Tamaño del lote para procesamiento (por defecto 10000)\n    run_async: Si es True, ejecuta el archivado en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea", "operationId": "archive_and_cleanup_interactions_api_v1_maintenance_maintenance_archive_and_cleanup_interactions_post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "days_to_keep", "in": "query", "required": false, "schema": {"type": "integer", "default": 180, "title": "Days To Keep"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}, {"name": "batch_size", "in": "query", "required": false, "schema": {"type": "integer", "default": 10000, "title": "<PERSON><PERSON> Si<PERSON>"}}, {"name": "run_async", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Run Async"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Archive And Cleanup Interactions Api V1 Maintenance Maintenance Archive And Cleanup Interactions Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/archived-files/{table_name}": {"get": {"tags": ["maintenance"], "summary": "List Archived Files", "description": "Lista archivos archivados para una tabla específica.\n\nArgs:\n    table_name: Nombre de la tabla (audit_logs o interactions)\n    start_date: Fecha de inicio para filtrar (formato ISO)\n    end_date: Fecha de fin para filtrar (formato ISO)\n    account_id: ID de cuenta para filtrar\n\nReturns:\n    Lista de archivos archivados con metadatos", "operationId": "list_archived_files_api_v1_maintenance_maintenance_archived_files__table_name__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "table_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Table Name"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "End Date"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response List Archived Files Api V1 Maintenance Maintenance Archived Files  Table Name  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/cleanup-soft-deleted-records": {"post": {"tags": ["maintenance"], "summary": "Cleanup Soft Deleted Records", "description": "Limpia registros con soft delete que han excedido el período de retención final.\n\nEsta función identifica y elimina permanentemente (o archiva y luego elimina)\nregistros que tienen is_active = FALSE y deleted_at anterior al umbral definido.\n\nArgs:\n    retention_days: Número de días de retención después del soft delete (por defecto 365)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    dry_run: Si es True, solo reporta qué se eliminaría sin hacer cambios\n    run_async: Si es True, ejecuta la limpieza en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea", "operationId": "cleanup_soft_deleted_records_api_v1_maintenance_maintenance_cleanup_soft_deleted_records_post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "retention_days", "in": "query", "required": false, "schema": {"type": "integer", "default": 365, "title": "Retention Days"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}, {"name": "dry_run", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Dry Run"}}, {"name": "run_async", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Run Async"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Cleanup Soft Deleted Records Api V1 Maintenance Maintenance Cleanup Soft Deleted Records Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/soft-delete-statistics": {"get": {"tags": ["maintenance"], "summary": "Get Soft Delete Statistics", "description": "Obtiene estadísticas sobre registros con soft delete.\n\nArgs:\n    account_id: ID de cuenta para filtrar (None para todas las cuentas)\n    run_async: Si es True, ejecuta como tarea en segundo plano\n\nReturns:\n    Estadísticas por tabla o ID de tarea si run_async=True", "operationId": "get_soft_delete_statistics_api_v1_maintenance_maintenance_soft_delete_statistics_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "account_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}, {"name": "run_async", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Run Async"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Soft Delete Statistics Api V1 Maintenance Maintenance Soft Delete Statistics Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/maintenance/maintenance/monitor-tables": {"post": {"tags": ["maintenance"], "summary": "Monitor Tables", "description": "Monitorea las tablas de alto volumen y devuelve estadísticas.\n\nArgs:\n    run_async: Si es <PERSON>, ejecuta el monitoreo en segundo plano\n\nReturns:\n    Diccionario con estadísticas de las tablas o el ID de la tarea", "operationId": "monitor_tables_api_v1_maintenance_maintenance_monitor_tables_post", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "run_async", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Run Async"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Monitor Tables Api V1 Maintenance Maintenance Monitor Tables Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscription/usage": {"get": {"tags": ["subscription"], "summary": "Get Subscription Usage", "description": "Get subscription usage information for the current account.\n\nReturns:\n    Dict with subscription usage information including:\n    - API calls used and limit\n    - Storage used and limit\n    - Available models\n    - Reset date for API calls counter", "operationId": "get_subscription_usage_api_v1_subscription_usage_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Subscription Usage Api V1 Subscription Usage Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/storage/usage": {"get": {"tags": ["storage"], "summary": "Get Storage Usage", "description": "Get storage usage information for the current account.\n\nReturns:\n    Dict with storage usage information including:\n    - Storage used and limit\n    - Breakdown by data type\n    - Last measurement time", "operationId": "get_storage_usage_api_v1_storage_usage_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Storage Usage Api V1 Storage Usage Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/storage/refresh": {"post": {"tags": ["storage"], "summary": "Refresh Storage Usage", "description": "Force a refresh of storage usage calculation for the current account.\n\nReturns:\n    Dict with updated storage usage information", "operationId": "refresh_storage_usage_api_v1_storage_refresh_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Refresh Storage Usage Api V1 Storage Refresh Post"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/ingestion/batch": {"post": {"tags": ["ingestion"], "summary": "Batch Data Ingestion", "description": "Carga masiva de datos de usuarios, productos e interacciones.\n\nEste endpoint permite cargar datos en lote para inicializar o actualizar el sistema de recomendación.\nLos datos se procesan de forma asíncrona utilizando Celery para mayor robustez.\nLos datos sensibles se almacenan de forma segura en GCS o en el sistema de archivos local.\n\nPara una guía detallada sobre cómo formatear y enviar datos, consulte la\n[Guía de Ingesta de Datos Masiva](/docs/guides/data_ingestion_guide).", "operationId": "batch_data_ingestion_api_v1_ingestion_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchIngestionRequest"}}}, "required": true}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchIngestionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/ingestion/batch/{job_id}": {"get": {"tags": ["ingestion"], "summary": "Get Batch Job Status", "description": "Consulta el estado de un trabajo de ingesta masiva.\n\nEste endpoint permite verificar el progreso de un trabajo de ingesta masiva,\nincluyendo la cantidad de registros procesados, errores encontrados y tiempo estimado\nde finalización.\n\nPara más detalles sobre cómo monitorear el proceso de ingesta y manejar errores,\nconsulte la sección [Monitoreo del Proceso](/docs/guides/data_ingestion_guide#monitoreo-del-proceso)\nen la Guía de Ingesta de Datos.\n\nArgs:\n    job_id: ID del trabajo de ingesta masiva\n    account: Cuenta actual\n    db: Sesión de base de datos\n\nReturns:\n    Estado del trabajo de ingesta masiva", "operationId": "get_batch_job_status_api_v1_ingestion_batch__job_id__get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Job Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchIngestionJobStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/usage/summary": {"get": {"tags": ["usage"], "summary": "Get Usage Summary", "description": "Get a consolidated summary of usage information for the current account.\n\nReturns:\n    Dict with comprehensive usage information including:\n    - Current plan details\n    - API calls usage with reset date\n    - Storage usage with last measurement time\n    - Training usage (last training date and frequency limit)\n    - Link to Stripe Billing Portal", "operationId": "get_usage_summary_api_v1_usage_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Usage Summary Api V1 Usage Summary Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/usage/history": {"get": {"tags": ["usage"], "summary": "Get Usage History", "description": "Get historical usage data for the current account.\n\nArgs:\n    start_date: Optional start date for filtering data\n    end_date: Optional end date for filtering data\n    \nReturns:\n    List of daily usage data points", "operationId": "get_usage_history_api_v1_usage_history_get", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Start date for historical data", "title": "Start Date"}, "description": "Start date for historical data"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "End date for historical data", "title": "End Date"}, "description": "End date for historical data"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Usage History Api V1 Usage History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api-keys/": {"get": {"tags": ["api-keys"], "summary": "List Api Keys", "description": "Lista todas las API Keys activas para la cuenta actual.\n\nRetorna una lista de todas las API Keys activas asociadas a la cuenta,\nincluyendo información como nombre, prefijo, últimos caracteres, fecha de creación\ny último uso.\n\n**Nota**: No se incluyen las API Keys completas por seguridad.", "operationId": "list_api_keys_api_v1_api_keys__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyListResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}, "post": {"tags": ["api-keys"], "summary": "Create Api Key", "description": "Crea una nueva API Key para la cuenta actual.\n\nEste endpoint permite crear múltiples API Keys para una cuenta, lo que mejora\nla seguridad y flexibilidad:\n- Diferentes claves para diferentes entornos (desarrollo, staging, producción)\n- Claves específicas para diferentes miembros del equipo\n- Posibilidad de revocar claves específicas sin afectar otras integraciones\n\n**IMPORTANTE**:\n- Requiere autenticación JWT (debes estar logueado)\n- La API Key completa solo se devolverá una vez\n- Puedes crear múltiples API Keys activas\n- Cada API Key puede tener un nombre descriptivo\n\n**Autenticación requerida**: JWT token en el header Authorization: Bearer <token>\n\nReturns:\n- **id**: ID único de la API Key\n- **api_key**: Tu nueva API Key completa (solo se muestra una vez)\n- **name**: Nombre descriptivo de la API Key\n- **prefix**: Prefijo de la API Key para identificación\n- **created_at**: Fecha y hora de creación\n- **message**: Mensaje informativo sobre el uso seguro", "operationId": "create_api_key_api_v1_api_keys__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewApiKeyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}, "delete": {"tags": ["api-keys"], "summary": "Revoke Api Key", "description": "Revoca todas las API Keys de la cuenta actual.\nEsta acción es irreversible. Después de revocar, tendrás que generar nuevas API Keys.", "operationId": "revoke_api_key_api_v1_api_keys__delete", "responses": {"204": {"description": "Successful Response"}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/api-keys/current": {"get": {"tags": ["api-keys"], "summary": "Get Current Api Key", "description": "Obtiene información sobre la API Key actual.\nNo devuelve la API Key completa, solo metadatos como:\n- Prefijo de la API Key\n- Últimos caracteres\n- Fecha de creación\n- Último uso (si está disponible)\n\nEsta información permite identificar la API Key sin comprometer seguridad.", "operationId": "get_current_api_key_api_v1_api_keys_current_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}]}}, "/api/v1/api-keys/{api_key_id}": {"put": {"tags": ["api-keys"], "summary": "Update Api Key", "description": "Actualiza los metadatos de una API Key específica.\n\nPermite actualizar información como el nombre descriptivo de la API Key.\nNo se puede cambiar la clave en sí, solo sus metadatos.\n\n**IMPORTANTE**:\n- Solo puedes actualizar API Keys que pertenecen a tu cuenta\n- No se puede cambiar la API Key en sí, solo metadatos\n- La API Key debe estar activa para poder actualizarla", "operationId": "update_api_key_api_v1_api_keys__api_key_id__put", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "api_key_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID de la API Key a actualizar", "title": "Api Key Id"}, "description": "ID de la API Key a actualizar"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["api-keys"], "summary": "Revoke Specific Api Key", "description": "Revoca una API Key específica.\n\nEsta acción es irreversible. La API Key será desactivada y no podrá\nutilizarse para autenticar solicitudes.\n\n**IMPORTANTE**:\n- Solo puedes revocar API Keys que pertenecen a tu cuenta\n- Esta acción es irreversible\n- La API Key dejará de funcionar inmediatamente\n- Otras API Keys de la cuenta no se verán afectadas", "operationId": "revoke_specific_api_key_api_v1_api_keys__api_key_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"APIKeyHeader": []}], "parameters": [{"name": "api_key_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID de la API Key a revocar", "title": "Api Key Id"}, "description": "ID de la API Key a revocar"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AccountCreate": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "mercadopagoCustomerId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mercadopagocustomerid"}, "isActive": {"type": "boolean", "title": "Isactive", "default": true}}, "type": "object", "required": ["name"], "title": "AccountCreate"}, "AccountResponse": {"properties": {"name": {"type": "string", "title": "Name"}, "accountId": {"type": "integer", "title": "Accountid"}, "mercadopagoCustomerId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mercadopagocustomerid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"type": "string", "format": "date-time", "title": "Updatedat"}, "isActive": {"type": "boolean", "title": "Isactive"}, "deletedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deletedat"}, "subscription": {"anyOf": [{"$ref": "#/components/schemas/SubscriptionBasicInfo"}, {"type": "null"}]}}, "type": "object", "required": ["name", "accountId", "createdAt", "updatedAt", "isActive"], "title": "AccountResponse"}, "AccountUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "mercadopagoCustomerId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mercadopagocustomerid"}, "isActive": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Isactive"}}, "type": "object", "title": "AccountUpdate"}, "ApiKeyCreate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Nombre descriptivo para esta API Key"}}, "type": "object", "title": "ApiKeyCreate", "description": "Schema for creating a new API Key"}, "ApiKeyListResponse": {"properties": {"api_keys": {"items": {"$ref": "#/components/schemas/ApiKeyResponse"}, "type": "array", "title": "A<PERSON> <PERSON>", "description": "Lista de API Keys"}, "total": {"type": "integer", "title": "Total", "description": "Número total de API Keys"}}, "type": "object", "required": ["api_keys", "total"], "title": "ApiKeyListResponse", "description": "Schema for listing API Keys"}, "ApiKeyResponse": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Nombre descriptivo para esta API Key"}, "id": {"type": "integer", "title": "Id", "description": "ID único de la API Key"}, "prefix": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefix", "description": "Prefijo de la API Key"}, "last_chars": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Chars", "description": "Últimos caracteres de la API Key"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "Fecha de creación"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Estado de la API Key", "default": true}, "last_used": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used", "description": "Último uso de la API Key"}}, "type": "object", "required": ["id"], "title": "ApiKeyResponse", "description": "Schema for API Key information (no sensitive data)"}, "ApiKeyUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Nuevo nombre descriptivo para esta API Key"}}, "type": "object", "title": "ApiKeyUpdate", "description": "Schema for updating an API Key"}, "AuditLog": {"properties": {"action": {"type": "string", "maxLength": 50, "title": "Action"}, "entity_type": {"type": "string", "maxLength": 50, "title": "Entity Type"}, "entity_id": {"type": "string", "title": "Entity Id"}, "changes": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Changes"}, "performed_by": {"type": "string", "title": "Performed By"}, "details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Details"}, "account_id": {"type": "integer", "title": "Account Id"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["action", "entity_type", "entity_id", "performed_by", "account_id", "id", "created_at"], "title": "AuditLog"}, "BatchIngestionJobStatus": {"properties": {"job_id": {"type": "integer", "title": "Job Id"}, "status": {"type": "string", "title": "Status"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}, "task_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Id"}, "processed_count": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Processed Count"}, "parameters": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Parameters"}, "progress_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Progress Percentage"}, "estimated_remaining_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Estimated Remaining Time"}, "success_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Success Rate"}, "error_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Error Count"}, "error_details": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["job_id", "status", "created_at"], "title": "BatchIngestionJobStatus", "description": "Esquema para consultar el estado de un trabajo de ingesta masiva."}, "BatchIngestionRequest": {"properties": {"users": {"anyOf": [{"items": {"$ref": "#/components/schemas/EndUserCreate"}, "type": "array"}, {"type": "null"}], "title": "Users", "description": "Lista de usuarios a cargar"}, "products": {"anyOf": [{"items": {"$ref": "#/components/schemas/ProductCreate"}, "type": "array"}, {"type": "null"}], "title": "Products", "description": "Lista de productos a cargar"}, "interactions": {"anyOf": [{"items": {"$ref": "#/components/schemas/InteractionCreate"}, "type": "array"}, {"type": "null"}], "title": "Interactions", "description": "Lista de interacciones a cargar"}}, "type": "object", "title": "BatchIngestionRequest", "description": "Esquema para la carga masiva de datos"}, "BatchIngestionResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "status": {"type": "string", "title": "Status"}, "job_id": {"type": "integer", "title": "Job Id"}, "task_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Id"}, "total_users": {"type": "integer", "title": "Total Users"}, "total_products": {"type": "integer", "title": "Total Products"}, "total_interactions": {"type": "integer", "title": "Total Interactions"}}, "type": "object", "required": ["message", "status", "job_id", "total_users", "total_products", "total_interactions"], "title": "BatchIngestionResponse", "description": "Esquema para la respuesta de la carga masiva"}, "Category": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "relevance": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Relevance"}}, "type": "object", "required": ["id", "name"], "title": "Category", "description": "Esquema para representar una categoría de productos."}, "CreateCheckoutSessionRequest": {"properties": {"price_id": {"type": "string", "title": "Price Id", "description": "Price ID (Stripe o Mercado Pago)"}, "success_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Success Url", "description": "URL de redirección en caso de éxito"}, "cancel_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cancel Url", "description": "URL de redirección en caso de cancelación"}, "payment_gateway": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Gateway", "description": "Pasarela de pago a utilizar (stripe o mercadopago)", "default": "mercadopago"}}, "type": "object", "required": ["price_id"], "title": "CreateCheckoutSessionRequest", "description": "Schema para crear una sesión de checkout."}, "CreateCheckoutSessionResponse": {"properties": {"url": {"type": "string", "title": "Url", "description": "URL de la sesión de checkout"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id", "description": "ID de la sesión de checkout (solo para Strip<PERSON>)"}}, "type": "object", "required": ["url"], "title": "CreateCheckoutSessionResponse", "description": "Schema para la respuesta de creación de sesión de checkout."}, "CreatePortalSessionRequest": {"properties": {"return_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Return Url", "description": "URL de retorno después de usar el portal"}, "payment_gateway": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Gateway", "description": "Pasarela de pago a utilizar (stripe o mercadopago)", "default": "mercadopago"}}, "type": "object", "title": "CreatePortalSessionRequest", "description": "Schema para crear una sesión del portal de facturación."}, "CreatePortalSessionResponse": {"properties": {"url": {"type": "string", "title": "Url", "description": "URL de la sesión del portal de facturación"}}, "type": "object", "required": ["url"], "title": "CreatePortalSessionResponse", "description": "Schema para la respuesta de creación de sesión del portal de facturación."}, "DetailedExplanation": {"properties": {"primary_reason": {"$ref": "#/components/schemas/ExplanationReason", "description": "Razón principal de la recomendación"}, "secondary_reasons": {"items": {"$ref": "#/components/schemas/ExplanationReason"}, "type": "array", "title": "Secondary Reasons", "description": "Razones secundarias", "default": []}, "confidence": {"type": "number", "maximum": 1, "minimum": 0, "title": "Confidence", "description": "Confianza en la recomendación (0-1)"}, "text_explanation": {"type": "string", "title": "Text Explanation", "description": "Explicación en texto plano"}, "evidence": {"items": {"$ref": "#/components/schemas/ExplanationEvidence"}, "type": "array", "title": "Evidence", "description": "Evidencia que respalda la explicación", "default": []}, "source": {"type": "string", "title": "Source", "description": "Fuente de la recomendación (collaborative, content, hybrid)"}}, "type": "object", "required": ["primary_reason", "confidence", "text_explanation", "source"], "title": "DetailedExplanation", "description": "Explicación detallada de una recomendación"}, "DeviceType": {"type": "string", "enum": ["desktop", "mobile", "tablet", "tv", "other"], "title": "DeviceType", "description": "Tipos de dispositivos desde donde se solicitan recomendaciones"}, "EndUser": {"properties": {"external_id": {"type": "string", "title": "External Id"}, "account_id": {"type": "integer", "title": "Account Id"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "is_active": {"type": "boolean", "title": "Is Active"}, "deleted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted At"}, "preferred_categories": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Preferred Categories"}, "disliked_categories": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Disliked Categories"}, "preferred_brands": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Preferred Brands"}, "disliked_brands": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Disliked <PERSON>s"}, "price_range_min": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Price Range Min"}, "price_range_max": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Price Range Max"}, "demographic_info": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Demographic Info"}, "onboarding_preferences": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Onboarding Preferences"}}, "type": "object", "required": ["external_id", "account_id", "id", "created_at", "updated_at", "is_active"], "title": "EndUser"}, "EndUserCreate": {"properties": {"external_id": {"type": "string", "title": "External Id"}, "preferred_categories": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Preferred Categories", "description": "List of preferred product categories for initial recommendations"}, "disliked_categories": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Disliked Categories", "description": "List of categories to avoid in recommendations"}, "preferred_brands": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Preferred Brands", "description": "List of preferred brands for initial recommendations"}, "disliked_brands": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Disliked <PERSON>s", "description": "List of brands to avoid in recommendations"}, "price_range_min": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "Price Range Min", "description": "Minimum price preference for recommendations"}, "price_range_max": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "Price Range Max", "description": "Maximum price preference for recommendations"}, "demographic_info": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Demographic Info", "description": "Demographic information (age_group, gender, location, etc.)"}, "onboarding_preferences": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Onboarding Preferences", "description": "Additional preferences collected during onboarding"}}, "type": "object", "required": ["external_id"], "title": "EndUserCreate"}, "ExplanationEvidence": {"properties": {"type": {"type": "string", "title": "Type", "description": "Tipo de evidencia (producto, categoría, atributo, etc.)"}, "id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id", "description": "ID del elemento (si aplica)"}, "name": {"type": "string", "title": "Name", "description": "Nombre o descripción del elemento"}, "relevance": {"type": "number", "maximum": 1, "minimum": 0, "title": "Relevance", "description": "Relevancia de esta evidencia (0-1)"}}, "type": "object", "required": ["type", "name", "relevance"], "title": "ExplanationEvidence", "description": "Evidencia que respalda una explicación"}, "ExplanationLevel": {"type": "string", "enum": ["simple", "detailed"], "title": "ExplanationLevel", "description": "Niveles de detalle para explicaciones de recomendaciones"}, "ExplanationReason": {"type": "string", "enum": ["similar_users", "similar_items", "category_affinity", "popular_item", "trending_item", "attribute_match", "complementary_item", "recent_interaction", "personalized_ranking", "new_item", "diversity", "seasonal", "promotional"], "title": "ExplanationReason", "description": "Razones para recomendar un producto"}, "Filter": {"properties": {"field": {"type": "string", "title": "Field", "description": "Campo a filtrar"}, "operator": {"$ref": "#/components/schemas/FilterOperator", "description": "Operador de filtrado"}, "value": {"title": "Value", "description": "Valor a comparar"}}, "type": "object", "required": ["field", "operator", "value"], "title": "Filter", "description": "Filtro para recomendaciones"}, "FilterGroup": {"properties": {"logic": {"$ref": "#/components/schemas/LogicalOperator", "description": "Lógica de combinación (and/or)", "default": "and"}, "filters": {"items": {"anyOf": [{"$ref": "#/components/schemas/Filter"}, {"$ref": "#/components/schemas/FilterGroup"}]}, "type": "array", "title": "Filters", "description": "Lista de filtros o grupos de filtros"}}, "type": "object", "required": ["filters"], "title": "FilterGroup", "description": "Grupo de filtros con lógica AND/OR"}, "FilterOperator": {"type": "string", "enum": ["eq", "ne", "gt", "gte", "lt", "lte", "in", "nin", "contains", "startswith", "endswith"], "title": "FilterOperator", "description": "Operadores para filtros estructurados"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Interaction": {"properties": {"user_id": {"type": "integer", "title": "User Id"}, "product_id": {"type": "integer", "title": "Product Id"}, "interaction_type": {"$ref": "#/components/schemas/InteractionType"}, "value": {"type": "number", "title": "Value"}, "recommendation_metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Recommendation Metadata"}, "id": {"type": "integer", "title": "Id"}, "account_id": {"type": "integer", "title": "Account Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}}, "type": "object", "required": ["user_id", "product_id", "interaction_type", "value", "id", "account_id", "timestamp"], "title": "Interaction"}, "InteractionCreate": {"properties": {"user_id": {"type": "integer", "title": "User Id"}, "product_id": {"type": "integer", "title": "Product Id"}, "interaction_type": {"$ref": "#/components/schemas/InteractionType"}, "value": {"type": "number", "title": "Value"}, "recommendation_metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Recommendation Metadata"}}, "type": "object", "required": ["user_id", "product_id", "interaction_type", "value"], "title": "InteractionCreate"}, "InteractionType": {"type": "string", "enum": ["VIEW", "LIKE", "PURCHASE", "CART", "RATING", "WISHLIST", "CLICK", "SEARCH", "FAVORITE"], "title": "InteractionType"}, "InventoryUpdate": {"properties": {"inventory_count": {"type": "integer", "minimum": 0, "title": "Inventory Count", "description": "Cantidad de inventario disponible"}}, "type": "object", "required": ["inventory_count"], "title": "InventoryUpdate"}, "LogicalOperator": {"type": "string", "enum": ["and", "or"], "title": "LogicalOperator", "description": "Operadores lógicos para combinar filtros"}, "LoginRequest": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "Email del usuario"}, "password": {"type": "string", "minLength": 1, "title": "Password", "description": "Contraseña del usuario"}}, "type": "object", "required": ["email", "password"], "title": "LoginRequest", "description": "Schema para el login de usuarios con JSON payload."}, "ModelInfo": {"properties": {"id": {"type": "integer", "title": "Id"}, "artifact_name": {"type": "string", "title": "Artifact Name"}, "artifact_version": {"type": "string", "title": "Artifact Version"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "training_date": {"type": "string", "format": "date-time", "title": "Training Date"}}, "type": "object", "required": ["id", "artifact_name", "artifact_version", "training_date"], "title": "ModelInfo", "description": "Información básica de un modelo"}, "ModelMetadataResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "artifact_name": {"type": "string", "title": "Artifact Name"}, "artifact_version": {"type": "string", "title": "Artifact Version"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "training_date": {"type": "string", "format": "date-time", "title": "Training Date"}, "performance_metrics": {"type": "object", "title": "Performance Metrics"}, "parameters": {"type": "object", "title": "Parameters"}}, "type": "object", "required": ["id", "artifact_name", "artifact_version", "training_date", "performance_metrics", "parameters"], "title": "ModelMetadataResponse"}, "NewApiKeyResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID único de la API Key"}, "api_key": {"type": "string", "title": "Api Key", "description": "API Key completa (solo se muestra una vez)"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Nombre descriptivo de la API Key"}, "prefix": {"type": "string", "title": "Prefix", "description": "Prefijo de la API Key"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Fecha de creación"}, "message": {"type": "string", "title": "Message", "description": "Mensaje informativo"}}, "type": "object", "required": ["id", "api_key", "prefix", "created_at", "message"], "title": "NewApiKeyResponse", "description": "Schema for a newly created API Key (includes the full key)"}, "PageType": {"type": "string", "enum": ["home", "product_detail", "category", "search_results", "cart", "checkout", "user_profile"], "title": "PageType", "description": "Tipos de páginas donde se muestran recomendaciones"}, "PaginatedResponse_Category_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/Category"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedResponse[Category]"}, "PaginatedResponse_EndUser_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/EndUser"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedResponse[EndUser]"}, "PaginatedResponse_Interaction_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/Interaction"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedResponse[Interaction]"}, "PaginatedResponse_Product_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/Product"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedResponse[Product]"}, "PaginatedResponse_Search_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/Search"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedResponse[Search]"}, "Permission": {"properties": {"name": {"$ref": "#/components/schemas/PermissionType"}, "account_id": {"type": "integer", "title": "Account Id"}, "id": {"type": "integer", "title": "Id"}}, "type": "object", "required": ["name", "account_id", "id"], "title": "Permission"}, "PermissionCreate": {"properties": {"name": {"$ref": "#/components/schemas/PermissionType"}}, "type": "object", "required": ["name"], "title": "PermissionCreate"}, "PermissionType": {"type": "string", "enum": ["READ", "WRITE", "DELETE", "ADMIN", "PRODUCT_READ", "PRODUCT_CREATE", "PRODUCT_UPDATE", "PRODUCT_DELETE", "USER_READ", "USER_CREATE", "USER_UPDATE", "USER_DELETE", "SYSTEM_USER_READ", "SYSTEM_USER_CREATE", "SYSTEM_USER_UPDATE", "SYSTEM_USER_DELETE", "ROLE_READ", "ROLE_CREATE", "ROLE_UPDATE", "ROLE_DELETE", "PERMISSION_ASSIGN", "ANALYTICS_READ", "MODEL_READ", "MODEL_CREATE", "MODEL_UPDATE", "MODEL_DELETE", "TRAINING_JOB_READ", "TRAINING_JOB_CREATE", "TRAINING_JOB_UPDATE", "TRAINING_JOB_CANCEL"], "title": "PermissionType"}, "PlanInfo": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "price": {"type": "string", "title": "Price"}, "features": {"items": {"type": "string"}, "type": "array", "title": "Features"}, "limits": {"type": "object", "title": "Limits"}, "stripe_price_id": {"type": "string", "title": "Stripe Price Id"}, "contact_required": {"type": "boolean", "title": "Contact Required", "default": false}, "recommended": {"type": "boolean", "title": "Recommended", "default": false}}, "type": "object", "required": ["id", "name", "description", "price", "features", "limits"], "title": "PlanInfo", "description": "Plan information model."}, "Product": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "price": {"type": "string", "title": "Price"}, "category": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Category"}, "average_rating": {"type": "number", "maximum": 5, "minimum": 0, "title": "Average Rating", "default": 0}, "num_ratings": {"type": "integer", "minimum": 0, "title": "<PERSON>um <PERSON>ings", "default": 0}, "inventory_count": {"type": "integer", "minimum": 0, "title": "Inventory Count", "default": 0}, "product_id": {"type": "integer", "title": "Product Id"}, "account_id": {"type": "integer", "title": "Account Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "deleted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted At"}, "score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Score"}, "source": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source"}, "explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Explanation"}}, "type": "object", "required": ["name", "price", "category", "product_id", "account_id", "created_at", "updated_at"], "title": "Product"}, "ProductCreate": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "price": {"anyOf": [{"type": "number", "exclusiveMinimum": 0}, {"type": "string"}], "title": "Price"}, "category": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Category"}, "average_rating": {"type": "number", "maximum": 5, "minimum": 0, "title": "Average Rating", "default": 0}, "num_ratings": {"type": "integer", "minimum": 0, "title": "<PERSON>um <PERSON>ings", "default": 0}, "inventory_count": {"type": "integer", "minimum": 0, "title": "Inventory Count", "default": 0}}, "type": "object", "required": ["name", "price", "category"], "title": "ProductCreate"}, "ProductUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "price": {"anyOf": [{"type": "number", "exclusiveMinimum": 0}, {"type": "string"}, {"type": "null"}], "title": "Price"}, "category": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Category"}, "average_rating": {"anyOf": [{"type": "number", "maximum": 5, "minimum": 0}, {"type": "null"}], "title": "Average Rating"}, "num_ratings": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "<PERSON>um <PERSON>ings"}, "inventory_count": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "Inventory Count"}}, "type": "object", "title": "ProductUpdate"}, "RecommendationContext": {"properties": {"page_type": {"anyOf": [{"$ref": "#/components/schemas/PageType"}, {"type": "null"}], "description": "Tipo de página donde se muestran las recomendaciones"}, "device": {"anyOf": [{"$ref": "#/components/schemas/DeviceType"}, {"type": "null"}], "description": "Tipo de dispositivo desde donde se solicitan las recomendaciones"}, "source_item_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Item Id", "description": "ID del producto de origen (ej: en página de detalle)"}, "search_query": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search Query", "description": "Consulta de búsqueda actual"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id", "description": "ID de la categoría actual"}, "cart_item_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Cart Item Ids", "description": "IDs de productos en el carrito"}, "recently_viewed_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Recently Viewed Ids", "description": "IDs de productos vistos recientemente"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location", "description": "Ubicación geográfica del usuario"}, "time_of_day": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Time Of Day", "description": "Momento del día (mañana, tarde, noche)"}, "custom_attributes": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Custom Attributes", "description": "Atributos personalizados adicionales"}}, "type": "object", "title": "RecommendationContext", "description": "Contexto explícito para recomendaciones personalizadas"}, "RecommendationQueryRequest": {"properties": {"user_id": {"type": "integer", "exclusiveMinimum": 0, "title": "User Id", "description": "ID del usuario para el que se generan recomendaciones"}, "filters": {"anyOf": [{"$ref": "#/components/schemas/FilterGroup"}, {"type": "null"}], "description": "Filtros estructurados para aplicar a las recomendaciones"}, "context": {"anyOf": [{"$ref": "#/components/schemas/RecommendationContext"}, {"type": "null"}], "description": "Contexto explícito para filtrar o re-rankear recomendaciones"}, "strategy": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Strategy", "description": "Estrategia de recomendación a utilizar"}, "model_type": {"type": "string", "title": "Model Type", "description": "Tipo de modelo a utilizar", "default": "standard"}, "include_explanation": {"type": "boolean", "title": "Include Explanation", "description": "Incluir explicación de la recomendación", "default": false}, "sort_by": {"anyOf": [{"$ref": "#/components/schemas/SortConfig"}, {"type": "null"}], "description": "Configuración de ordenamiento"}, "skip": {"type": "integer", "minimum": 0, "title": "<PERSON><PERSON>", "description": "Número de elementos a saltar", "default": 0}, "limit": {"type": "integer", "maximum": 100, "exclusiveMinimum": 0, "title": "Limit", "description": "Número máximo de elementos a devolver", "default": 10}}, "type": "object", "required": ["user_id"], "title": "RecommendationQueryRequest", "description": "Esquema para solicitudes de recomendaciones con filtros complejos.\nPermite especificar filtros estructurados y otros parámetros en el cuerpo de la solicitud.", "example": {"context": {"device": "mobile", "page_type": "product_detail", "source_item_id": 456}, "filters": {"filters": [{"field": "price", "op": "lt", "value": 50}, {"field": "category", "op": "eq", "value": "electronics"}], "logic": "and"}, "include_explanation": true, "limit": 10, "model_type": "standard", "skip": 0, "strategy": "balanced", "user_id": 123}}, "RegisterRequest": {"properties": {"accountName": {"type": "string", "minLength": 1, "title": "Accountname", "description": "Nombre de la cuenta"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "Email del usuario administrador"}, "password": {"type": "string", "minLength": 8, "title": "Password", "description": "Contraseña del usuario administrador"}}, "type": "object", "required": ["accountName", "email", "password"], "title": "RegisterRequest", "description": "Schema para el registro completo de una cuenta con usuario administrador."}, "Role": {"properties": {"name": {"$ref": "#/components/schemas/RoleType"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description", "description": "Descripción del rol"}, "account_id": {"type": "integer", "title": "Account Id"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "account_id", "id", "created_at", "updated_at"], "title": "Role"}, "RoleCreate": {"properties": {"name": {"$ref": "#/components/schemas/RoleType"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description", "description": "Descripción del rol"}}, "type": "object", "required": ["name"], "title": "RoleCreate"}, "RoleType": {"type": "string", "enum": ["ADMIN", "EDITOR", "VIEWER"], "title": "RoleType"}, "Search": {"properties": {"user_id": {"type": "integer", "title": "User Id"}, "query": {"type": "string", "title": "Query"}, "id": {"type": "integer", "title": "Id"}, "account_id": {"type": "integer", "title": "Account Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}}, "type": "object", "required": ["user_id", "query", "id", "account_id", "timestamp"], "title": "Search"}, "SortConfig": {"properties": {"field": {"type": "string", "title": "Field", "description": "Campo por el cual ordenar"}, "direction": {"$ref": "#/components/schemas/SortDirection", "description": "Dirección del ordenamiento", "default": "desc"}}, "type": "object", "required": ["field"], "title": "SortConfig", "description": "Configuración de ordenamiento"}, "SortDirection": {"type": "string", "enum": ["asc", "desc"], "title": "SortDirection", "description": "Dirección de ordenamiento"}, "SubscriptionBasicInfo": {"properties": {"plan": {"type": "string", "title": "Plan"}, "isActive": {"type": "boolean", "title": "Isactive"}, "expiresAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expiresat"}}, "type": "object", "required": ["plan", "isActive"], "title": "SubscriptionBasicInfo", "description": "Información básica de suscripción para incluir en las respuestas de cuenta"}, "SystemUserCreate": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["email", "password"], "title": "SystemUserCreate"}, "SystemUserResponse": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "account_id": {"type": "integer", "title": "Account Id"}, "id": {"type": "integer", "title": "Id"}, "is_active": {"type": "boolean", "title": "Is Active"}, "is_admin": {"type": "boolean", "title": "Is Admin"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "deleted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted At"}, "roles": {"items": {"$ref": "#/components/schemas/Role"}, "type": "array", "title": "Roles", "default": []}}, "type": "object", "required": ["email", "account_id", "id", "is_active", "is_admin", "created_at", "updated_at"], "title": "SystemUserResponse", "description": "Esquema de respuesta para usuarios del sistema que excluye campos sensibles"}, "SystemUserUpdate": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}}, "type": "object", "required": ["email"], "title": "SystemUserUpdate"}, "Token": {"properties": {"accessToken": {"type": "string", "title": "Accesstoken"}, "tokenType": {"type": "string", "title": "Tokentype"}}, "type": "object", "required": ["accessToken", "tokenType"], "title": "Token"}, "TrainingJobStatus": {"properties": {"job_id": {"type": "integer", "title": "Job Id"}, "status": {"type": "string", "title": "Status"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}, "task_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Id"}, "parameters": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Parameters"}, "metrics": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Metrics"}, "model": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["job_id", "status", "created_at"], "title": "TrainingJobStatus", "description": "Estado detallado de un trabajo de entrenamiento"}, "TrainingResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "job_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Job Id"}, "task_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Id"}, "account_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Account Id"}}, "type": "object", "required": ["message"], "title": "TrainingResponse", "description": "Respuesta para el inicio de entrenamiento"}, "TrainingStatus": {"properties": {"ndcg": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Ndcg"}, "precision": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Precision"}, "recall": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Recall"}, "map": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Map"}, "diversity": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Diversity"}, "coverage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Coverage"}, "training_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Training Time"}, "data_points": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Data Points"}, "custom_metrics": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Custom Metrics"}}, "type": "object", "title": "TrainingStatus", "description": "Estado del entrenamiento y métricas"}, "UsageStats": {"properties": {"api_calls_count": {"type": "integer", "title": "Api Calls Count", "default": 0}, "storage_used": {"type": "integer", "title": "Storage Used", "default": 0}, "last_reset": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Reset"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "title": "UsageStats", "description": "Estadísticas de uso de la API"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "token"}}}, "APIKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}}}