/**
 * New API client using generated code from OpenAPI spec
 * This replaces the manual api.ts file with generated, type-safe functions
 */

// Import generated API functions and types
import {
  DefaultApi,
  AuthApi,
  AccountsApi,
  UsageApi,
  SystemUsersApi,
  ApiKeysApi,
  PlansApi,
  HealthApi,
  // Types
  AuthTokenResponse,
  RegisterResponse,
  UserInfoResponse,
  AccountInfoResponse,
  AccountUsageResponse,
  UsageHistoryPoint,
  ApiHealthStatus,
  CheckoutSessionResponse,
  PlanInfo,
  ApiKeyResponse,
  NewApiKeyResponse,
  ApiKeyListResponse,
  ApiKeyCreate,
  ApiKeyUpdate
} from './generated/rayuelaAPI';

import { 
  createApiConfiguration, 
  handleApiResponse, 
  ApiError 
} from './generated/migration-helper';

// --- Authentication Functions ---

export const loginUser = async (credentials: { email: string; password: string }): Promise<AuthTokenResponse> => {
  const authApi = new AuthApi(createApiConfiguration());
  return handleApiResponse(authApi.authLoginPost(credentials));
};

export const registerUser = async (
  accountName: string, 
  email: string, 
  password: string
): Promise<RegisterResponse> => {
  const authApi = new AuthApi(createApiConfiguration());
  const request = {
    account_name: accountName,
    email,
    password
  };
  return handleApiResponse(authApi.authRegisterPost(request));
};

export const logout = async (token: string): Promise<{ message: string }> => {
  const authApi = new AuthApi(createApiConfiguration(token));
  return handleApiResponse(authApi.authLogoutPost());
};

export const requestEmailVerification = async (token: string): Promise<{ message: string }> => {
  const authApi = new AuthApi(createApiConfiguration(token));
  return handleApiResponse(authApi.authSendVerificationEmailPost());
};

export const verifyEmail = async (token: string): Promise<{ message: string }> => {
  const authApi = new AuthApi(createApiConfiguration());
  return handleApiResponse(authApi.authVerifyEmailGet(token));
};

// --- User Functions ---

export const getMe = async (token: string, apiKey?: string | null): Promise<UserInfoResponse> => {
  const usersApi = new SystemUsersApi(createApiConfiguration(token, apiKey || undefined));
  return handleApiResponse(usersApi.systemUsersMeGet());
};

// --- Account Functions ---

export const createAccount = async (data: { name: string }): Promise<AccountInfoResponse> => {
  const accountsApi = new AccountsApi(createApiConfiguration());
  return handleApiResponse(accountsApi.accountsPost(data));
};

export const getCurrentAccountByApiKey = async (apiKey: string): Promise<AccountInfoResponse> => {
  const accountsApi = new AccountsApi(createApiConfiguration(undefined, apiKey));
  return handleApiResponse(accountsApi.accountsCurrentGet());
};

export const getMyAccount = async (token: string, apiKey?: string): Promise<AccountInfoResponse> => {
  const accountsApi = new AccountsApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(accountsApi.accountsCurrentGet());
};

// --- Usage Functions ---

export const getAccountUsage = async (token: string, apiKey: string): Promise<AccountUsageResponse> => {
  const usageApi = new UsageApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(usageApi.usageSummaryGet());
};

export const getUsageHistory = async (
  token: string,
  apiKey: string,
  startDate?: string,
  endDate?: string
): Promise<UsageHistoryPoint[]> => {
  const usageApi = new UsageApi(createApiConfiguration(token, apiKey));
  const params: any = {};
  if (startDate) params.startDate = startDate;
  if (endDate) params.endDate = endDate;
  
  return handleApiResponse(usageApi.usageHistoryGet(params.startDate, params.endDate));
};

// --- API Key Functions ---

export const listApiKeys = async (token: string, apiKey: string): Promise<ApiKeyListResponse> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysGet());
};

export const createApiKey = async (
  token: string, 
  apiKey: string, 
  data: ApiKeyCreate
): Promise<NewApiKeyResponse> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysPost(data));
};

export const updateApiKey = async (
  token: string, 
  apiKey: string, 
  apiKeyId: number, 
  data: ApiKeyUpdate
): Promise<ApiKeyResponse> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysApiKeyIdPatch(apiKeyId, data));
};

export const revokeApiKey = async (
  token: string, 
  apiKey: string, 
  apiKeyId: number
): Promise<void> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysApiKeyIdDelete(apiKeyId));
};

export const getApiKey = async (token: string, apiKey: string): Promise<ApiKeyResponse> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysCurrentGet());
};

export const revokeAllApiKeys = async (token: string, apiKey: string): Promise<void> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysDelete());
};

// --- Billing Functions ---

export const createCheckoutSession = async (
  priceId: string,
  token: string,
  apiKey: string
): Promise<CheckoutSessionResponse> => {
  const defaultApi = new DefaultApi(createApiConfiguration(token, apiKey));
  const request = { price_id: priceId };
  return handleApiResponse(defaultApi.subscriptionsCheckoutPost(request));
};

export const createBillingPortalSession = async (
  token: string,
  apiKey: string
): Promise<CheckoutSessionResponse> => {
  const defaultApi = new DefaultApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(defaultApi.subscriptionsPortalPost());
};

// --- Plans Functions ---

export const getAvailablePlans = async (): Promise<Record<string, PlanInfo>> => {
  const plansApi = new PlansApi(createApiConfiguration());
  return handleApiResponse(plansApi.plansGet());
};

// --- Health Functions ---

export const getApiHealth = async (): Promise<ApiHealthStatus> => {
  const healthApi = new HealthApi(createApiConfiguration());
  return handleApiResponse(healthApi.healthGet());
};

// --- Legacy Functions for Backward Compatibility ---

export const createLegacyApiKey = (token: string, apiKey: string): Promise<NewApiKeyResponse> =>
  createApiKey(token, apiKey, { name: "Default API Key" });

// Re-export error class for compatibility
export { ApiError };

// Re-export types for compatibility
export type {
  AuthTokenResponse,
  RegisterResponse,
  UserInfoResponse as UserInfo,
  AccountInfoResponse as AccountInfo,
  AccountUsageResponse as AccountUsage,
  UsageHistoryPoint,
  ApiHealthStatus,
  CheckoutSessionResponse,
  PlanInfo,
  ApiKeyResponse as ApiKey,
  NewApiKeyResponse,
  ApiKeyListResponse,
  ApiKeyCreate,
  ApiKeyUpdate
};
