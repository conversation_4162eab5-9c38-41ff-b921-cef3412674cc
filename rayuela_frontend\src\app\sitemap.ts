import { MetadataRoute } from 'next'
import fs from 'fs'
import path from 'path'

// Base URL for the site
const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://rayuela.ai'

// Function to get documentation pages from backend docs
function getDocumentationPages(): Array<{ url: string; lastModified: Date; priority: number }> {
  const docsPath = path.join(process.cwd(), '../rayuela_backend/docs')
  const pages: Array<{ url: string; lastModified: Date; priority: number }> = []
  
  try {
    // Add main API documentation pages
    const apiDocsPages = [
      'api/index',
      'api/recommendations', 
      'api/pipeline',
      'quickstart/README',
      'quickstart/python',
      'quickstart/nodejs',
      'quickstart/php',
      'guides/data_ingestion_guide'
    ]
    
    apiDocsPages.forEach(page => {
      const filePath = path.join(docsPath, `${page}.md`)
      let lastModified = new Date()
      
      try {
        const stats = fs.statSync(filePath)
        lastModified = stats.mtime
      } catch {
        // If file doesn't exist, use current date
      }
      
      // Convert file path to URL path
      const urlPath = page.replace(/\/README$/, '').replace(/\.md$/, '')
      
      pages.push({
        url: `${baseUrl}/docs/${urlPath}`,
        lastModified,
        priority: 0.8
      })
    })
  } catch (error) {
    console.warn('Could not read documentation files:', error)
  }
  
  return pages
}

export default function sitemap(): MetadataRoute.Sitemap {
  const currentDate = new Date()
  
  // Static pages with their priorities
  const staticPages = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 1.0,
    },
    {
      url: `${baseUrl}/features`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/pricing`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/docs`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/legal/privacy`,
      lastModified: currentDate,
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/legal/terms`,
      lastModified: currentDate,
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/legal/notice`,
      lastModified: currentDate,
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/legal/cookies`,
      lastModified: currentDate,
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/legal/dpa`,
      lastModified: currentDate,
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
  ]
  
  // Get documentation pages
  const docPages = getDocumentationPages().map(page => ({
    ...page,
    changeFrequency: 'weekly' as const,
  }))
  
  return [...staticPages, ...docPages]
}
