#!/usr/bin/env python3
"""
Test FastAPI app to verify OpenAPI schema generation with camelCase fields.
"""

import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from fastapi import FastAPI
from db.schemas.auth import RegisterRequest, Token

app = FastAPI(title="Test API", version="1.0.0")

@app.post("/auth/register", response_model=Token)
async def register(request: RegisterRequest):
    """Test registration endpoint"""
    return Token(access_token="test_token", token_type="bearer")

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {"status": "ok"}

if __name__ == "__main__":
    # Generate and print the OpenAPI schema
    openapi_schema = app.openapi()
    
    print("🔍 OpenAPI Schema Analysis")
    print("=" * 50)
    
    # Check RegisterRequest schema
    if "components" in openapi_schema and "schemas" in openapi_schema["components"]:
        schemas = openapi_schema["components"]["schemas"]
        
        if "RegisterRequest" in schemas:
            register_schema = schemas["RegisterRequest"]
            print("📋 RegisterRequest Schema:")
            print(json.dumps(register_schema, indent=2))
            
            # Check if properties use camelCase
            if "properties" in register_schema:
                properties = register_schema["properties"]
                if "accountName" in properties:
                    print("✅ RegisterRequest uses camelCase (accountName) in OpenAPI schema")
                else:
                    print("❌ RegisterRequest does not use camelCase in OpenAPI schema")
                    print(f"   Available properties: {list(properties.keys())}")
        
        if "Token" in schemas:
            token_schema = schemas["Token"]
            print("\n📋 Token Schema:")
            print(json.dumps(token_schema, indent=2))
            
            # Check if properties use camelCase
            if "properties" in token_schema:
                properties = token_schema["properties"]
                if "accessToken" in properties and "tokenType" in properties:
                    print("✅ Token uses camelCase (accessToken, tokenType) in OpenAPI schema")
                else:
                    print("❌ Token does not use camelCase in OpenAPI schema")
                    print(f"   Available properties: {list(properties.keys())}")
    
    print("\n" + "=" * 50)
    print("✅ OpenAPI schema analysis completed!")
    
    # Save the schema to a file for inspection
    with open("test_openapi.json", "w") as f:
        json.dump(openapi_schema, f, indent=2)
    print("📄 Full OpenAPI schema saved to test_openapi.json")
