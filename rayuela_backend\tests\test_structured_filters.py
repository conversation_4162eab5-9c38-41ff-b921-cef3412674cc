import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import HTTPException
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.schemas import Filter, FilterGroup, FilterOperator, LogicalOperator
from src.utils.filter_utils import apply_filter, apply_filter_group, apply_structured_filters, parse_simple_filter_string
from src.ml_pipeline.serving_engine import ServingEngine
from main import app


@pytest.fixture
def mock_db():
    return AsyncMock(spec=AsyncSession)


def test_apply_filter():
    """Test that apply_filter correctly applies individual filters"""
    # Test items
    item1 = {"id": 1, "price": 50, "category": "electronics", "brand": "Samsung", "rating": 4.5}
    item2 = {"id": 2, "price": 100, "category": "clothing", "brand": "Nike", "rating": 3.8}
    
    # Test equals operator
    filter_eq = Filter(field="category", op=FilterOperator.EQUALS, value="electronics")
    assert apply_filter(item1, filter_eq) is True
    assert apply_filter(item2, filter_eq) is False
    
    # Test not equals operator
    filter_ne = Filter(field="category", op=FilterOperator.NOT_EQUALS, value="electronics")
    assert apply_filter(item1, filter_ne) is False
    assert apply_filter(item2, filter_ne) is True
    
    # Test greater than operator
    filter_gt = Filter(field="price", op=FilterOperator.GREATER_THAN, value=75)
    assert apply_filter(item1, filter_gt) is False
    assert apply_filter(item2, filter_gt) is True
    
    # Test less than operator
    filter_lt = Filter(field="price", op=FilterOperator.LESS_THAN, value=75)
    assert apply_filter(item1, filter_lt) is True
    assert apply_filter(item2, filter_lt) is False
    
    # Test in operator
    filter_in = Filter(field="brand", op=FilterOperator.IN, value=["Samsung", "Apple"])
    assert apply_filter(item1, filter_in) is True
    assert apply_filter(item2, filter_in) is False
    
    # Test contains operator
    filter_contains = Filter(field="brand", op=FilterOperator.CONTAINS, value="sam")
    assert apply_filter(item1, filter_contains) is True
    assert apply_filter(item2, filter_contains) is False


def test_apply_filter_group():
    """Test that apply_filter_group correctly applies groups of filters with logical operators"""
    # Test items
    item1 = {"id": 1, "price": 50, "category": "electronics", "brand": "Samsung", "rating": 4.5}
    item2 = {"id": 2, "price": 100, "category": "clothing", "brand": "Nike", "rating": 3.8}
    item3 = {"id": 3, "price": 30, "category": "electronics", "brand": "Apple", "rating": 4.8}
    
    # Test AND logic
    filter_group_and = FilterGroup(
        logic=LogicalOperator.AND,
        filters=[
            Filter(field="category", op=FilterOperator.EQUALS, value="electronics"),
            Filter(field="price", op=FilterOperator.LESS_THAN, value=40)
        ]
    )
    assert apply_filter_group(item1, filter_group_and) is False  # Price is 50, not < 40
    assert apply_filter_group(item2, filter_group_and) is False  # Category is not electronics
    assert apply_filter_group(item3, filter_group_and) is True   # Matches both conditions
    
    # Test OR logic
    filter_group_or = FilterGroup(
        logic=LogicalOperator.OR,
        filters=[
            Filter(field="category", op=FilterOperator.EQUALS, value="electronics"),
            Filter(field="price", op=FilterOperator.GREATER_THAN, value=90)
        ]
    )
    assert apply_filter_group(item1, filter_group_or) is True   # Category is electronics
    assert apply_filter_group(item2, filter_group_or) is True   # Price is > 90
    assert apply_filter_group(item3, filter_group_or) is True   # Category is electronics
    
    # Test nested groups
    nested_filter_group = FilterGroup(
        logic=LogicalOperator.AND,
        filters=[
            Filter(field="category", op=FilterOperator.EQUALS, value="electronics"),
            FilterGroup(
                logic=LogicalOperator.OR,
                filters=[
                    Filter(field="price", op=FilterOperator.LESS_THAN, value=40),
                    Filter(field="rating", op=FilterOperator.GREATER_THAN, value=4.0)
                ]
            )
        ]
    )
    assert apply_filter_group(item1, nested_filter_group) is True   # Electronics AND rating > 4.0
    assert apply_filter_group(item2, nested_filter_group) is False  # Not electronics
    assert apply_filter_group(item3, nested_filter_group) is True   # Electronics AND price < 40


def test_apply_structured_filters():
    """Test that apply_structured_filters correctly filters a list of items"""
    # Test items
    items = [
        {"id": 1, "price": 50, "category": "electronics", "brand": "Samsung", "rating": 4.5},
        {"id": 2, "price": 100, "category": "clothing", "brand": "Nike", "rating": 3.8},
        {"id": 3, "price": 30, "category": "electronics", "brand": "Apple", "rating": 4.8},
        {"id": 4, "price": 120, "category": "electronics", "brand": "Sony", "rating": 4.2},
        {"id": 5, "price": 25, "category": "books", "brand": "Penguin", "rating": 4.0}
    ]
    
    # Filter for electronics under $100
    filter_group = FilterGroup(
        logic=LogicalOperator.AND,
        filters=[
            Filter(field="category", op=FilterOperator.EQUALS, value="electronics"),
            Filter(field="price", op=FilterOperator.LESS_THAN, value=100)
        ]
    )
    
    filtered_items = apply_structured_filters(items, filter_group)
    assert len(filtered_items) == 2
    assert filtered_items[0]["id"] == 1  # Samsung
    assert filtered_items[1]["id"] == 3  # Apple
    
    # Filter for items with rating >= 4.0 that are either electronics or under $50
    complex_filter = FilterGroup(
        logic=LogicalOperator.AND,
        filters=[
            Filter(field="rating", op=FilterOperator.GREATER_THAN_EQUALS, value=4.0),
            FilterGroup(
                logic=LogicalOperator.OR,
                filters=[
                    Filter(field="category", op=FilterOperator.EQUALS, value="electronics"),
                    Filter(field="price", op=FilterOperator.LESS_THAN, value=50)
                ]
            )
        ]
    )
    
    filtered_items = apply_structured_filters(items, complex_filter)
    assert len(filtered_items) == 4
    assert [item["id"] for item in filtered_items] == [1, 3, 4, 5]


def test_parse_simple_filter_string():
    """Test that parse_simple_filter_string correctly parses simple filter strings"""
    # Test basic field:value syntax
    filter_str = "category:electronics,price_lt:100"
    filter_group = parse_simple_filter_string(filter_str)
    
    assert filter_group is not None
    assert filter_group.logic == LogicalOperator.AND
    assert len(filter_group.filters) == 2
    
    # Check first filter
    assert filter_group.filters[0].field == "category"
    assert filter_group.filters[0].op == FilterOperator.EQUALS
    assert filter_group.filters[0].value == "electronics"
    
    # Check second filter
    assert filter_group.filters[1].field == "price"
    assert filter_group.filters[1].op == FilterOperator.LESS_THAN
    assert filter_group.filters[1].value == "100"
    
    # Test with numeric values
    filter_str = "rating_gte:4.5,price:50"
    filter_group = parse_simple_filter_string(filter_str)
    
    assert filter_group is not None
    assert len(filter_group.filters) == 2
    
    # Check first filter
    assert filter_group.filters[0].field == "rating"
    assert filter_group.filters[0].op == FilterOperator.GREATER_THAN_EQUALS
    assert filter_group.filters[0].value == 4.5
    
    # Check second filter
    assert filter_group.filters[1].field == "price"
    assert filter_group.filters[1].op == FilterOperator.EQUALS
    assert filter_group.filters[1].value == 50
    
    # Test with boolean values
    filter_str = "in_stock:true,featured:false"
    filter_group = parse_simple_filter_string(filter_str)
    
    assert filter_group is not None
    assert len(filter_group.filters) == 2
    
    # Check first filter
    assert filter_group.filters[0].field == "in_stock"
    assert filter_group.filters[0].op == FilterOperator.EQUALS
    assert filter_group.filters[0].value is True
    
    # Check second filter
    assert filter_group.filters[1].field == "featured"
    assert filter_group.filters[1].op == FilterOperator.EQUALS
    assert filter_group.filters[1].value is False


@pytest.mark.asyncio
async def test_get_recommendations_with_filters(mock_db):
    """Test that the GET endpoint correctly handles the filters parameter"""
    with patch("src.api.v1.endpoints.recommendations.get_current_account") as mock_get_account:
        mock_get_account.return_value = MagicMock(account_id=1)
        
        with patch("src.api.v1.endpoints.recommendations.get_db") as mock_get_db:
            mock_get_db.return_value = mock_db
            
            with patch("src.api.v1.endpoints.recommendations.get_pagination_params") as mock_pagination:
                mock_pagination.return_value = (0, 10)
                
                with patch("src.api.v1.endpoints.recommendations.get_limit_service") as mock_limit_service:
                    mock_limit_service.return_value = MagicMock()
                    
                    with patch("src.api.v1.endpoints.recommendations.recommendation_service") as mock_rec_service:
                        mock_rec_service.get_personalized_recommendations_with_cache.return_value = {
                            "items": [
                                {"id": 1, "name": "Product 1", "price": 50, "category": "electronics"},
                                {"id": 2, "name": "Product 2", "price": 100, "category": "clothing"}
                            ],
                            "total": 2,
                            "page": 1,
                            "size": 10
                        }
                        
                        # Create test client
                        client = AsyncClient(app=app, base_url="http://test")
                        
                        # Test with JSON filters
                        filters = {
                            "logic": "and",
                            "filters": [
                                {"field": "price", "op": "lt", "value": 75},
                                {"field": "category", "op": "eq", "value": "electronics"}
                            ]
                        }
                        
                        response = client.get(
                            f"/api/v1/recommendations/personalized/1?filters={json.dumps(filters)}"
                        )
                        
                        assert response.status_code == 200
                        assert "items" in response.json()
                        
                        # Test with simple syntax filters
                        response = client.get(
                            "/api/v1/recommendations/personalized/1?filters=price_lt:75,category:electronics"
                        )
                        
                        assert response.status_code == 200
                        assert "items" in response.json()


@pytest.mark.asyncio
async def test_post_recommendations_query(mock_db):
    """Test that the POST endpoint correctly handles the request body with filters"""
    with patch("src.api.v1.endpoints.recommendations.get_current_account") as mock_get_account:
        mock_get_account.return_value = MagicMock(account_id=1)
        
        with patch("src.api.v1.endpoints.recommendations.get_db") as mock_get_db:
            mock_get_db.return_value = mock_db
            
            with patch("src.api.v1.endpoints.recommendations.get_limit_service") as mock_limit_service:
                mock_limit_service.return_value = MagicMock()
                
                with patch("src.api.v1.endpoints.recommendations.recommendation_service") as mock_rec_service:
                    mock_rec_service.get_personalized_recommendations_with_cache.return_value = {
                        "items": [
                            {"id": 1, "name": "Product 1", "price": 50, "category": "electronics"},
                            {"id": 2, "name": "Product 2", "price": 100, "category": "clothing"}
                        ],
                        "total": 2,
                        "page": 1,
                        "size": 10
                    }
                    
                    # Create test client
                    client = AsyncClient(app=app, base_url="http://test")
                    
                    # Test with complex filters in request body
                    request_body = {
                        "user_id": 1,
                        "filters": {
                            "logic": "and",
                            "filters": [
                                {"field": "price", "op": "lt", "value": 75},
                                {
                                    "logic": "or",
                                    "filters": [
                                        {"field": "category", "op": "eq", "value": "electronics"},
                                        {"field": "brand", "op": "in", "value": ["Samsung", "Apple"]}
                                    ]
                                }
                            ]
                        },
                        "context": {
                            "page_type": "product_detail",
                            "device": "mobile",
                            "source_item_id": 123
                        },
                        "recommendation_type": "hybrid",
                        "model_type": "standard",
                        "strategy": "balanced",
                        "include_explanation": True,
                        "skip": 0,
                        "limit": 10
                    }
                    
                    response = client.post(
                        "/api/v1/recommendations/personalized/query",
                        json=request_body
                    )
                    
                    assert response.status_code == 200
                    assert "items" in response.json()
