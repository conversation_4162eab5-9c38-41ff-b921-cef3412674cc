import pytest
import pytest_asyncio
from unittest.mock import MagicMock, patch, AsyncMock
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone

from src.db import models
from src.ml_pipeline.serving_engine import ServingEngine
from main import app


class TestRecommendationsAPI:
    """Tests de integración para la API de recomendaciones"""

    @pytest.fixture
    def mock_auth_middleware(self):
        """Fixture para mockear el middleware de autenticación"""

        # Crear un middleware que simula la autenticación
        async def mock_auth_middleware(request, call_next):
            # Simular una cuenta autenticada
            request.state.account = MagicMock(id=1, account_id=1)
            request.state.user_id = 1
            return await call_next(request)

        # Guardar el middleware original
        original_middleware = app.middleware_stack

        # Reemplazar con nuestro middleware mock
        app.middleware_stack = mock_auth_middleware

        yield

        # Restaurar el middleware original
        app.middleware_stack = original_middleware

    @pytest.fixture
    def mock_recommender(self):
        """Fixture para mockear el servicio de recomendaciones"""
        with patch("src.api.v1.endpoints.recommendations.serving_engine") as mock_rec:
            # Configurar los mocks necesarios
            mock_rec.get_candidate_recommendations = AsyncMock()
            mock_rec.get_candidate_recommendations.return_value = [
                {"item_id": 1, "name": "Product 1", "category": "Category A", "score": 0.9},
                {"item_id": 2, "name": "Product 2", "category": "Category B", "score": 0.8},
                {"item_id": 3, "name": "Product 3", "category": "Category A", "score": 0.7},
            ]

            return mock_rec

    @pytest.mark.asyncio
    async def test_get_recommendations_for_user(
        self, test_client, async_db_session, mock_auth_middleware, mock_recommender
    ):
        """Test que verifica la obtención de recomendaciones para un usuario"""
        # 1. Crear una cuenta de prueba
        account = models.Account(
            id=1,
            name="Test Account",
            subscription_plan="BASIC",
            api_key="test_api_key",
            is_active=True,
        )
        async_db_session.add(account)

        # 2. Crear un usuario final
        end_user = models.EndUser(
            account_id=1, id=1, external_id="user123", is_active=True
        )
        async_db_session.add(end_user)

        # 3. Crear productos
        for i in range(1, 6):
            product = models.Product(
                account_id=1,
                id=i,
                name=f"Product {i}",
                description=f"Description for product {i}",
                category="Category A" if i % 2 == 0 else "Category B",
                is_active=True,
            )
            async_db_session.add(product)

        await async_db_session.commit()

        # 4. Solicitar recomendaciones para el usuario
        response = test_client.get(
            "/api/v1/recommendations/user/1?limit=3",
            headers={"X-API-Key": "test_api_key"},
        )

        # Verificar respuesta
        assert response.status_code == 200
        assert len(response.json()) == 3

        # Verificar que se llamó a get_candidate_recommendations
        mock_recommender.get_candidate_recommendations.assert_called_once()

        # 5. Solicitar recomendaciones con filtro de categoría
        response = test_client.get(
            "/api/v1/recommendations/user/1?limit=3&category=Category+A",
            headers={"X-API-Key": "test_api_key"},
        )

        # Verificar respuesta
        assert response.status_code == 200

        # 6. Solicitar recomendaciones similares a un producto
        response = test_client.get(
            "/api/v1/recommendations/similar/1?limit=3",
            headers={"X-API-Key": "test_api_key"},
        )

        # Verificar respuesta
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_record_interaction_and_get_recommendations(
        self, test_client, async_db_session, mock_auth_middleware, mock_recommender
    ):
        """Test que verifica el registro de interacciones y obtención de recomendaciones"""
        # 1. Crear una cuenta de prueba
        account = models.Account(
            id=1,
            name="Test Account",
            subscription_plan="BASIC",
            api_key="test_api_key",
            is_active=True,
        )
        async_db_session.add(account)

        # 2. Crear un usuario final
        end_user = models.EndUser(
            account_id=1, id=1, external_id="user123", is_active=True
        )
        async_db_session.add(end_user)

        # 3. Crear productos
        for i in range(1, 6):
            product = models.Product(
                account_id=1,
                id=i,
                name=f"Product {i}",
                description=f"Description for product {i}",
                category="Category A" if i % 2 == 0 else "Category B",
                is_active=True,
            )
            async_db_session.add(product)

        await async_db_session.commit()

        # 4. Registrar una interacción
        response = test_client.post(
            "/api/v1/interactions/",
            json={
                "end_user_id": 1,
                "product_id": 1,
                "interaction_type": "VIEW",
                "value": 1.0,
            },
            headers={"X-API-Key": "test_api_key"},
        )

        # Verificar respuesta
        assert response.status_code == 200

        # 5. Verificar que la interacción se guardó
        interactions = await async_db_session.execute(
            models.Interaction.select().where(
                models.Interaction.account_id == 1, models.Interaction.end_user_id == 1
            )
        )
        interaction = interactions.scalar_one_or_none()
        assert interaction is not None
        assert interaction.product_id == 1
        assert interaction.interaction_type == "VIEW"

        # 6. Solicitar recomendaciones personalizadas
        response = test_client.post(
            "/api/v1/recommendations/personalized/query",
            json={"user_id": 1, "limit": 3},
            headers={"X-API-Key": "test_api_key"},
        )

        # Verificar respuesta
        assert response.status_code == 200
