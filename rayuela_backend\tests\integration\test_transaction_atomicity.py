"""
Tests críticos de atomicidad de transacciones para operaciones complejas.
Valida que operaciones que involucran múltiples entidades de base de datos sean atómicas.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.db.models import (
    Account,
    SystemUser,
    Product,
    EndUser,
    Interaction,
    BatchIngestionJob,
)
from src.db.schemas import BatchIngestionRequest
from src.services.auth_service import AuthService
from src.services.api_key_service import ApiKeyService
from src.services.data_ingestion_service import DataIngestionService
from src.services.limit_service import LimitService
from src.utils.base_logger import log_info, log_error


@pytest.mark.asyncio
class TestTransactionAtomicity:
    """Tests críticos para validar atomicidad de transacciones complejas."""

    @pytest.fixture
    async def auth_service(self, db_session: AsyncSession) -> AuthService:
        """Crear servicio de autenticación para tests."""
        api_key_service = ApiKeyService()
        return AuthService(db_session, api_key_service)

    async def test_account_registration_atomicity_success(
        self, db_session: AsyncSession, auth_service: AuthService
    ):
        """Test que el registro de cuenta sea atómico cuando todo funciona correctamente."""
        
        # Datos de registro válidos
        account_name = f"Test Account {uuid.uuid4().hex[:8]}"
        email = f"test_{uuid.uuid4().hex[:8]}@example.com"
        password = "secure_password_123"

        # Verificar estado inicial
        initial_account_count = await self._count_accounts(db_session)
        initial_user_count = await self._count_system_users(db_session)

        # Ejecutar registro
        result = await auth_service.register_account(
            account_name=account_name,
            email=email,
            password=password
        )

        # Verificar que se crearon todas las entidades
        assert result is not None
        assert "account" in result
        assert "system_user" in result
        assert "access_token" in result
        assert "api_key" in result

        # Verificar que se incrementaron los contadores
        final_account_count = await self._count_accounts(db_session)
        final_user_count = await self._count_system_users(db_session)

        assert final_account_count == initial_account_count + 1
        assert final_user_count == initial_user_count + 1

        # Verificar que la cuenta tiene API key
        account = result["account"]
        assert account.api_key_hash is not None
        assert account.api_key_prefix is not None
        assert account.api_key_last_chars is not None

        log_info(f"✅ Account registration atomicity test passed for account {account.account_id}")

    async def test_account_registration_atomicity_failure_duplicate_email(
        self, db_session: AsyncSession, auth_service: AuthService
    ):
        """Test que el registro falle atómicamente si el email ya existe."""
        
        # Crear una cuenta existente
        existing_email = f"existing_{uuid.uuid4().hex[:8]}@example.com"
        await auth_service.register_account(
            account_name="Existing Account",
            email=existing_email,
            password="password123"
        )

        # Verificar estado antes del intento fallido
        initial_account_count = await self._count_accounts(db_session)
        initial_user_count = await self._count_system_users(db_session)

        # Intentar registrar con el mismo email (debe fallar)
        with pytest.raises(Exception):  # Debería lanzar HTTPException
            await auth_service.register_account(
                account_name="Duplicate Email Account",
                email=existing_email,  # Email duplicado
                password="password456"
            )

        # Verificar que NO se crearon entidades adicionales
        final_account_count = await self._count_accounts(db_session)
        final_user_count = await self._count_system_users(db_session)

        assert final_account_count == initial_account_count
        assert final_user_count == initial_user_count

        log_info("✅ Account registration atomicity failure test passed")

    async def test_complex_multi_entity_transaction_atomicity(
        self, db_session: AsyncSession
    ):
        """Test atomicidad en operación compleja que involucra múltiples entidades."""
        
        # Crear cuenta base
        account = Account(
            name=f"Complex Test Account {uuid.uuid4().hex[:8]}",
            api_key_hash="test_hash",
            api_key_prefix="test_prefix",
            api_key_last_chars="test_last",
            is_active=True
        )
        db_session.add(account)
        await db_session.flush()

        # Verificar estado inicial
        initial_product_count = await self._count_products_for_account(db_session, account.account_id)
        initial_user_count = await self._count_end_users_for_account(db_session, account.account_id)
        initial_interaction_count = await self._count_interactions_for_account(db_session, account.account_id)

        try:
            # Operación compleja que debe ser atómica
            async with db_session.begin():
                # Crear múltiples productos
                products = []
                for i in range(3):
                    product = Product(
                        account_id=account.account_id,
                        name=f"Product {i}",
                        description=f"Description {i}",
                        price=10.99 + i,
                        category=f"category_{i}",
                        inventory_count=100 + i
                    )
                    db_session.add(product)
                    products.append(product)

                await db_session.flush()

                # Crear múltiples usuarios
                end_users = []
                for i in range(2):
                    end_user = EndUser(
                        account_id=account.account_id,
                        external_user_id=f"ext_user_{i}_{uuid.uuid4().hex[:8]}",
                        user_attributes={"name": f"User {i}", "age": 25 + i}
                    )
                    db_session.add(end_user)
                    end_users.append(end_user)

                await db_session.flush()

                # Crear interacciones entre usuarios y productos
                for user in end_users:
                    for product in products:
                        interaction = Interaction(
                            account_id=account.account_id,
                            end_user_id=user.id,
                            product_id=product.id,
                            interaction_type="view",
                            interaction_value=1.0,
                            metadata={"test": True}
                        )
                        db_session.add(interaction)

                # Si llegamos aquí, la transacción debería completarse exitosamente

        except Exception as e:
            log_error(f"Transaction failed as expected: {e}")
            raise

        # Verificar que se crearon todas las entidades
        final_product_count = await self._count_products_for_account(db_session, account.account_id)
        final_user_count = await self._count_end_users_for_account(db_session, account.account_id)
        final_interaction_count = await self._count_interactions_for_account(db_session, account.account_id)

        assert final_product_count == initial_product_count + 3
        assert final_user_count == initial_user_count + 2
        assert final_interaction_count == initial_interaction_count + 6  # 2 users * 3 products

        log_info("✅ Complex multi-entity transaction atomicity test passed")

    async def test_transaction_rollback_on_constraint_violation(
        self, db_session: AsyncSession
    ):
        """Test que las transacciones hagan rollback correctamente en violaciones de constraints."""
        
        # Crear cuenta base
        account = Account(
            name=f"Rollback Test Account {uuid.uuid4().hex[:8]}",
            api_key_hash="test_hash",
            api_key_prefix="test_prefix", 
            api_key_last_chars="test_last",
            is_active=True
        )
        db_session.add(account)
        await db_session.flush()

        # Verificar estado inicial
        initial_product_count = await self._count_products_for_account(db_session, account.account_id)

        # Intentar operación que debe fallar
        with pytest.raises(Exception):
            async with db_session.begin():
                # Crear producto válido
                valid_product = Product(
                    account_id=account.account_id,
                    name="Valid Product",
                    description="Valid description",
                    price=99.99,
                    category="valid_category",
                    inventory_count=100
                )
                db_session.add(valid_product)

                # Crear producto inválido (violación de constraint)
                invalid_product = Product(
                    account_id=account.account_id,
                    name=None,  # Violación: name no puede ser NULL
                    description="Invalid description",
                    price=99.99,
                    category="invalid_category",
                    inventory_count=100
                )
                db_session.add(invalid_product)

                # Esto debería fallar y hacer rollback de toda la transacción
                await db_session.flush()

        # Verificar que NO se creó ningún producto (rollback completo)
        final_product_count = await self._count_products_for_account(db_session, account.account_id)
        assert final_product_count == initial_product_count

        log_info("✅ Transaction rollback on constraint violation test passed")

    async def test_batch_ingestion_atomicity_success(
        self, db_session: AsyncSession
    ):
        """Test que la ingesta en lote sea atómica cuando todo funciona correctamente."""

        # Crear cuenta base
        account = Account(
            name=f"Batch Test Account {uuid.uuid4().hex[:8]}",
            api_key_hash="test_hash",
            api_key_prefix="test_prefix",
            api_key_last_chars="test_last",
            is_active=True
        )
        db_session.add(account)
        await db_session.flush()

        # Mock services
        mock_limit_service = AsyncMock(spec=LimitService)
        mock_storage_service = AsyncMock()
        mock_storage_service.store_batch_data.return_value = "/tmp/test_batch_data.json"

        # Crear servicio de ingesta
        ingestion_service = DataIngestionService(
            db=db_session,
            account_id=account.account_id,
            limit_service=mock_limit_service,
            redis=AsyncMock(),
            storage_service=mock_storage_service
        )

        # Datos de ingesta válidos
        batch_data = BatchIngestionRequest(
            users=[
                {"external_user_id": "user1", "user_attributes": {"name": "User 1"}},
                {"external_user_id": "user2", "user_attributes": {"name": "User 2"}}
            ],
            products=[
                {"id": "prod1", "name": "Product 1", "price": 10.99},
                {"id": "prod2", "name": "Product 2", "price": 20.99}
            ],
            interactions=[
                {"user_id": "user1", "product_id": "prod1", "interaction_type": "view"},
                {"user_id": "user2", "product_id": "prod2", "interaction_type": "purchase"}
            ]
        )

        # Verificar estado inicial
        initial_job_count = await self._count_batch_jobs_for_account(db_session, account.account_id)

        # Mock Celery task
        with patch('src.workers.celery_app.celery_app.send_task') as mock_celery:
            mock_task = MagicMock()
            mock_task.id = "test_task_id"
            mock_celery.return_value = mock_task

            # Ejecutar ingesta en lote
            result = await ingestion_service.create_batch_ingestion_job(batch_data)

        # Verificar que se creó el job
        assert result is not None
        assert result["status"] == "processing"
        assert "job_id" in result
        assert result["total_users"] == 2
        assert result["total_products"] == 2
        assert result["total_interactions"] == 2

        # Verificar que se incrementó el contador de jobs
        final_job_count = await self._count_batch_jobs_for_account(db_session, account.account_id)
        assert final_job_count == initial_job_count + 1

        log_info("✅ Batch ingestion atomicity success test passed")

    async def test_batch_ingestion_atomicity_failure_storage_error(
        self, db_session: AsyncSession
    ):
        """Test que la ingesta en lote falle atómicamente si hay error en storage."""

        # Crear cuenta base
        account = Account(
            name=f"Batch Fail Test Account {uuid.uuid4().hex[:8]}",
            api_key_hash="test_hash",
            api_key_prefix="test_prefix",
            api_key_last_chars="test_last",
            is_active=True
        )
        db_session.add(account)
        await db_session.flush()

        # Mock services con error en storage
        mock_limit_service = AsyncMock(spec=LimitService)
        mock_storage_service = AsyncMock()
        mock_storage_service.store_batch_data.side_effect = Exception("Storage error")

        # Crear servicio de ingesta
        ingestion_service = DataIngestionService(
            db=db_session,
            account_id=account.account_id,
            limit_service=mock_limit_service,
            redis=AsyncMock(),
            storage_service=mock_storage_service
        )

        # Datos de ingesta
        batch_data = BatchIngestionRequest(
            users=[{"external_user_id": "user1", "user_attributes": {"name": "User 1"}}],
            products=[{"id": "prod1", "name": "Product 1", "price": 10.99}],
            interactions=[]
        )

        # Verificar estado inicial
        initial_job_count = await self._count_batch_jobs_for_account(db_session, account.account_id)

        # Intentar ingesta (debe fallar)
        with pytest.raises(Exception):
            await ingestion_service.create_batch_ingestion_job(batch_data)

        # Verificar que NO se creó ningún job (rollback completo)
        final_job_count = await self._count_batch_jobs_for_account(db_session, account.account_id)
        assert final_job_count == initial_job_count

        log_info("✅ Batch ingestion atomicity failure test passed")

    # Métodos auxiliares para contar entidades
    async def _count_accounts(self, db_session: AsyncSession) -> int:
        """Contar total de cuentas."""
        result = await db_session.execute(select(Account))
        return len(result.scalars().all())

    async def _count_system_users(self, db_session: AsyncSession) -> int:
        """Contar total de usuarios del sistema."""
        result = await db_session.execute(select(SystemUser))
        return len(result.scalars().all())

    async def _count_products_for_account(self, db_session: AsyncSession, account_id: int) -> int:
        """Contar productos para una cuenta específica."""
        result = await db_session.execute(
            select(Product).where(Product.account_id == account_id)
        )
        return len(result.scalars().all())

    async def _count_end_users_for_account(self, db_session: AsyncSession, account_id: int) -> int:
        """Contar end users para una cuenta específica."""
        result = await db_session.execute(
            select(EndUser).where(EndUser.account_id == account_id)
        )
        return len(result.scalars().all())

    async def _count_interactions_for_account(self, db_session: AsyncSession, account_id: int) -> int:
        """Contar interacciones para una cuenta específica."""
        result = await db_session.execute(
            select(Interaction).where(Interaction.account_id == account_id)
        )
        return len(result.scalars().all())

    async def _count_batch_jobs_for_account(self, db_session: AsyncSession, account_id: int) -> int:
        """Contar jobs de ingesta en lote para una cuenta específica."""
        result = await db_session.execute(
            select(BatchIngestionJob).where(BatchIngestionJob.account_id == account_id)
        )
        return len(result.scalars().all())
