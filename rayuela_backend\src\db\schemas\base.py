"""
Base Pydantic models with automatic camelCase to snake_case field mapping.
"""
from pydantic import BaseModel, ConfigDict
from typing import Any, Dict


def to_camel_case(snake_str: str) -> str:
    """Convert snake_case to camelCase"""
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])


def generate_alias_dict(model_fields: Dict[str, Any]) -> Dict[str, str]:
    """Generate alias dictionary for all fields in a model"""
    return {field_name: to_camel_case(field_name) for field_name in model_fields}


class CamelCaseModel(BaseModel):
    """
    Base model that automatically generates camelCase aliases for all fields.
    
    This allows the API to accept both snake_case (Python convention) and 
    camelCase (JavaScript/JSON convention) field names, while always 
    serializing to camelCase in JSON responses.
    
    Example:
        - Python field: account_name
        - JSON field: accountName
        - Both are accepted in requests
        - JSON responses use camelCase
    """
    
    model_config = ConfigDict(
        # Allow population by both field name and alias
        populate_by_name=True,
        # Generate camelCase aliases automatically
        alias_generator=to_camel_case,
        # Serialize using aliases (camelCase) in JSON output
        by_alias=True
    )
