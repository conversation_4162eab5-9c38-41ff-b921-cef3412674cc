#!/bin/bash

# Script de Validación Final de Implementaciones Críticas
# Verifica que todas las historias de usuario críticas están implementadas correctamente

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${BLUE}${BOLD}🏆 Validación Final de Implementaciones Críticas${NC}"
echo "=================================================="
echo ""

# Global counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to log check result
log_check() {
    local name="$1"
    local status="$2"
    local message="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ $name${NC}"
        [ -n "$message" ] && echo -e "   $message"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ $name${NC}"
        [ -n "$message" ] && echo -e "   $message"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    else
        echo -e "${YELLOW}⚠️ $name${NC}"
        [ -n "$message" ] && echo -e "   $message"
    fi
}

# Validation 1: VPC Security Implementation
validate_vpc_security() {
    echo -e "${BLUE}1. 🔒 Validando Implementación de Seguridad VPC${NC}"
    echo "-----------------------------------------------"
    
    # Check VPC connector configuration in cloudbuild files
    if grep -q "vpc-connector=rayuela-vpc-connector" cloudbuild.yaml; then
        log_check "VPC Connector en cloudbuild.yaml" "PASS" "Configurado para rayuela-backend y rayuela-frontend"
    else
        log_check "VPC Connector en cloudbuild.yaml" "FAIL" "No configurado correctamente"
    fi
    
    if grep -q "vpc-connector=rayuela-vpc-connector" cloudbuild-deploy-production.yaml; then
        local vpc_count=$(grep -c "vpc-connector=rayuela-vpc-connector" cloudbuild-deploy-production.yaml)
        if [ $vpc_count -eq 4 ]; then
            log_check "VPC Connector en producción" "PASS" "Configurado en todos los 4 servicios"
        else
            log_check "VPC Connector en producción" "WARN" "Configurado en $vpc_count/4 servicios"
        fi
    else
        log_check "VPC Connector en producción" "FAIL" "No configurado"
    fi
    
    # Check scripts existence
    local vpc_scripts=(
        "scripts/verify-vpc-security.sh"
        "scripts/setup-vpc-connector.sh"
        "scripts/configure-sql-private.sh"
        "scripts/configure-redis-private.sh"
    )
    
    local script_count=0
    for script in "${vpc_scripts[@]}"; do
        if [ -f "$script" ] && [ -x "$script" ]; then
            script_count=$((script_count + 1))
        fi
    done
    
    if [ $script_count -eq 4 ]; then
        log_check "Scripts de VPC" "PASS" "Todos los scripts creados y ejecutables"
    else
        log_check "Scripts de VPC" "WARN" "$script_count/4 scripts disponibles"
    fi
    
    # Check documentation
    if [ -f "docs/VPC_SECURITY_SETUP.md" ]; then
        local doc_size=$(wc -c < "docs/VPC_SECURITY_SETUP.md")
        if [ $doc_size -gt 5000 ]; then
            log_check "Documentación VPC" "PASS" "Guía completa disponible ($doc_size bytes)"
        else
            log_check "Documentación VPC" "WARN" "Documentación pequeña"
        fi
    else
        log_check "Documentación VPC" "FAIL" "No encontrada"
    fi
    
    echo ""
}

# Validation 2: OpenAPI Implementation
validate_openapi_implementation() {
    echo -e "${BLUE}2. 🔄 Validando Implementación de OpenAPI${NC}"
    echo "----------------------------------------"
    
    # Check OpenAPI specification file
    local openapi_file="rayuela_frontend/src/lib/openapi/openapi.json"
    if [ -f "$openapi_file" ]; then
        local file_size=$(wc -c < "$openapi_file")
        if [ $file_size -gt 100000 ]; then
            log_check "Especificación OpenAPI" "PASS" "Archivo completo ($file_size bytes)"
            
            # Check paths and schemas if jq is available
            if command -v jq &> /dev/null; then
                local paths_count=$(jq '.paths | length' "$openapi_file" 2>/dev/null || echo "0")
                local schemas_count=$(jq '.components.schemas // {} | length' "$openapi_file" 2>/dev/null || echo "0")
                
                if [ "$paths_count" -gt 50 ] && [ "$schemas_count" -gt 20 ]; then
                    log_check "Contenido OpenAPI" "PASS" "$paths_count endpoints, $schemas_count esquemas"
                else
                    log_check "Contenido OpenAPI" "WARN" "$paths_count endpoints, $schemas_count esquemas"
                fi
            fi
        else
            log_check "Especificación OpenAPI" "FAIL" "Archivo muy pequeño ($file_size bytes)"
        fi
    else
        log_check "Especificación OpenAPI" "FAIL" "Archivo no encontrado"
    fi
    
    # Check generated API client
    local client_file="rayuela_frontend/src/lib/generated/rayuelaAPI.ts"
    if [ -f "$client_file" ]; then
        local client_lines=$(wc -l < "$client_file")
        if [ $client_lines -gt 1000 ]; then
            log_check "Cliente API generado" "PASS" "$client_lines líneas de código"
            
            # Check for key exports
            if grep -q "export const getRayuela" "$client_file"; then
                log_check "Exports del cliente" "PASS" "getRayuela exportado correctamente"
            else
                log_check "Exports del cliente" "FAIL" "getRayuela no encontrado"
            fi
            
            local type_count=$(grep -c "export type" "$client_file" || echo "0")
            if [ $type_count -gt 100 ]; then
                log_check "Tipos TypeScript" "PASS" "$type_count tipos exportados"
            else
                log_check "Tipos TypeScript" "WARN" "Solo $type_count tipos"
            fi
        else
            log_check "Cliente API generado" "FAIL" "Archivo muy pequeño ($client_lines líneas)"
        fi
    else
        log_check "Cliente API generado" "FAIL" "Archivo no encontrado"
    fi
    
    # Check Orval configuration
    if [ -f "rayuela_frontend/orval.config.ts" ]; then
        log_check "Configuración Orval" "PASS" "Archivo de configuración presente"
    else
        log_check "Configuración Orval" "FAIL" "Configuración no encontrada"
    fi
    
    # Check scripts
    local openapi_scripts=(
        "scripts/fix-openapi-generation.sh"
        "scripts/verify-openapi-implementation.sh"
    )
    
    local openapi_script_count=0
    for script in "${openapi_scripts[@]}"; do
        if [ -f "$script" ] && [ -x "$script" ]; then
            openapi_script_count=$((openapi_script_count + 1))
        fi
    done
    
    if [ $openapi_script_count -eq 2 ]; then
        log_check "Scripts de OpenAPI" "PASS" "Scripts de diagnóstico y verificación disponibles"
    else
        log_check "Scripts de OpenAPI" "WARN" "$openapi_script_count/2 scripts disponibles"
    fi
    
    echo ""
}

# Validation 3: Backend Configuration
validate_backend_configuration() {
    echo -e "${BLUE}3. ⚙️ Validando Configuración del Backend${NC}"
    echo "----------------------------------------"
    
    # Check main.py configuration
    if [ -f "rayuela_backend/main.py" ]; then
        if grep -q "openapi_url=\"/api/openapi.json\"" rayuela_backend/main.py; then
            log_check "Configuración FastAPI" "PASS" "OpenAPI URL configurada correctamente"
        else
            log_check "Configuración FastAPI" "WARN" "Verificar configuración OpenAPI URL"
        fi
        
        if grep -q "app.include_router" rayuela_backend/main.py; then
            local router_count=$(grep -c "app.include_router" rayuela_backend/main.py)
            log_check "Routers incluidos" "PASS" "$router_count routers configurados"
        else
            log_check "Routers incluidos" "FAIL" "No se encontraron routers"
        fi
    else
        log_check "Archivo main.py" "FAIL" "No encontrado"
    fi
    
    # Check API structure
    if [ -d "rayuela_backend/src/api/v1" ]; then
        log_check "Estructura API" "PASS" "Estructura v1 presente"
    else
        log_check "Estructura API" "FAIL" "Estructura API no encontrada"
    fi
    
    echo ""
}

# Validation 4: Documentation and Scripts
validate_documentation_and_scripts() {
    echo -e "${BLUE}4. 📚 Validando Documentación y Scripts${NC}"
    echo "--------------------------------------"
    
    # Check implementation summaries
    local docs=(
        "docs/IMPLEMENTATION_SUMMARY_CRITICAL_TASKS.md"
        "docs/VPC_SECURITY_SETUP.md"
    )
    
    local doc_count=0
    for doc in "${docs[@]}"; do
        if [ -f "$doc" ]; then
            doc_count=$((doc_count + 1))
        fi
    done
    
    if [ $doc_count -eq 2 ]; then
        log_check "Documentación de implementación" "PASS" "Resúmenes y guías completas"
    else
        log_check "Documentación de implementación" "WARN" "$doc_count/2 documentos encontrados"
    fi
    
    # Check TODO.md updates
    if [ -f "docs/TODO.md" ]; then
        if grep -q "✅ COMPLETADA" docs/TODO.md; then
            local completed_count=$(grep -c "✅ COMPLETADA" docs/TODO.md)
            log_check "Historias de usuario actualizadas" "PASS" "$completed_count historias marcadas como completadas"
        else
            log_check "Historias de usuario actualizadas" "WARN" "No se encontraron historias completadas"
        fi
    else
        log_check "Archivo TODO.md" "FAIL" "No encontrado"
    fi
    
    # Check script executability
    local all_scripts=(
        "scripts/verify-vpc-security.sh"
        "scripts/setup-vpc-connector.sh"
        "scripts/configure-sql-private.sh"
        "scripts/configure-redis-private.sh"
        "scripts/fix-openapi-generation.sh"
        "scripts/verify-openapi-implementation.sh"
        "scripts/validate-critical-implementations.sh"
    )
    
    local executable_count=0
    for script in "${all_scripts[@]}"; do
        if [ -f "$script" ] && [ -x "$script" ]; then
            executable_count=$((executable_count + 1))
        fi
    done
    
    if [ $executable_count -eq ${#all_scripts[@]} ]; then
        log_check "Scripts ejecutables" "PASS" "Todos los scripts son ejecutables"
    else
        log_check "Scripts ejecutables" "WARN" "$executable_count/${#all_scripts[@]} scripts ejecutables"
    fi
    
    echo ""
}

# Function to show final summary
show_final_summary() {
    echo ""
    echo "======================================================"
    echo -e "${BLUE}${BOLD}📊 Resumen Final de Validación${NC}"
    echo "======================================================"
    
    local success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    
    echo -e "${BOLD}Estadísticas de Validación:${NC}"
    echo "  Total de verificaciones: $TOTAL_CHECKS"
    echo -e "  ${GREEN}Exitosas: $PASSED_CHECKS${NC}"
    echo -e "  ${RED}Fallidas: $FAILED_CHECKS${NC}"
    echo -e "  ${YELLOW}Advertencias: $((TOTAL_CHECKS - PASSED_CHECKS - FAILED_CHECKS))${NC}"
    echo "  Tasa de éxito: $success_rate%"
    echo ""
    
    if [ $success_rate -ge 90 ]; then
        echo -e "${GREEN}${BOLD}🎉 EXCELENTE: Implementaciones críticas completadas exitosamente!${NC}"
        echo -e "${GREEN}✅ El proyecto está listo para la siguiente fase de implementación${NC}"
        return 0
    elif [ $success_rate -ge 70 ]; then
        echo -e "${YELLOW}${BOLD}⚠️ BUENO: Implementaciones mayormente completas${NC}"
        echo -e "${YELLOW}🔧 Revisar puntos de advertencia antes de continuar${NC}"
        return 1
    else
        echo -e "${RED}${BOLD}❌ REQUIERE ATENCIÓN: Implementaciones incompletas${NC}"
        echo -e "${RED}🚨 Resolver problemas críticos antes de continuar${NC}"
        return 2
    fi
}

# Function to provide next steps
provide_next_steps() {
    echo ""
    echo -e "${BLUE}${BOLD}🚀 Próximos Pasos Recomendados${NC}"
    echo "================================"
    echo ""
    
    if [ $FAILED_CHECKS -gt 0 ]; then
        echo -e "${RED}🔥 CRÍTICO - Resolver problemas fallidos:${NC}"
        echo "1. Revisar logs de validación arriba"
        echo "2. Ejecutar scripts de reparación si es necesario"
        echo "3. Re-ejecutar esta validación"
        echo ""
    fi
    
    echo -e "${YELLOW}⚙️ CONFIGURACIÓN DE INFRAESTRUCTURA:${NC}"
    echo "1. Ejecutar configuración de VPC:"
    echo "   ./scripts/setup-vpc-connector.sh"
    echo "   ./scripts/configure-sql-private.sh"
    echo "   ./scripts/configure-redis-private.sh"
    echo ""
    echo "2. Verificar configuración:"
    echo "   ./scripts/verify-vpc-security.sh"
    echo ""
    
    echo -e "${BLUE}🔄 DESARROLLO CONTINUO:${NC}"
    echo "1. Refactorizar frontend para usar cliente OpenAPI generado"
    echo "2. Agregar generación automática de OpenAPI en CI/CD"
    echo "3. Implementar siguientes historias de usuario de alta prioridad"
    echo ""
    
    echo -e "${GREEN}📈 MONITOREO:${NC}"
    echo "1. Ejecutar esta validación regularmente:"
    echo "   ./scripts/validate-critical-implementations.sh"
    echo "2. Monitorear métricas de seguridad VPC"
    echo "3. Verificar sincronización OpenAPI en deploys"
}

# Main execution
echo "🚀 Iniciando validación de implementaciones críticas..."
echo ""

# Check if we're in the right directory
if [ ! -d "rayuela_backend" ] || [ ! -d "rayuela_frontend" ]; then
    echo -e "${RED}❌ Error: Execute this script from the project root directory${NC}"
    echo "Expected structure:"
    echo "  project-root/"
    echo "  ├── rayuela_backend/"
    echo "  └── rayuela_frontend/"
    exit 1
fi

# Execute all validations
validate_vpc_security
validate_openapi_implementation
validate_backend_configuration
validate_documentation_and_scripts

# Show results and next steps
show_final_summary
EXIT_CODE=$?

provide_next_steps

echo ""
echo "======================================================"
echo -e "${BLUE}${BOLD}🔧 Validación de implementaciones críticas completada${NC}"
echo "======================================================"

exit $EXIT_CODE 