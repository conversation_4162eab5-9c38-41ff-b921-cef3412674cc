#!/usr/bin/env python3
"""
Script para ejecutar todas las verificaciones de calidad de código localmente.
Replica exactamente las mismas verificaciones que se ejecutan en CI/CD.

Este script debe ser ejecutado antes de hacer commit para asegurar que
el código pase todas las verificaciones del pipeline.

Uso:
    python scripts/run_code_quality_checks.py
    python scripts/run_code_quality_checks.py --fix  # Para auto-corregir problemas
    python scripts/run_code_quality_checks.py --security-only  # Solo security checks
"""

import argparse
import subprocess
import sys
import logging
from pathlib import Path
from typing import List, Tuple
import json

# Configurar logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CodeQualityChecker:
    """Ejecutor de verificaciones de calidad de código."""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.src_dir = project_root / "src"
        self.tests_dir = project_root / "tests"
        self.success_count = 0
        self.failure_count = 0
        self.results = []

    def run_command(
        self, command: List[str], check_name: str, critical: bool = True
    ) -> bool:
        """Ejecutar un comando y registrar el resultado."""
        try:
            logger.info(f"🔍 Ejecutando: {check_name}")
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True,
            )

            logger.info(f"✅ {check_name}: PASÓ")
            self.success_count += 1
            self.results.append(
                {
                    "check": check_name,
                    "status": "PASS",
                    "output": result.stdout.strip() if result.stdout else "",
                    "critical": critical,
                }
            )
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {check_name}: FALLÓ")
            if e.stdout:
                logger.error(f"STDOUT: {e.stdout}")
            if e.stderr:
                logger.error(f"STDERR: {e.stderr}")

            self.failure_count += 1
            self.results.append(
                {
                    "check": check_name,
                    "status": "FAIL",
                    "output": e.stdout.strip() if e.stdout else "",
                    "error": e.stderr.strip() if e.stderr else "",
                    "critical": critical,
                }
            )
            return False

        except Exception as e:
            logger.error(f"💥 {check_name}: ERROR - {str(e)}")
            self.failure_count += 1
            self.results.append(
                {
                    "check": check_name,
                    "status": "ERROR",
                    "error": str(e),
                    "critical": critical,
                }
            )
            return False

    def check_formatting(self, fix: bool = False) -> bool:
        """Verificar formato de código con Black."""
        if fix:
            command = ["black", "src/", "tests/"]
            check_name = "Black (auto-fix)"
        else:
            command = ["black", "--check", "src/", "tests/"]
            check_name = "Black (format check)"

        return self.run_command(command, check_name, critical=True)

    def check_import_sorting(self, fix: bool = False) -> bool:
        """Verificar orden de imports con isort."""
        if fix:
            command = ["isort", "src/", "tests/"]
            check_name = "isort (auto-fix)"
        else:
            command = ["isort", "--check-only", "src/", "tests/"]
            check_name = "isort (import order check)"

        return self.run_command(command, check_name, critical=True)

    def check_linting(self) -> bool:
        """Verificar estilo de código con flake8."""
        command = ["flake8", "src/", "tests/"]
        return self.run_command(command, "flake8 (code style)", critical=True)

    def check_type_annotations(self) -> bool:
        """Verificar tipos con MyPy."""
        command = [
            "mypy",
            "src/",
            "tests/",
            "--ignore-missing-imports",
            "--show-error-codes",
            "--pretty",
        ]
        return self.run_command(command, "MyPy (type checking)", critical=True)

    def check_security_bandit(self) -> bool:
        """Verificar vulnerabilidades de código con Bandit."""
        command = [
            "bandit",
            "-r",
            "src/",
            "-ll",  # Low level and above
            "-f",
            "json",
            "-o",
            "bandit_report.json",
        ]
        return self.run_command(command, "Bandit (security scan)", critical=True)

    def check_security_safety(self) -> bool:
        """Verificar vulnerabilidades en dependencias con Safety."""
        command = ["safety", "check", "--json", "--output", "safety_report.json"]
        return self.run_command(
            command, "Safety (dependency vulnerabilities)", critical=True
        )

    def check_security_pip_audit(self) -> bool:
        """Verificar dependencias con pip-audit."""
        command = ["pip-audit", "--format=json", "--output=pip_audit_report.json"]
        return self.run_command(command, "pip-audit (dependency audit)", critical=True)

    def run_tests(self) -> bool:
        """Ejecutar tests unitarios básicos."""
        command = [
            "python",
            "-m",
            "pytest",
            "tests/unit/",
            "-v",
            "--tb=short",
            "-x",  # Stop on first failure
        ]
        return self.run_command(command, "Unit Tests", critical=True)

    def generate_report(self) -> dict:
        """Generar reporte de resultados."""
        critical_failures = [
            r
            for r in self.results
            if r["status"] in ["FAIL", "ERROR"] and r.get("critical", False)
        ]

        report = {
            "summary": {
                "total_checks": len(self.results),
                "successful": self.success_count,
                "failed": self.failure_count,
                "critical_failures": len(critical_failures),
                "overall_status": "PASS" if len(critical_failures) == 0 else "FAIL",
            },
            "details": self.results,
            "critical_failures": critical_failures,
        }

        return report


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(
        description="Ejecutar verificaciones de calidad de código"
    )
    parser.add_argument(
        "--fix", action="store_true", help="Auto-corregir problemas cuando sea posible"
    )
    parser.add_argument(
        "--security-only",
        action="store_true",
        help="Ejecutar solo verificaciones de seguridad",
    )
    parser.add_argument(
        "--no-tests", action="store_true", help="No ejecutar tests unitarios"
    )
    parser.add_argument(
        "--output",
        default="code_quality_report.json",
        help="Archivo de salida para el reporte",
    )

    args = parser.parse_args()

    # Determinar directorio del proyecto
    project_root = Path(__file__).parent.parent

    logger.info("🚀 Iniciando verificaciones de calidad de código...")
    logger.info(f"📁 Directorio del proyecto: {project_root}")

    checker = CodeQualityChecker(project_root)

    try:
        # Verificaciones de formato y estilo
        if not args.security_only:
            logger.info("\n📝 === VERIFICACIONES DE FORMATO Y ESTILO ===")
            checker.check_formatting(fix=args.fix)
            checker.check_import_sorting(fix=args.fix)
            checker.check_linting()
            checker.check_type_annotations()

        # Verificaciones de seguridad
        logger.info("\n🛡️ === VERIFICACIONES DE SEGURIDAD ===")
        checker.check_security_bandit()
        checker.check_security_safety()
        checker.check_security_pip_audit()

        # Tests unitarios
        if not args.no_tests and not args.security_only:
            logger.info("\n🧪 === TESTS UNITARIOS ===")
            checker.run_tests()

        # Generar reporte
        report = checker.generate_report()

        # Guardar reporte
        with open(args.output, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # Mostrar resumen
        logger.info("\n" + "=" * 60)
        logger.info("📊 RESUMEN DE VERIFICACIONES DE CALIDAD")
        logger.info("=" * 60)
        logger.info(f"Estado general: {report['summary']['overall_status']}")
        logger.info(f"Total de verificaciones: {report['summary']['total_checks']}")
        logger.info(f"Exitosas: {report['summary']['successful']}")
        logger.info(f"Fallidas: {report['summary']['failed']}")
        logger.info(f"Fallas críticas: {report['summary']['critical_failures']}")
        logger.info(f"Reporte guardado en: {args.output}")

        if report["summary"]["critical_failures"] > 0:
            logger.error("\n🚨 FALLAS CRÍTICAS ENCONTRADAS:")
            for failure in report["critical_failures"]:
                logger.error(
                    f"- {failure['check']}: {failure.get('error', 'Ver detalles en reporte')}"
                )

            logger.error(
                "\n❌ VERIFICACIONES FALLARON - Corregir problemas antes del commit"
            )
            sys.exit(1)
        else:
            logger.info(
                "\n✅ TODAS las verificaciones PASARON - Código listo para commit"
            )
            sys.exit(0)

    except Exception as e:
        logger.error(f"💥 Error durante las verificaciones: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
