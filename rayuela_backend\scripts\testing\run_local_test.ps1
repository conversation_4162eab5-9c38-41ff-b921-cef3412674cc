# Script para ejecutar pruebas locales de Rayuela en Windows
# Este script configura y ejecuta todos los componentes necesarios para probar Rayuela localmente

# Función para limpiar procesos al salir
function Cleanup {
    Write-Host "`nLimpiando procesos..." -ForegroundColor Yellow

    # Detener contenedores Docker
    if ($dockerStarted) {
        Write-Host "Deteniendo contenedores Docker..." -ForegroundColor Yellow
        docker-compose -f docker-compose.simple.yml down
    }

    # Detener procesos
    foreach ($process in $processes) {
        if (-not $process.HasExited) {
            Write-Host "Deteniendo proceso $($process.Id)..." -ForegroundColor Yellow
            Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
        }
    }

    Write-Host "Limpieza completada." -ForegroundColor Green
}

# Inicializar variables
$rootDir = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$processes = @()
$dockerStarted = $false

# Cambiar al directorio raíz
Set-Location $rootDir

# Parsear argumentos
param(
    [switch]$SkipDocker,
    [switch]$SkipDbInit,
    [switch]$SkipApi,
    [switch]$SkipWorkers,
    [switch]$SkipTests
)

try {
    # Iniciar servicios de Docker
    if (-not $SkipDocker) {
        Write-Host "Iniciando servicios de Docker..." -ForegroundColor Cyan

        # Verificar si docker-compose.simple.yml existe
        if (Test-Path "docker-compose.simple.yml") {
            docker-compose -f docker-compose.simple.yml up -d
        } else {
            docker-compose up -d db redis
        }

        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error al iniciar servicios de Docker." -ForegroundColor Red
            exit 1
        }

        $dockerStarted = $true
        Write-Host "Servicios de Docker iniciados correctamente." -ForegroundColor Green
        Start-Sleep -Seconds 5  # Esperar a que los servicios estén listos
    }

    # Activar entorno virtual si existe
    if (Test-Path "venv\Scripts\Activate.ps1") {
        Write-Host "Activando entorno virtual..." -ForegroundColor Cyan
        & .\venv\Scripts\Activate.ps1
    }

    # Inicializar base de datos
    if (-not $SkipDbInit) {
        Write-Host "Inicializando base de datos..." -ForegroundColor Cyan

        # Primero, crear la base de datos si no existe
        Write-Host "Creando base de datos si no existe..." -ForegroundColor Cyan
        python -m scripts.start create-db

        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error al crear la base de datos." -ForegroundColor Red
            exit 1
        }

        # Luego, aplicar migraciones
        Write-Host "Aplicando migraciones..." -ForegroundColor Cyan
        python -m scripts.start migrate-db

        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error al aplicar migraciones." -ForegroundColor Red
            exit 1
        }

        # Finalmente, insertar datos iniciales
        Write-Host "Insertando datos iniciales..." -ForegroundColor Cyan
        python -m scripts.start seed-db

        # No verificamos el código de salida aquí porque los datos iniciales podrían ser opcionales

        Write-Host "Base de datos inicializada correctamente." -ForegroundColor Green
    }

    # Iniciar servidor API
    if (-not $SkipApi) {
        Write-Host "Iniciando servidor API..." -ForegroundColor Cyan
        $apiProcess = Start-Process -FilePath "python" -ArgumentList "main.py" -PassThru -WindowStyle Normal
        $processes += $apiProcess

        Write-Host "Servidor API iniciado correctamente (PID: $($apiProcess.Id))." -ForegroundColor Green
        Start-Sleep -Seconds 3  # Esperar a que el servidor esté listo
    }

    # Iniciar workers de Celery
    if (-not $SkipWorkers) {
        Write-Host "Iniciando workers de Celery..." -ForegroundColor Cyan

        # Worker para tareas por defecto
        $defaultWorkerProcess = Start-Process -FilePath "celery" -ArgumentList "-A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=10 --queues=default --hostname=default@%h" -PassThru -WindowStyle Normal
        $processes += $defaultWorkerProcess

        # Worker para tareas de entrenamiento
        $trainingWorkerProcess = Start-Process -FilePath "celery" -ArgumentList "-A src.workers.celery_app worker --loglevel=info --concurrency=1 --max-tasks-per-child=1 --queues=training --hostname=training@%h" -PassThru -WindowStyle Normal
        $processes += $trainingWorkerProcess

        # Celery Beat para tareas periódicas
        $beatProcess = Start-Process -FilePath "celery" -ArgumentList "-A src.workers.celery_app beat --loglevel=info" -PassThru -WindowStyle Normal
        $processes += $beatProcess

        Write-Host "Workers de Celery iniciados correctamente." -ForegroundColor Green
        Start-Sleep -Seconds 3  # Esperar a que los workers estén listos
    }

    # Ejecutar pruebas
    if (-not $SkipTests) {
        Write-Host "Ejecutando pruebas..." -ForegroundColor Cyan
        python -m scripts.run_tests all

        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error al ejecutar las pruebas." -ForegroundColor Red
            exit 1
        }

        Write-Host "Pruebas ejecutadas correctamente." -ForegroundColor Green
    }

    Write-Host "`nTodo el proceso se completó correctamente." -ForegroundColor Green
    Write-Host "Presiona Ctrl+C para detener todos los servicios." -ForegroundColor Yellow

    # Mantener el script en ejecución hasta que el usuario lo detenga
    while ($true) {
        Start-Sleep -Seconds 1
    }
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
}
finally {
    # Limpiar al salir
    Cleanup
}
