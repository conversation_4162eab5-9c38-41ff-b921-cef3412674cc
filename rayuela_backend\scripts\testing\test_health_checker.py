#!/usr/bin/env python3
"""
Script para diagnosticar y generar reporte del estado de los tests en Rayuela.
Identifica tests obsoletos, dependencias faltantes y problemas de compatibilidad.
"""

import os
import sys
import subprocess
import ast
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple
import importlib.util


def find_all_test_files(test_dir: str) -> List[Path]:
    """Encuentra todos los archivos de test en el directorio especificado."""
    test_files = []
    test_path = Path(test_dir)

    for file_path in test_path.rglob("test_*.py"):
        test_files.append(file_path)

    return test_files


def analyze_imports(file_path: Path) -> Tuple[Set[str], Set[str]]:
    """Analiza las importaciones de un archivo de test."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        tree = ast.parse(content, filename=str(file_path))

        imports = set()
        from_imports = set()

        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    from_imports.add(node.module)

        return imports, from_imports
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
        return set(), set()


def check_import_availability(module_name: str) -> bool:
    """Verifica si un módulo puede ser importado."""
    try:
        spec = importlib.util.find_spec(module_name)
        return spec is not None
    except (ImportError, ModuleNotFoundError, ValueError):
        return False


def extract_test_functions(file_path: Path) -> List[str]:
    """Extrae los nombres de las funciones de test de un archivo."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        tree = ast.parse(content, filename=str(file_path))
        test_functions = []

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name.startswith("test_"):
                test_functions.append(node.name)

        return test_functions
    except Exception as e:
        print(f"Error extracting test functions from {file_path}: {e}")
        return []


def check_deprecated_patterns(file_path: Path) -> List[str]:
    """Busca patrones de código que pueden estar obsoletos."""
    deprecated_patterns = []

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Patrones a buscar
        patterns = {
            "pytest.yield_fixture": "Use @pytest.fixture instead",
            '@pytest.fixture(scope="function")': "Function scope is default, can be omitted",
            "asyncio.get_event_loop()": "Use asyncio.run() or async context managers",
            "unittest.TestCase": "Consider using pytest style tests",
            "from src.": "Check if import paths are still valid",
            "TestClient": "Ensure FastAPI TestClient is being used correctly",
        }

        for pattern, suggestion in patterns.items():
            if pattern in content:
                deprecated_patterns.append(f"{pattern}: {suggestion}")

        return deprecated_patterns
    except Exception as e:
        print(f"Error checking deprecated patterns in {file_path}: {e}")
        return []


def run_syntax_check(file_path: Path) -> bool:
    """Verifica si el archivo tiene errores de sintaxis."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        ast.parse(content, filename=str(file_path))
        return True
    except SyntaxError:
        return False
    except Exception:
        return False


def generate_test_report(test_dir: str = "tests") -> Dict:
    """Genera un reporte completo del estado de los tests."""
    print("🔍 Analizando tests de Rayuela...")

    test_files = find_all_test_files(test_dir)
    report = {
        "summary": {
            "total_files": len(test_files),
            "syntax_errors": 0,
            "import_issues": 0,
            "deprecated_patterns": 0,
            "test_functions": 0,
        },
        "files": {},
        "issues": {
            "syntax_errors": [],
            "import_failures": [],
            "deprecated_patterns": {},
            "missing_modules": set(),
        },
        "recommendations": [],
    }

    for file_path in test_files:
        relative_path = str(file_path.relative_to(Path(test_dir).parent))
        print(f"📄 Analizando: {relative_path}")

        file_report = {
            "path": relative_path,
            "syntax_ok": run_syntax_check(file_path),
            "imports": [],
            "from_imports": [],
            "test_functions": [],
            "deprecated_patterns": [],
            "import_issues": [],
        }

        # Análisis de sintaxis
        if not file_report["syntax_ok"]:
            report["summary"]["syntax_errors"] += 1
            report["issues"]["syntax_errors"].append(relative_path)
            print(f"  ❌ Error de sintaxis")

        # Análisis de importaciones
        imports, from_imports = analyze_imports(file_path)
        file_report["imports"] = list(imports)
        file_report["from_imports"] = list(from_imports)

        # Verificar disponibilidad de módulos
        all_modules = imports.union(from_imports)
        for module in all_modules:
            # Filtrar módulos de sistema y relativos
            if not module.startswith(".") and not module.startswith("src."):
                if not check_import_availability(module):
                    file_report["import_issues"].append(module)
                    report["issues"]["missing_modules"].add(module)

        if file_report["import_issues"]:
            report["summary"]["import_issues"] += 1
            report["issues"]["import_failures"].append(
                {"file": relative_path, "missing_modules": file_report["import_issues"]}
            )
            print(f"  ⚠️  Módulos faltantes: {', '.join(file_report['import_issues'])}")

        # Extraer funciones de test
        test_functions = extract_test_functions(file_path)
        file_report["test_functions"] = test_functions
        report["summary"]["test_functions"] += len(test_functions)

        # Buscar patrones obsoletos
        deprecated = check_deprecated_patterns(file_path)
        if deprecated:
            file_report["deprecated_patterns"] = deprecated
            report["summary"]["deprecated_patterns"] += len(deprecated)
            report["issues"]["deprecated_patterns"][relative_path] = deprecated
            print(f"  📅 Patrones obsoletos encontrados: {len(deprecated)}")

        report["files"][relative_path] = file_report
        print(f"  ✅ Funciones de test: {len(test_functions)}")

    # Generar recomendaciones
    recommendations = []

    if report["summary"]["syntax_errors"] > 0:
        recommendations.append(
            {
                "priority": "HIGH",
                "category": "Syntax",
                "description": f"Corregir {report['summary']['syntax_errors']} archivos con errores de sintaxis",
                "files": report["issues"]["syntax_errors"],
            }
        )

    if report["issues"]["missing_modules"]:
        recommendations.append(
            {
                "priority": "HIGH",
                "category": "Dependencies",
                "description": "Instalar módulos faltantes o actualizar imports",
                "modules": list(report["issues"]["missing_modules"]),
            }
        )

    if report["summary"]["deprecated_patterns"] > 0:
        recommendations.append(
            {
                "priority": "MEDIUM",
                "category": "Code Quality",
                "description": f"Actualizar {report['summary']['deprecated_patterns']} patrones obsoletos",
                "details": report["issues"]["deprecated_patterns"],
            }
        )

    # Recomendación para Docker
    recommendations.append(
        {
            "priority": "HIGH",
            "category": "Environment",
            "description": "Usar Docker para ejecutar tests y evitar problemas de dependencias",
            "command": "docker-compose -f docker-compose.test.yml up --build",
        }
    )

    report["recommendations"] = recommendations

    return report


def print_summary(report: Dict):
    """Imprime un resumen del reporte."""
    summary = report["summary"]

    print("\n" + "=" * 60)
    print("📊 RESUMEN DEL ANÁLISIS DE TESTS")
    print("=" * 60)
    print(f"📁 Total de archivos de test: {summary['total_files']}")
    print(f"🔧 Funciones de test encontradas: {summary['test_functions']}")
    print(f"❌ Archivos con errores de sintaxis: {summary['syntax_errors']}")
    print(f"⚠️  Archivos con problemas de imports: {summary['import_issues']}")
    print(f"📅 Archivos con patrones obsoletos: {summary['deprecated_patterns']}")

    if report["issues"]["missing_modules"]:
        print(f"\n🔍 MÓDULOS FALTANTES:")
        for module in sorted(report["issues"]["missing_modules"]):
            print(f"  - {module}")

    print(f"\n🎯 RECOMENDACIONES:")
    for i, rec in enumerate(report["recommendations"], 1):
        priority_emoji = (
            "🔥"
            if rec["priority"] == "HIGH"
            else "⚡" if rec["priority"] == "MEDIUM" else "💡"
        )
        print(
            f"  {i}. {priority_emoji} [{rec['priority']}] {rec['category']}: {rec['description']}"
        )


def save_report(report: Dict, output_file: str = "test_health_report.json"):
    """Guarda el reporte en un archivo JSON."""
    # Convertir sets a listas para serialización JSON
    if "missing_modules" in report["issues"]:
        report["issues"]["missing_modules"] = list(report["issues"]["missing_modules"])

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    print(f"\n💾 Reporte guardado en: {output_file}")


def main():
    """Función principal del script."""
    print("🚀 Iniciando diagnóstico de tests de Rayuela")

    test_dir = "tests"
    if not os.path.exists(test_dir):
        print(f"❌ Directorio de tests no encontrado: {test_dir}")
        sys.exit(1)

    # Generar reporte
    report = generate_test_report(test_dir)

    # Mostrar resumen
    print_summary(report)

    # Guardar reporte
    save_report(report)

    print(
        "\n✅ Diagnóstico completado. Revisa el archivo test_health_report.json para detalles."
    )


if __name__ == "__main__":
    main()
