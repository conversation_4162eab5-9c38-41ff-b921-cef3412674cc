#!/usr/bin/env python3
"""
Script de validación RLS para producción con datos reales.
Diseñado para ejecutarse de manera segura en producción sin afectar datos.

Este script valida que:
1. RLS esté funcionando correctamente con tenants reales
2. Los datos estén correctamente aislados
3. No haya filtraciones de datos entre tenants

Uso:
    python -m scripts.validate_rls_production --tenant-id 123 --verify-isolation
"""

import asyncio
import argparse
import logging
import sys
from typing import List, Dict, Any, Optional
from sqlalchemy import text
from datetime import datetime

from src.db.session import get_db
from src.core.config import settings

# Configurar logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Tablas críticas para validación
CRITICAL_TABLES = [
    "products",
    "end_users", 
    "interactions",
    "orders",
    "api_keys",
    "system_users"
]

class ProductionRLSValidator:
    """Validador de RLS para entorno de producción."""
    
    def __init__(self, tenant_id: Optional[int] = None):
        self.tenant_id = tenant_id
        self.validation_results = []
        
    async def get_available_tenants(self) -> List[int]:
        """Obtener lista de tenants disponibles en el sistema."""
        async with get_db() as db:
            try:
                result = await db.execute(
                    text("SELECT DISTINCT account_id FROM accounts WHERE account_id IS NOT NULL LIMIT 5")
                )
                return [row[0] for row in result.fetchall()]
            except Exception as e:
                logger.error(f"Error obteniendo tenants: {e}")
                return []
    
    async def validate_table_isolation(self, table_name: str, tenant_ids: List[int]) -> Dict[str, Any]:
        """Validar que una tabla respete el aislamiento por tenant."""
        results = {
            "table": table_name,
            "isolation_working": True,
            "tenant_data_counts": {},
            "cross_tenant_leaks": [],
            "errors": []
        }
        
        async with get_db() as db:
            try:
                # Verificar que RLS esté habilitado
                rls_check = await db.execute(
                    text("""
                        SELECT c.relrowsecurity 
                        FROM pg_class c 
                        JOIN pg_namespace n ON c.relnamespace = n.oid 
                        WHERE c.relname = :table_name AND n.nspname = 'public'
                    """),
                    {"table_name": table_name}
                )
                
                rls_enabled = rls_check.fetchone()
                if not rls_enabled or not rls_enabled[0]:
                    results["errors"].append(f"RLS no está habilitado en {table_name}")
                    results["isolation_working"] = False
                    return results
                
                # Probar aislamiento para cada tenant
                for tenant_id in tenant_ids:
                    # Establecer contexto del tenant
                    await db.execute(text("SET app.tenant_id = :tenant_id"), {"tenant_id": tenant_id})
                    
                    # Contar registros accesibles
                    count_result = await db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    count = count_result.scalar()
                    results["tenant_data_counts"][tenant_id] = count
                    
                    # Verificar que solo se acceda a datos del tenant actual
                    if table_name in ["products", "end_users", "interactions", "orders"]:
                        verification_result = await db.execute(
                            text(f"""
                                SELECT COUNT(*) FROM {table_name} 
                                WHERE account_id != :tenant_id
                            """),
                            {"tenant_id": tenant_id}
                        )
                        
                        cross_tenant_count = verification_result.scalar()
                        if cross_tenant_count > 0:
                            results["cross_tenant_leaks"].append({
                                "tenant_id": tenant_id,
                                "leaked_records": cross_tenant_count
                            })
                            results["isolation_working"] = False
                
                # Limpiar contexto
                await db.execute(text("SET app.tenant_id = ''"))
                
            except Exception as e:
                results["errors"].append(f"Error validando {table_name}: {str(e)}")
                results["isolation_working"] = False
        
        return results
    
    async def validate_rls_enforcement(self) -> Dict[str, Any]:
        """Validar que RLS se aplique correctamente sin contexto de tenant."""
        results = {
            "enforcement_working": True,
            "accessible_without_tenant": {},
            "errors": []
        }
        
        async with get_db() as db:
            try:
                # Limpiar cualquier contexto de tenant
                await db.execute(text("SET app.tenant_id = ''"))
                
                # Verificar acceso sin tenant_id configurado
                for table in CRITICAL_TABLES:
                    try:
                        result = await db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.scalar()
                        results["accessible_without_tenant"][table] = count
                        
                        # En un sistema bien configurado, deberían ser 0 registros
                        # o muy pocos (dependiendo de las políticas)
                        if count > 100:  # Threshold configurable
                            results["errors"].append(
                                f"Tabla {table} devuelve {count} registros sin tenant_id configurado"
                            )
                            results["enforcement_working"] = False
                            
                    except Exception as e:
                        # Si hay error, podría ser buena señal (RLS bloqueando acceso)
                        logger.info(f"Tabla {table} bloqueada sin tenant (buena señal): {e}")
                        results["accessible_without_tenant"][table] = "BLOCKED"
                        
            except Exception as e:
                results["errors"].append(f"Error en validación de enforcement: {str(e)}")
                results["enforcement_working"] = False
        
        return results
    
    async def validate_policy_coverage(self) -> Dict[str, Any]:
        """Validar que todas las operaciones tengan políticas."""
        results = {
            "complete_coverage": True,
            "policy_summary": {},
            "missing_policies": [],
            "errors": []
        }
        
        async with get_db() as db:
            try:
                for table in CRITICAL_TABLES:
                    # Obtener políticas de la tabla
                    policies_result = await db.execute(
                        text("""
                            SELECT polcmd, COUNT(*) as policy_count
                            FROM pg_policy p
                            JOIN pg_class c ON p.polrelid = c.oid
                            JOIN pg_namespace n ON c.relnamespace = n.oid
                            WHERE c.relname = :table_name AND n.nspname = 'public'
                            GROUP BY polcmd
                        """),
                        {"table_name": table}
                    )
                    
                    policies = {row[0]: row[1] for row in policies_result.fetchall()}
                    results["policy_summary"][table] = policies
                    
                    # Verificar operaciones críticas
                    required_ops = ["SELECT", "INSERT", "UPDATE", "DELETE"]
                    missing = [op for op in required_ops if op not in policies]
                    
                    if missing:
                        results["missing_policies"].append({
                            "table": table,
                            "missing_operations": missing
                        })
                        results["complete_coverage"] = False
                        
            except Exception as e:
                results["errors"].append(f"Error validando políticas: {str(e)}")
                results["complete_coverage"] = False
        
        return results
    
    async def run_full_validation(self) -> Dict[str, Any]:
        """Ejecutar validación completa de RLS en producción."""
        logger.info("🔒 Iniciando validación RLS en producción...")
        
        validation_report = {
            "timestamp": datetime.now().isoformat(),
            "environment": settings.ENV,
            "tenant_id_tested": self.tenant_id,
            "overall_status": "UNKNOWN",
            "validations": {}
        }
        
        try:
            # 1. Obtener tenants disponibles
            if not self.tenant_id:
                tenants = await self.get_available_tenants()
                if not tenants:
                    validation_report["overall_status"] = "ERROR"
                    validation_report["error"] = "No se encontraron tenants para validar"
                    return validation_report
                
                tenant_list = tenants[:3]  # Limitar a 3 tenants para eficiencia
            else:
                tenant_list = [self.tenant_id]
            
            logger.info(f"📋 Validando con tenants: {tenant_list}")
            
            # 2. Validar aislamiento por tabla
            logger.info("🔍 Validando aislamiento por tabla...")
            isolation_results = []
            for table in CRITICAL_TABLES:
                result = await self.validate_table_isolation(table, tenant_list)
                isolation_results.append(result)
                
                status = "✅" if result["isolation_working"] else "❌"
                logger.info(f"{status} {table}: {result['tenant_data_counts']}")
            
            validation_report["validations"]["table_isolation"] = isolation_results
            
            # 3. Validar enforcement de RLS
            logger.info("🛡️ Validando enforcement de RLS...")
            enforcement_result = await self.validate_rls_enforcement()
            validation_report["validations"]["rls_enforcement"] = enforcement_result
            
            status = "✅" if enforcement_result["enforcement_working"] else "❌"
            logger.info(f"{status} RLS Enforcement: {enforcement_result['enforcement_working']}")
            
            # 4. Validar cobertura de políticas
            logger.info("📋 Validando cobertura de políticas...")
            coverage_result = await self.validate_policy_coverage()
            validation_report["validations"]["policy_coverage"] = coverage_result
            
            status = "✅" if coverage_result["complete_coverage"] else "❌"
            logger.info(f"{status} Policy Coverage: {coverage_result['complete_coverage']}")
            
            # 5. Determinar estado general
            all_working = (
                all(r["isolation_working"] for r in isolation_results) and
                enforcement_result["enforcement_working"] and
                coverage_result["complete_coverage"]
            )
            
            validation_report["overall_status"] = "SUCCESS" if all_working else "FAILED"
            
        except Exception as e:
            logger.error(f"❌ Error durante validación: {e}")
            validation_report["overall_status"] = "ERROR"
            validation_report["error"] = str(e)
        
        return validation_report
    
    def print_summary(self, report: Dict[str, Any]):
        """Imprimir resumen de la validación."""
        print("\n" + "="*80)
        print("🔒 RESUMEN DE VALIDACIÓN RLS EN PRODUCCIÓN")
        print("="*80)
        
        status_emoji = {
            "SUCCESS": "✅",
            "FAILED": "❌", 
            "ERROR": "🚨",
            "UNKNOWN": "❓"
        }
        
        status = report["overall_status"]
        print(f"{status_emoji.get(status, '❓')} Estado General: {status}")
        print(f"🕐 Timestamp: {report['timestamp']}")
        print(f"🏷️ Ambiente: {report['environment']}")
        
        if "validations" in report:
            validations = report["validations"]
            
            # Resumen de aislamiento
            if "table_isolation" in validations:
                isolation_results = validations["table_isolation"]
                working_tables = sum(1 for r in isolation_results if r["isolation_working"])
                print(f"🗂️ Aislamiento de tablas: {working_tables}/{len(isolation_results)}")
                
                for result in isolation_results:
                    if not result["isolation_working"]:
                        print(f"   ❌ {result['table']}: {result['errors']}")
            
            # Resumen de enforcement
            if "rls_enforcement" in validations:
                enforcement = validations["rls_enforcement"]
                print(f"🛡️ RLS Enforcement: {'✅' if enforcement['enforcement_working'] else '❌'}")
            
            # Resumen de políticas
            if "policy_coverage" in validations:
                coverage = validations["policy_coverage"]
                print(f"📋 Cobertura de políticas: {'✅' if coverage['complete_coverage'] else '❌'}")
        
        if status != "SUCCESS":
            print("\n🚨 ACCIÓN REQUERIDA:")
            print("   1. Revisar logs detallados arriba")
            print("   2. Verificar configuración de RLS")
            print("   3. NO DESPLEGAR EN PRODUCCIÓN hasta resolver problemas")
        else:
            print("\n🎉 RLS VALIDADO EXITOSAMENTE - SEGURO PARA PRODUCCIÓN")


async def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Validar RLS en producción")
    parser.add_argument("--tenant-id", type=int, help="ID específico de tenant para validar")
    parser.add_argument("--verify-isolation", action="store_true", help="Verificar aislamiento entre tenants")
    parser.add_argument("--output-json", help="Guardar reporte en archivo JSON")
    
    args = parser.parse_args()
    
    # Verificar que estemos en un entorno apropiado
    if settings.ENV == "production" and not args.verify_isolation:
        logger.warning("⚠️ Ejecutando en PRODUCCIÓN - Use --verify-isolation para confirmar")
        confirmation = input("¿Continuar con validación en producción? (y/N): ")
        if confirmation.lower() != 'y':
            print("Validación cancelada")
            sys.exit(0)
    
    validator = ProductionRLSValidator(tenant_id=args.tenant_id)
    report = await validator.run_full_validation()
    
    # Mostrar resumen
    validator.print_summary(report)
    
    # Guardar JSON si se solicita
    if args.output_json:
        import json
        with open(args.output_json, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        logger.info(f"📄 Reporte guardado en: {args.output_json}")
    
    # Exit code basado en el resultado
    if report["overall_status"] == "SUCCESS":
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 