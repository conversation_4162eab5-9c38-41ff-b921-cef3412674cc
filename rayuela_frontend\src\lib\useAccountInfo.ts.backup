// src/lib/useAccountInfo.ts
import useSWR from 'swr';
import { getMyAccount, AccountInfo } from '@/lib/api';
import { useAuth } from '@/lib/auth';

/**
 * Hook para obtener y gestionar la información de la cuenta del usuario.
 * Utiliza SWR para cachear los datos y proporcionar revalidación automática.
 * 
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de la cuenta, estado de carga, errores y funciones de utilidad
 */
export function useAccountInfo(options: {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
} = {}) {
  const { token, apiKey } = useAuth();
  
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
  } = useSWR<AccountInfo>(
    token && apiKey ? ['account-info', token, apiKey] : null,
    async ([_, tokenValue, apiKeyValue]: [string, string, string]) => await getMyAccount(tokenValue, apiKeyValue),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? false,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 60000, // 1 minuto por defecto
      errorRetryCount: options.errorRetryCount ?? 3
    }
  );

  // Función para verificar si la cuenta tiene una API Key
  const hasApiKey = () => {
    if (!data) return false;
    return !!(data.api_key_prefix && data.api_key_last_chars);
  };

  // Función para verificar si la API Key es reciente (menos de 24 horas)
  const isApiKeyRecent = () => {
    if (!data || !data.api_key_created_at) return false;
    
    const keyCreatedAt = new Date(data.api_key_created_at);
    const now = new Date();
    const hoursSinceCreation = (now.getTime() - keyCreatedAt.getTime()) / (1000 * 60 * 60);
    
    return hoursSinceCreation < 24;
  };

  // Función para obtener la fecha de creación de la cuenta
  const getCreationDate = () => {
    if (!data) return null;
    return data.created_at ? new Date(data.created_at) : null;
  };

  // Función para verificar si la cuenta está activa
  const isActive = () => {
    if (!data) return false;
    return data.is_active;
  };

  // Función para formatear la API Key para mostrar (prefijo + asteriscos + sufijo)
  const getFormattedApiKey = () => {
    if (!data || !data.api_key_prefix || !data.api_key_last_chars) return null;
    return `${data.api_key_prefix}••••••••${data.api_key_last_chars}`;
  };

  // Función para obtener el plan de suscripción directamente de la información de cuenta
  const getSubscriptionPlan = () => {
    if (!data || !data.subscription) return null;
    return data.subscription.plan;
  };

  // Función para verificar si la suscripción está activa
  const isSubscriptionActive = () => {
    if (!data || !data.subscription) return false;
    return data.subscription.is_active;
  };

  // Función para obtener la fecha de expiración de la suscripción
  const getSubscriptionExpiryDate = () => {
    if (!data || !data.subscription || !data.subscription.expires_at) return null;
    return new Date(data.subscription.expires_at);
  };

  return {
    accountData: data,
    error,
    isLoading,
    isValidating,
    refresh: mutate,
    lastUpdated: null,
    hasApiKey,
    isApiKeyRecent,
    getCreationDate,
    isActive,
    getFormattedApiKey,
    getSubscriptionPlan,
    isSubscriptionActive,
    getSubscriptionExpiryDate
  };
}
