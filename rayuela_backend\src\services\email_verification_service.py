"""
Service for email verification.
"""
from typing import Optional
from datetime import datetime, timezone, timedelta
import secrets
import string
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from fastapi import HTTPException, status

from src.db.models import SystemUser
from src.utils.base_logger import log_info, log_error
from src.core.config import settings


class EmailVerificationService:
    """Service for email verification."""

    def __init__(self, db: AsyncSession, account_id: Optional[int] = None):
        """
        Initialize the email verification service.
        
        Args:
            db: Database session
            account_id: Account ID (optional)
        """
        self.db = db
        self.account_id = account_id
        self.token_length = 64
        self.token_expiry_hours = 24
    
    def _generate_token(self) -> str:
        """
        Generate a random token for email verification.
        
        Returns:
            Random token
        """
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(self.token_length))
    
    async def create_verification_token(self, user_id: int) -> str:
        """
        Create a verification token for a user.

        Args:
            user_id: User ID

        Returns:
            Verification token

        Raises:
            HTTPException: If the user is not found
        """
        try:
            # Use explicit transaction for database write operation
            async with self.db.begin():
                # Get the user
                query = select(SystemUser).where(
                    SystemUser.id == user_id,
                    SystemUser.account_id == self.account_id if self.account_id else True
                )
                result = await self.db.execute(query)
                user = result.scalars().first()

                if not user:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="User not found"
                    )

                # Generate token
                token = self._generate_token()
                expires_at = datetime.now(timezone.utc) + timedelta(hours=self.token_expiry_hours)

                # Update user
                user.verification_token = token
                user.verification_token_expires_at = expires_at

            log_info(f"Verification token created for user {user_id}")
            return token

        except HTTPException:
            raise
        except Exception as e:
            log_error(f"Error creating verification token: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating verification token"
            )
    
    async def verify_email(self, token: str) -> bool:
        """
        Verify a user's email using a token.

        Args:
            token: Verification token

        Returns:
            True if verification was successful, False otherwise

        Raises:
            HTTPException: If the token is invalid or expired
        """
        try:
            # Use explicit transaction for database write operation
            async with self.db.begin():
                # Get the user by token
                query = select(SystemUser).where(
                    SystemUser.verification_token == token,
                    SystemUser.account_id == self.account_id if self.account_id else True
                )
                result = await self.db.execute(query)
                user = result.scalars().first()

                if not user:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Invalid verification token"
                    )

                # Check if token is expired
                if user.verification_token_expires_at < datetime.now(timezone.utc):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Verification token has expired"
                    )

                # Update user
                user.email_verified = True
                user.verification_token = None
                user.verification_token_expires_at = None

            log_info(f"Email verified for user {user.id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            log_error(f"Error verifying email: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error verifying email"
            )
    
    async def send_verification_email(self, user_id: int, email: str) -> bool:
        """
        Send a verification email to a user.
        
        Args:
            user_id: User ID
            email: User's email address
            
        Returns:
            True if email was sent successfully, False otherwise
        """
        try:
            # Create verification token
            token = await self.create_verification_token(user_id)
            
            # Build verification URL
            verification_url = f"{settings.API_BASE_URL}/api/v1/auth/verify-email?token={token}"
            
            # In a real implementation, you would send an email here
            # For now, we'll just log the URL
            log_info(f"Verification URL for user {user_id}: {verification_url}")
            
            # TODO: Implement actual email sending
            # Example:
            # from src.utils.email import send_email
            # await send_email(
            #     to=email,
            #     subject="Verify your email address",
            #     body=f"Please click the following link to verify your email address: {verification_url}"
            # )
            
            return True
            
        except Exception as e:
            log_error(f"Error sending verification email: {str(e)}")
            return False
