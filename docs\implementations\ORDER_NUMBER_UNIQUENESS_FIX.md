# Order Number Uniqueness Fix for Partitioned Tables

## Overview

This document outlines the critical data integrity fix implemented to address **US-SEC-004: Corregir Restricción UNIQUE de Order.order_number** - a critical issue where the UNIQUE constraint on `order_number` was incompatible with table partitioning.

## Problem Addressed

**Critical Data Integrity Issue:** The `orders` table is partitioned by `account_id`, but the UNIQUE constraint on `order_number` was global, violating PostgreSQL's requirement that UNIQUE constraints on partitioned tables must include all partitioning columns.

### Vulnerability Details

1. **Incompatible Constraint**: Global UNIQUE index on `order_number` without `account_id`
2. **Partitioning Violation**: PostgreSQL requires UNIQUE constraints to include partitioning columns
3. **Data Integrity Risk**: Could allow duplicate order numbers within the same account
4. **Database Errors**: Potential constraint enforcement failures on partitioned tables

### Current Issues in Code

**File:** `alembic/versions/add_order_and_order_item_tables.py` (Line 72)
```sql
CREATE UNIQUE INDEX IF NOT EXISTS uq_order_number ON orders (order_number);
```
❌ **Problem**: Global uniqueness without `account_id` - incompatible with partitioning

**File:** `src/db/models/order.py`
```python
# Missing proper UniqueConstraint in __table_args__
```
❌ **Problem**: No ORM-level constraint definition

## Security Fix Implemented

### ✅ **1. Updated ORM Model**

**File:** `src/db/models/order.py`

**Before (Problematic):**
```python
order_number = Column(String(50), nullable=False, index=True)

__table_args__ = (
    PrimaryKeyConstraint("account_id", "id"),
    # Missing proper unique constraint
)
```

**After (Fixed):**
```python
order_number = Column(String(50), nullable=False)

__table_args__ = (
    PrimaryKeyConstraint("account_id", "id"),
    # CRITICAL: Unique constraint for order_number per account (US-SEC-004)
    UniqueConstraint("account_id", "order_number", name="uq_order_account_number"),
    # Other constraints...
)
```

### ✅ **2. Database Migration**

**File:** `alembic/versions/fix_order_number_unique_constraint.py`

The migration includes:

1. **Duplicate Detection**: Identifies existing duplicate order numbers within accounts
2. **Automatic Resolution**: Renames duplicate orders with suffixes
3. **Constraint Migration**: Removes global index, adds composite constraint
4. **Verification**: Ensures the fix works correctly

**Migration Features:**
- ✅ Pre-migration duplicate detection and resolution
- ✅ Safe constraint replacement
- ✅ Comprehensive logging and error handling
- ✅ Rollback capability with warnings

### ✅ **3. Pre-Migration Verification Tool**

**File:** `scripts/check_order_duplicates.py`

Features:
- **Duplicate Detection**: Finds order_number duplicates within accounts
- **Detailed Analysis**: Shows order details and creation dates
- **Automatic Resolution**: Renames duplicates with safe suffixes
- **Dry Run Mode**: Simulate changes before applying
- **Comprehensive Reporting**: Detailed logs of all operations

### ✅ **4. Comprehensive Testing**

**File:** `tests/security/test_order_number_uniqueness.py`

Test coverage includes:
- **Constraint Enforcement**: Verifies database prevents duplicates within accounts
- **Cross-Tenant Isolation**: Ensures different accounts can use same order numbers
- **Edge Cases**: Tests special characters, length limits, case sensitivity
- **Database Integrity**: Verifies constraint exists and works correctly

## Data Integrity Benefits

### 🛡️ **Partitioning Compatibility**
- **Compliant with PostgreSQL**: UNIQUE constraint includes partitioning column
- **Proper Constraint Enforcement**: Database can enforce uniqueness correctly
- **No Constraint Violations**: Eliminates potential database errors

### 🔒 **Tenant Isolation**
- **Per-Account Uniqueness**: Order numbers unique within each tenant
- **Cross-Tenant Flexibility**: Different accounts can use same order numbers
- **Data Integrity**: Prevents duplicate order numbers within accounts

### 📊 **Operational Reliability**
- **Database-Level Enforcement**: Constraints enforced at database level
- **Application Safety**: ORM model reflects database constraints
- **Migration Safety**: Automatic duplicate resolution during migration

## Implementation Details

### **Constraint Structure**

The new constraint ensures:
```sql
-- Composite unique constraint compatible with partitioning
ALTER TABLE orders ADD CONSTRAINT uq_order_account_number 
UNIQUE (account_id, order_number);
```

### **Duplicate Resolution Strategy**

When duplicates are found:
1. **Keep First Order**: Preserve the oldest order (by creation date)
2. **Rename Others**: Add suffixes like `_dup_2`, `_dup_3`, etc.
3. **Ensure Uniqueness**: Check that new names don't conflict
4. **Update Timestamps**: Mark when the change was made

Example:
```
Original: ORD-001, ORD-001, ORD-001
After:    ORD-001, ORD-001_dup_2, ORD-001_dup_3
```

### **Migration Safety**

The migration process:
1. **Scan for Duplicates**: Find all order_number duplicates within accounts
2. **Report Findings**: Show detailed information about duplicates
3. **Resolve Conflicts**: Automatically rename duplicate orders
4. **Drop Old Constraint**: Remove the problematic global unique index
5. **Add New Constraint**: Create the composite unique constraint
6. **Verify Success**: Ensure the constraint works correctly

## Pre-Migration Steps

### **CRITICAL: Check for Duplicates**

**Before applying the migration**, run the duplicate checker:

```bash
# Check for duplicates (read-only)
python -m scripts.check_order_duplicates --check-only

# Simulate resolution (dry run)
python -m scripts.check_order_duplicates --resolve

# Actually resolve duplicates
python -m scripts.check_order_duplicates --resolve --no-dry-run
```

### **Manual SQL Check**

You can also check manually:
```sql
-- Find duplicate order numbers within accounts
SELECT account_id, order_number, COUNT(*) as count,
       array_agg(id) as order_ids
FROM orders 
GROUP BY account_id, order_number 
HAVING COUNT(*) > 1
ORDER BY account_id, order_number;
```

## Migration Execution

### **Step-by-Step Process**

1. **Backup Database**:
   ```bash
   pg_dump your_database > backup_before_order_fix.sql
   ```

2. **Check for Duplicates**:
   ```bash
   python -m scripts.check_order_duplicates --check-only
   ```

3. **Resolve Duplicates** (if any found):
   ```bash
   python -m scripts.check_order_duplicates --resolve --no-dry-run
   ```

4. **Apply Migration**:
   ```bash
   alembic upgrade fix_order_number_unique_constraint
   ```

5. **Verify Success**:
   ```bash
   # Check constraint exists
   psql -c "\d orders" | grep uq_order_account_number
   
   # Run tests
   python -m pytest tests/security/test_order_number_uniqueness.py -v
   ```

## Testing and Verification

### **Database Constraint Testing**

```python
# This should work - different accounts, same order number
order1 = Order(account_id=1, order_number="ORD-001", ...)
order2 = Order(account_id=2, order_number="ORD-001", ...)  # ✅ Allowed

# This should fail - same account, same order number
order3 = Order(account_id=1, order_number="ORD-001", ...)  # ❌ IntegrityError
```

### **Migration Testing**

The migration includes built-in verification:
- Detects pre-existing duplicates
- Resolves conflicts automatically
- Verifies constraint application
- Provides detailed operation logs

## Compliance and Standards

### ✅ **Database Standards Met**

- **PostgreSQL Partitioning**: Compliant with partitioned table constraints
- **Data Integrity**: Proper UNIQUE constraint enforcement
- **ACID Properties**: Maintains database consistency

### ✅ **Best Practices Implemented**

- **Constraint Naming**: Descriptive constraint names
- **Migration Safety**: Automatic conflict resolution
- **Comprehensive Testing**: All edge cases covered
- **Detailed Documentation**: Clear procedures and rationale

## Monitoring and Maintenance

### **Post-Migration Monitoring**

1. **Constraint Violations**: Monitor for any attempts to create duplicate order numbers
2. **Application Errors**: Watch for IntegrityError exceptions in logs
3. **Performance Impact**: Monitor query performance on orders table

### **Code Review Guidelines**

When reviewing order-related code:
- ✅ Verify order number generation maintains uniqueness per account
- ✅ Ensure error handling for constraint violations
- ✅ Check that order lookups include account_id for proper partitioning
- ✅ Validate that bulk operations respect the constraint

### **Future Considerations**

1. **Order Number Generation**: Ensure generators maintain per-account uniqueness
2. **Bulk Operations**: Verify bulk inserts respect the constraint
3. **Data Migration**: Any future data migrations must respect the constraint
4. **Performance Optimization**: Monitor and optimize queries on the composite index

---

**Data Integrity Impact:** This fix resolves a critical database constraint issue that could have led to data integrity problems and database errors. The composite unique constraint ensures proper partitioning compatibility while maintaining order number uniqueness within each tenant.
