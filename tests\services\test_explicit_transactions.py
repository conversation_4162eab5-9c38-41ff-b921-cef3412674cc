"""
Tests for explicit transaction implementation in services.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from sqlalchemy.exc import SQLAlchemyError

from src.services.billing_webhook_service import BillingWebhookService
from src.services.usage_meter_service import UsageMeterService
from src.services.email_verification_service import EmailVerificationService
from src.db.models import Subscription, SystemUser
from src.db.enums import SubscriptionPlan


@pytest.fixture
def mock_db():
    """Mock database session with transaction support."""
    db = AsyncMock()
    
    # Mock the begin() context manager
    transaction_mock = AsyncMock()
    transaction_mock.__aenter__ = AsyncMock(return_value=transaction_mock)
    transaction_mock.__aexit__ = AsyncMock(return_value=None)
    db.begin.return_value = transaction_mock
    
    # Mock other database operations
    db.execute = AsyncMock()
    db.add = MagicMock()
    db.flush = AsyncMock()
    db.refresh = AsyncMock()
    
    return db


@pytest.fixture
def mock_mercadopago_service():
    """Mock MercadoPagoService."""
    service = MagicMock()
    service._get_plan_from_price_id.return_value = SubscriptionPlan.STARTER
    return service


class TestBillingWebhookServiceTransactions:
    """Test explicit transactions in BillingWebhookService."""

    @pytest.mark.asyncio
    async def test_handle_payment_approved_uses_transaction(self, mock_db, mock_mercadopago_service):
        """Test that payment approved handler uses explicit transaction."""
        # Arrange
        service = BillingWebhookService(mock_db)
        service.mercadopago_service = mock_mercadopago_service
        
        payment = {
            "metadata": {
                "account_id": "123",
                "price_id": "price_123"
            },
            "subscription_id": "sub_123"
        }
        
        # Mock repository
        with patch("src.services.billing_webhook_service.SubscriptionRepository") as mock_repo_class:
            mock_repo = mock_repo_class.return_value
            mock_repo.get_by_account.return_value = None
            
            # Act
            await service._handle_mercadopago_payment_approved(payment)
            
            # Assert
            mock_db.begin.assert_called_once()
            mock_db.add.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_subscription_created_uses_transaction(self, mock_db, mock_mercadopago_service):
        """Test that subscription created handler uses explicit transaction."""
        # Arrange
        service = BillingWebhookService(mock_db)
        service.mercadopago_service = mock_mercadopago_service
        
        subscription = {
            "id": "sub_123",
            "metadata": {
                "account_id": "123",
                "price_id": "price_123"
            }
        }
        
        # Mock repository
        with patch("src.services.billing_webhook_service.SubscriptionRepository") as mock_repo_class:
            mock_repo = mock_repo_class.return_value
            mock_repo.get_by_account.return_value = None
            
            # Act
            await service._handle_mercadopago_subscription_created(subscription)
            
            # Assert
            mock_db.begin.assert_called_once()
            mock_db.add.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_subscription_paused_uses_transaction(self, mock_db, mock_mercadopago_service):
        """Test that subscription paused handler uses explicit transaction."""
        # Arrange
        service = BillingWebhookService(mock_db)
        service.mercadopago_service = mock_mercadopago_service
        
        subscription = {
            "metadata": {
                "account_id": "123"
            }
        }
        
        # Mock repository and subscription
        mock_subscription = MagicMock()
        with patch("src.services.billing_webhook_service.SubscriptionRepository") as mock_repo_class:
            mock_repo = mock_repo_class.return_value
            mock_repo.get_by_account.return_value = mock_subscription
            
            # Act
            await service._handle_mercadopago_subscription_paused(subscription)
            
            # Assert
            mock_db.begin.assert_called_once()
            assert mock_subscription.is_active is False

    @pytest.mark.asyncio
    async def test_handle_subscription_cancelled_uses_transaction(self, mock_db, mock_mercadopago_service):
        """Test that subscription cancelled handler uses explicit transaction."""
        # Arrange
        service = BillingWebhookService(mock_db)
        service.mercadopago_service = mock_mercadopago_service
        
        subscription = {
            "metadata": {
                "account_id": "123"
            }
        }
        
        # Mock repository and subscription
        mock_subscription = MagicMock()
        with patch("src.services.billing_webhook_service.SubscriptionRepository") as mock_repo_class:
            mock_repo = mock_repo_class.return_value
            mock_repo.get_by_account.return_value = mock_subscription
            
            # Act
            await service._handle_mercadopago_subscription_cancelled(subscription)
            
            # Assert
            mock_db.begin.assert_called_once()
            assert mock_subscription.is_active is False
            assert mock_subscription.expires_at is not None


class TestUsageMeterServiceTransactions:
    """Test explicit transactions in UsageMeterService."""

    @pytest.mark.asyncio
    async def test_increment_api_call_in_db_uses_transaction(self, mock_db):
        """Test that database increment uses explicit transaction."""
        # Arrange
        service = UsageMeterService(mock_db)
        
        # Mock subscription query result
        mock_subscription = MagicMock()
        mock_subscription.monthly_api_calls_used = 5
        mock_result = MagicMock()
        mock_result.scalars.return_value.first.return_value = mock_subscription
        mock_db.execute.return_value = mock_result
        
        # Act
        result = await service._increment_api_call_in_db("123")
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.execute.assert_called()
        assert result == 6

    @pytest.mark.asyncio
    async def test_update_db_counter_uses_transaction(self, mock_db):
        """Test that database counter update uses explicit transaction."""
        # Arrange
        service = UsageMeterService(mock_db)
        
        # Mock subscription query result
        mock_subscription = MagicMock()
        mock_subscription.monthly_api_calls_used = 5
        mock_result = MagicMock()
        mock_result.scalars.return_value.first.return_value = mock_subscription
        mock_db.execute.return_value = mock_result
        
        # Act
        await service._update_db_counter("123", 10)
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.execute.assert_called()

    @pytest.mark.asyncio
    async def test_reset_monthly_counters_uses_transaction(self, mock_db):
        """Test that monthly counter reset uses explicit transaction."""
        # Arrange
        service = UsageMeterService(mock_db)
        
        # Mock Redis
        mock_redis = AsyncMock()
        mock_redis.keys.return_value = []
        service._redis = mock_redis
        
        # Act
        result = await service.reset_monthly_counters()
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.execute.assert_called()
        assert result["success"] is True


class TestEmailVerificationServiceTransactions:
    """Test explicit transactions in EmailVerificationService."""

    @pytest.mark.asyncio
    async def test_create_verification_token_uses_transaction(self, mock_db):
        """Test that token creation uses explicit transaction."""
        # Arrange
        service = EmailVerificationService(mock_db, account_id=1)
        
        # Mock user query result
        mock_user = MagicMock()
        mock_result = MagicMock()
        mock_result.scalars.return_value.first.return_value = mock_user
        mock_db.execute.return_value = mock_result
        
        # Act
        token = await service.create_verification_token(1)
        
        # Assert
        mock_db.begin.assert_called_once()
        assert token is not None
        assert len(token) == 64
        assert mock_user.verification_token is not None
        assert mock_user.verification_token_expires_at is not None

    @pytest.mark.asyncio
    async def test_verify_email_uses_transaction(self, mock_db):
        """Test that email verification uses explicit transaction."""
        # Arrange
        service = EmailVerificationService(mock_db, account_id=1)
        
        # Mock user query result
        mock_user = MagicMock()
        mock_user.verification_token_expires_at = datetime.now(timezone.utc).replace(year=2025)
        mock_result = MagicMock()
        mock_result.scalars.return_value.first.return_value = mock_user
        mock_db.execute.return_value = mock_result
        
        # Act
        result = await service.verify_email("test_token")
        
        # Assert
        mock_db.begin.assert_called_once()
        assert result is True
        assert mock_user.email_verified is True
        assert mock_user.verification_token is None
        assert mock_user.verification_token_expires_at is None


class TestTransactionAtomicity:
    """Test transaction atomicity and error handling."""

    @pytest.mark.asyncio
    async def test_transaction_rollback_on_error(self, mock_db):
        """Test that transactions are rolled back on errors."""
        # Arrange
        service = BillingWebhookService(mock_db)
        
        # Mock transaction to raise an error
        transaction_mock = AsyncMock()
        transaction_mock.__aenter__ = AsyncMock(side_effect=SQLAlchemyError("Test error"))
        mock_db.begin.return_value = transaction_mock
        
        payment = {
            "metadata": {
                "account_id": "123",
                "price_id": "price_123"
            },
            "subscription_id": "sub_123"
        }
        
        # Act & Assert
        with pytest.raises(SQLAlchemyError):
            await service._handle_mercadopago_payment_approved(payment)
        
        # Verify transaction was attempted
        mock_db.begin.assert_called_once()
