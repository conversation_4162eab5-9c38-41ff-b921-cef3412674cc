"""add_comprehensive_rls_policies

Revision ID: add_comprehensive_rls_policies
Revises: 5410f56c34d5
Create Date: 2025-01-27 10:00:00.000000

"""
from typing import Sequence, Union
import logging

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_comprehensive_rls_policies'
down_revision: Union[str, None] = '5410f56c34d5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Comprehensive list of all tenant-scoped tables that need RLS
TENANT_SCOPED_TABLES = [
    # Core data tables
    "products",
    "end_users", 
    "interactions",
    "searches",
    "recommendations",
    
    # ML and training tables
    "artifact_metadata",
    "model_metrics", 
    "training_jobs",
    "batch_ingestion_jobs",
    "training_metrics",
    
    # System and audit tables
    "system_users",
    "system_user_roles",
    "audit_logs",
    "notifications",
    
    # Billing and usage tables
    "account_usage_metrics",
    "subscriptions",
    "endpoint_metrics",
    
    # API and order tables
    "api_keys",
    "orders",
    "order_items",
    
    # RBAC tables (tenant-scoped)
    "roles",
    "permissions",
    "role_permissions",
]

# Global tables that should NOT have RLS (for reference)
GLOBAL_TABLES = [
    "accounts",  # Global account table
]

def create_database_roles():
    """Create necessary database roles for RLS."""
    logger.info("Creating database roles...")
    
    # Create app_role if it doesn't exist
    op.execute("""
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'app_role') THEN
                CREATE ROLE app_role;
                GRANT USAGE ON SCHEMA public TO app_role;
            END IF;
        END
        $$;
    """)
    
    # Create maintenance_role if it doesn't exist  
    op.execute("""
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'maintenance_role') THEN
                CREATE ROLE maintenance_role;
                GRANT USAGE ON SCHEMA public TO maintenance_role;
            END IF;
        END
        $$;
    """)
    
    logger.info("Database roles created successfully")

def create_tenant_extension():
    """Create the tenant extension functions."""
    logger.info("Creating tenant extension functions...")
    
    # Create app schema if it doesn't exist
    op.execute("CREATE SCHEMA IF NOT EXISTS app;")
    
    # Create tenant_id setting function
    op.execute("""
        CREATE OR REPLACE FUNCTION app.set_tenant_id()
        RETURNS void AS $$
        BEGIN
            -- Verificar si la variable ya está configurada
            IF current_setting('app.tenant_id', true) IS NULL THEN
                -- Establecer un valor por defecto (NULL)
                PERFORM set_config('app.tenant_id', NULL, false);
            END IF;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Grant permissions
    op.execute("GRANT USAGE ON SCHEMA app TO PUBLIC;")
    op.execute("GRANT EXECUTE ON FUNCTION app.set_tenant_id() TO PUBLIC;")
    
    logger.info("Tenant extension functions created successfully")

def enable_rls_on_table(table_name: str):
    """Enable RLS on a specific table."""
    logger.info(f"Enabling RLS on table: {table_name}")
    
    # Check if table exists first
    op.execute(f"""
        DO $$
        BEGIN
            IF EXISTS (SELECT 1 FROM information_schema.tables 
                      WHERE table_schema = 'public' AND table_name = '{table_name}') THEN
                ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;
                ALTER TABLE {table_name} FORCE ROW LEVEL SECURITY;
            ELSE
                RAISE NOTICE 'Table % does not exist, skipping RLS setup', '{table_name}';
            END IF;
        END
        $$;
    """)

def create_rls_policies_for_table(table_name: str):
    """Create comprehensive RLS policies for a table."""
    logger.info(f"Creating RLS policies for table: {table_name}")
    
    # Check if table exists before creating policies
    op.execute(f"""
        DO $$
        BEGIN
            IF EXISTS (SELECT 1 FROM information_schema.tables 
                      WHERE table_schema = 'public' AND table_name = '{table_name}') THEN
                
                -- DROP existing policies if they exist (for idempotency)
                DROP POLICY IF EXISTS {table_name}_select_policy ON {table_name};
                DROP POLICY IF EXISTS {table_name}_insert_policy ON {table_name};
                DROP POLICY IF EXISTS {table_name}_update_policy ON {table_name};
                DROP POLICY IF EXISTS {table_name}_delete_policy ON {table_name};
                
                -- CREATE SELECT Policy
                CREATE POLICY {table_name}_select_policy ON {table_name}
                FOR SELECT
                USING (account_id = current_setting('app.tenant_id')::integer);
                
                -- CREATE INSERT Policy
                CREATE POLICY {table_name}_insert_policy ON {table_name}
                FOR INSERT
                WITH CHECK (account_id = current_setting('app.tenant_id')::integer);
                
                -- CREATE UPDATE Policy
                CREATE POLICY {table_name}_update_policy ON {table_name}
                FOR UPDATE
                USING (account_id = current_setting('app.tenant_id')::integer)
                WITH CHECK (account_id = current_setting('app.tenant_id')::integer);
                
                -- CREATE DELETE Policy
                CREATE POLICY {table_name}_delete_policy ON {table_name}
                FOR DELETE
                USING (account_id = current_setting('app.tenant_id')::integer);
                
            ELSE
                RAISE NOTICE 'Table % does not exist, skipping policy creation', '{table_name}';
            END IF;
        END
        $$;
    """)

def grant_table_permissions(table_name: str):
    """Grant necessary permissions to database roles."""
    logger.info(f"Granting permissions for table: {table_name}")
    
    op.execute(f"""
        DO $$
        BEGIN
            IF EXISTS (SELECT 1 FROM information_schema.tables 
                      WHERE table_schema = 'public' AND table_name = '{table_name}') THEN
                
                -- Grant permissions to app_role
                GRANT SELECT, INSERT, UPDATE, DELETE ON {table_name} TO app_role;
                
                -- Grant permissions to maintenance_role
                GRANT SELECT, INSERT, UPDATE, DELETE ON {table_name} TO maintenance_role;
                
            END IF;
        END
        $$;
    """)


def upgrade() -> None:
    """Upgrade schema with comprehensive RLS policies."""
    logger.info("Starting comprehensive RLS policies migration...")
    
    try:
        # Step 1: Create database roles
        create_database_roles()
        
        # Step 2: Create tenant extension functions
        create_tenant_extension()
        
        # Step 3: Enable RLS and create policies for all tenant-scoped tables
        for table_name in TENANT_SCOPED_TABLES:
            enable_rls_on_table(table_name)
            create_rls_policies_for_table(table_name)
            grant_table_permissions(table_name)
        
        logger.info("✅ Comprehensive RLS policies migration completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Error during RLS migration: {e}")
        raise


def downgrade() -> None:
    """Downgrade schema by removing RLS policies."""
    logger.info("Starting RLS policies rollback...")
    
    try:
        # Remove RLS policies and disable RLS for all tables
        for table_name in TENANT_SCOPED_TABLES:
            logger.info(f"Removing RLS from table: {table_name}")
            
            op.execute(f"""
                DO $$
                BEGIN
                    IF EXISTS (SELECT 1 FROM information_schema.tables 
                              WHERE table_schema = 'public' AND table_name = '{table_name}') THEN
                        
                        -- Drop all policies
                        DROP POLICY IF EXISTS {table_name}_select_policy ON {table_name};
                        DROP POLICY IF EXISTS {table_name}_insert_policy ON {table_name};
                        DROP POLICY IF EXISTS {table_name}_update_policy ON {table_name};
                        DROP POLICY IF EXISTS {table_name}_delete_policy ON {table_name};
                        
                        -- Disable RLS
                        ALTER TABLE {table_name} DISABLE ROW LEVEL SECURITY;
                        
                    END IF;
                END
                $$;
            """)
        
        # Note: We don't drop the roles or tenant functions as they might be used elsewhere
        logger.info("✅ RLS policies rollback completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Error during RLS rollback: {e}")
        raise
