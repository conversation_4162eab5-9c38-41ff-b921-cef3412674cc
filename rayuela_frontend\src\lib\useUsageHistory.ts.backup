// src/lib/useUsageHistory.ts
import useS<PERSON> from 'swr';
import { getUsageHistory, UsageHistoryPoint, ApiError } from '@/lib/api';
import { useAuth } from '@/lib/auth';

interface UsageHistoryState {
  data: UsageHistoryPoint[] | null;
  error: ApiError | null;
  isLoading: boolean;
  isValidating: boolean;
  mutate: () => Promise<UsageHistoryPoint[] | undefined>;
  dataUpdatedAt: number;
}

interface UsageHistoryMetrics {
  getTotalApiCalls: () => number;
  getAverageApiCallsPerDay: () => number;
  getPeakApiCallsDay: () => { date: Date; apiCalls: number } | null;
  getLatestStorageValue: () => number;
  getChartData: () => Array<{
    date: Date;
    apiCalls: number;
    storage: number;
  }>;
  getApiCallsTrend: () => number;
}

/**
 * Hook para obtener y gestionar el historial de uso de la cuenta.
 * Utiliza SWR para cachear los datos y proporcionar revalidación automática.
 * 
 * @param startDate Fecha de inicio para el historial (formato ISO string o Date)
 * @param endDate Fecha de fin para el historial (formato ISO string o Date)
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos históricos, estado de carga, errores y funciones de utilidad
 */
export function useUsageHistory(
  startDate: Date | string | null,
  endDate: Date | string | null,
  options: {
    revalidateOnFocus?: boolean;
    refreshInterval?: number;
    dedupingInterval?: number;
    errorRetryCount?: number;
  } = {}
): UsageHistoryState & UsageHistoryMetrics {
  const { token, apiKey } = useAuth();
  
  // Convertir fechas a formato ISO string si son objetos Date
  const startIso = startDate instanceof Date ? startDate.toISOString() : startDate;
  const endIso = endDate instanceof Date ? endDate.toISOString() : endDate;
  
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt
  } = useSWR<UsageHistoryPoint[], ApiError>(
    token && apiKey && startIso && endIso ? 
      ['usage-history', token, apiKey, startIso, endIso] : null,
    async ([_, tokenValue, apiKeyValue, start, end]) => 
      await getUsageHistory(tokenValue, apiKeyValue, start, end),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? false,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 60000,
      errorRetryCount: options.errorRetryCount ?? 3,
      onError: (err) => {
        if (ApiError.isApiError(err)) {
          console.error('Error fetching usage history:', err.message, err.details);
        } else {
          console.error('Unknown error fetching usage history:', err);
        }
      }
    }
  );

  const getTotalApiCalls = (): number => {
    if (!data) return 0;
    return data.reduce((sum, point) => sum + point.api_calls, 0);
  };

  const getAverageApiCallsPerDay = (): number => {
    if (!data || data.length === 0) return 0;
    return getTotalApiCalls() / data.length;
  };

  const getPeakApiCallsDay = (): { date: Date; apiCalls: number } | null => {
    if (!data || data.length === 0) return null;
    
    const peakDay = data.reduce((max, point) => 
      point.api_calls > max.api_calls ? point : max, data[0]);
      
    return {
      date: new Date(peakDay.date),
      apiCalls: peakDay.api_calls
    };
  };

  const getLatestStorageValue = (): number => {
    if (!data || data.length === 0) return 0;
    
    const sortedData = [...data].sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime());
      
    return sortedData[0].storage;
  };

  const getChartData = () => {
    if (!data) return [];
    
    return data.map(point => ({
      date: new Date(point.date),
      apiCalls: point.api_calls,
      storage: point.storage
    }));
  };

  const getApiCallsTrend = (): number => {
    if (!data || data.length < 2) return 0;
    
    const sortedData = [...data].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime());
    
    const firstValue = sortedData[0].api_calls;
    const lastValue = sortedData[sortedData.length - 1].api_calls;
    
    if (firstValue === 0) return lastValue > 0 ? 100 : 0;
    
    return ((lastValue - firstValue) / firstValue) * 100;
  };

  return {
    data: data ?? null,
    error: error ?? null,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt: dataUpdatedAt ?? 0,
    getTotalApiCalls,
    getAverageApiCallsPerDay,
    getPeakApiCallsDay,
    getLatestStorageValue,
    getChartData,
    getApiCallsTrend
  };
}
