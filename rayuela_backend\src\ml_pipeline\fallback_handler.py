from typing import Dict, Any, List, Optional, Set
import random
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from datetime import datetime, timedelta

from src.db.models import Product, Interaction
from src.utils.base_logger import log_info, log_warning


class FallbackHandler:
    """
    Clase responsable de generar recomendaciones alternativas (fallback)
    cuando no hay suficientes recomendaciones personalizadas.
    """
    
    async def get_fallback_recommendations(
        self,
        db: AsyncSession,
        account_id: int,
        user_id: int,
        n_recommendations: int,
        category: Optional[str] = None,
        user_preferences: Optional[Dict[int, float]] = None,
        is_new_user: bool = False,
        user_segment: Optional[str] = None,
        user_onboarding_preferences: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Obtiene recomendaciones de fallback utilizando múltiples estrategias.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            user_id: ID del usuario
            n_recommendations: Número de recomendaciones a generar
            category: Categoría específica para filtrar recomendaciones
            user_preferences: Preferencias del usuario (interacciones previas)
            is_new_user: Si el usuario es nuevo en el sistema
            user_segment: Segmento del usuario (si está disponible)
            user_onboarding_preferences: Preferencias iniciales del usuario (categorías, marcas, etc.)

        Returns:
            Lista de recomendaciones de fallback
        """
        log_info(f"Generando recomendaciones fallback para user_id={user_id}, account_id={account_id}")
        
        # Lista para almacenar todas las recomendaciones fallback
        all_fallback_recs = []
        
        # Determinar estrategias a utilizar según las condiciones
        if is_new_user:
            # Para nuevos usuarios, usar estrategias no personalizadas
            strategies = [
                self._get_popular_products,
                self._get_trending_products,
                self._get_new_arrivals
            ]

            # Si tenemos preferencias de onboarding, añadir estrategia basada en preferencias
            if user_onboarding_preferences:
                strategies.insert(0, self._get_preference_based_recommendations)

            # Si tenemos información de segmento, añadir estrategia basada en segmento
            if user_segment:
                strategies.insert(-1, self._get_segment_recommendations)

        else:
            # Para usuarios existentes, usar estrategias semi-personalizadas
            strategies = [
                self._get_related_category_recommendations,
                self._get_popular_products,
                self._get_new_arrivals,
                self._get_trending_products
            ]
            
        # Aplicar estrategias hasta tener suficientes recomendaciones
        remaining = n_recommendations
        
        for strategy in strategies:
            if remaining <= 0:
                break
                
            # Obtener recomendaciones con la estrategia actual
            strategy_recs = await strategy(
                db=db,
                account_id=account_id,
                user_id=user_id,
                n_recommendations=remaining,
                category=category,
                user_preferences=user_preferences,
                user_segment=user_segment,
                user_onboarding_preferences=user_onboarding_preferences
            )
            
            # Filtrar para evitar duplicados
            existing_ids = {r["item_id"] for r in all_fallback_recs}
            filtered_recs = [r for r in strategy_recs if r["item_id"] not in existing_ids]
            
            all_fallback_recs.extend(filtered_recs)
            remaining = n_recommendations - len(all_fallback_recs)
            
        # Establecer informacion de fallback y asignar ranks
        for i, rec in enumerate(all_fallback_recs):
            rec.setdefault("model_type", "fallback")
            rec.setdefault("rank", i + 1)
            
        log_info(f"Generadas {len(all_fallback_recs)} recomendaciones fallback")
        
        return all_fallback_recs[:n_recommendations]
        
    async def _get_popular_products(
        self,
        db: AsyncSession,
        account_id: int,
        user_id: int,
        n_recommendations: int,
        category: Optional[str] = None,
        user_preferences: Optional[Dict[int, float]] = None,
        user_segment: Optional[str] = None,
        user_onboarding_preferences: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Obtiene productos populares basados en interacciones."""
        timeframe_days = 30  # Últimos 30 días
        
        # Construir consulta base
        query = select(
            Interaction.item_id,
            func.count(Interaction.id).label("interaction_count")
        ).where(
            Interaction.account_id == account_id
        ).group_by(
            Interaction.item_id
        ).order_by(
            func.count(Interaction.id).desc()
        )
        
        # Añadir filtro por fecha
        date_limit = datetime.now() - timedelta(days=timeframe_days)
        query = query.where(Interaction.timestamp >= date_limit)
        
        # Añadir filtro por categoría si es necesario
        if category:
            product_subquery = select(Product.product_id).where(
                (Product.account_id == account_id) &
                (Product.category == category)
            )
            query = query.where(Interaction.item_id.in_(product_subquery))
            
        # Limitar número de resultados
        query = query.limit(n_recommendations * 2)  # Extra para tener margen
        
        # Ejecutar consulta
        result = await db.execute(query)
        popular_items = result.all()
        
        # Obtener información de productos
        recommendations = []
        
        if popular_items:
            item_ids = [item.item_id for item in popular_items]
            
            products_query = select(Product).where(
                (Product.account_id == account_id) &
                (Product.product_id.in_(item_ids))
            )
            
            products_result = await db.execute(products_query)
            products = products_result.scalars().all()
            
            # Crear diccionario para acceso rápido
            products_dict = {p.product_id: p for p in products}
            
            # Crear recomendaciones
            for i, item in enumerate(popular_items):
                if item.item_id in products_dict:
                    product = products_dict[item.item_id]
                    
                    recommendations.append({
                        "item_id": product.product_id,
                        "name": product.name,
                        "category": product.category,
                        "price": product.price,
                        "description": product.description,
                        "average_rating": product.average_rating or 0.0,
                        "score": 1.0 - (i * 0.01),  # Score decreciente
                        "model_type": "fallback",
                        "fallback_reason": "popular",
                        "rank": i + 1
                    })
                    
        log_info(f"Generadas {len(recommendations)} recomendaciones de productos populares")
        return recommendations[:n_recommendations]
        
    async def _get_trending_products(
        self,
        db: AsyncSession,
        account_id: int,
        user_id: int,
        n_recommendations: int,
        category: Optional[str] = None,
        user_preferences: Optional[Dict[int, float]] = None,
        user_segment: Optional[str] = None,
        user_onboarding_preferences: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Obtiene productos con tendencia creciente en interacciones."""
        recent_days = 7  # Últimos 7 días
        previous_days = 30  # Comparar con los 30 días anteriores
        
        # Fecha límite para interacciones recientes
        recent_date = datetime.now() - timedelta(days=recent_days)
        older_date = recent_date - timedelta(days=previous_days)
        
        # Consulta para interacciones recientes
        recent_query = select(
            Interaction.item_id,
            func.count(Interaction.id).label("recent_count")
        ).where(
            (Interaction.account_id == account_id) &
            (Interaction.timestamp >= recent_date)
        ).group_by(
            Interaction.item_id
        )
        
        # Consulta para interacciones anteriores
        older_query = select(
            Interaction.item_id,
            func.count(Interaction.id).label("older_count")
        ).where(
            (Interaction.account_id == account_id) &
            (Interaction.timestamp >= older_date) &
            (Interaction.timestamp < recent_date)
        ).group_by(
            Interaction.item_id
        )
        
        # Ejecutar consultas
        recent_result = await db.execute(recent_query)
        recent_counts = {item.item_id: item.recent_count for item in recent_result}
        
        older_result = await db.execute(older_query)
        older_counts = {item.item_id: item.older_count for item in older_result}
        
        # Calcular tendencia (crecimiento reciente)
        trending_items = []
        for item_id, recent_count in recent_counts.items():
            older_count = older_counts.get(item_id, 0)
            
            # Evitar división por cero
            if older_count == 0:
                older_count = 1
                
            # Normalizar por período
            recent_rate = recent_count / recent_days
            older_rate = older_count / previous_days
            
            growth_rate = recent_rate / older_rate
            
            # Considerar productos con crecimiento significativo
            if growth_rate > 1.2:  # 20% de crecimiento
                trending_items.append((item_id, growth_rate))
                
        # Ordenar por tasa de crecimiento
        trending_items.sort(key=lambda x: x[1], reverse=True)
        
        # Filtrar por categoría si es necesario
        if category:
            category_products_query = select(Product.product_id).where(
                (Product.account_id == account_id) &
                (Product.category == category)
            )
            category_result = await db.execute(category_products_query)
            category_products = set(item.product_id for item in category_result)
            
            trending_items = [(item_id, growth) for item_id, growth in trending_items 
                               if item_id in category_products]
                               
        # Limitar a los top trending
        trending_items = trending_items[:n_recommendations * 2]
        
        # Obtener información de productos
        recommendations = []
        
        if trending_items:
            item_ids = [item[0] for item in trending_items]
            
            products_query = select(Product).where(
                (Product.account_id == account_id) &
                (Product.product_id.in_(item_ids))
            )
            
            products_result = await db.execute(products_query)
            products = products_result.scalars().all()
            
            # Crear diccionario para acceso rápido
            products_dict = {p.id: p for p in products}
            
            # Crear recomendaciones
            for i, (item_id, growth_rate) in enumerate(trending_items):
                if item_id in products_dict:
                    product = products_dict[item_id]
                    
                    recommendations.append({
                        "item_id": product.id,
                        "name": product.name,
                        "category": product.category,
                        "price": product.price,
                        "description": product.description,
                        "image_url": product.image_url,
                        "average_rating": product.average_rating or 0.0,
                        "score": min(1.0, growth_rate / 5.0),  # Normalizar score
                        "model_type": "fallback",
                        "fallback_reason": "trending",
                        "growth_rate": growth_rate,
                        "rank": i + 1
                    })
                    
        log_info(f"Generadas {len(recommendations)} recomendaciones de productos tendencia")
        return recommendations[:n_recommendations]
        
    async def _get_new_arrivals(
        self,
        db: AsyncSession,
        account_id: int,
        user_id: int,
        n_recommendations: int,
        category: Optional[str] = None,
        user_preferences: Optional[Dict[int, float]] = None,
        user_segment: Optional[str] = None,
        user_onboarding_preferences: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Obtiene productos nuevos recientemente añadidos."""
        # Consulta base para productos recientes
        query = select(Product).where(
            Product.account_id == account_id
        ).order_by(
            Product.created_at.desc()
        )
        
        # Filtrar por categoría si es necesario
        if category:
            query = query.where(Product.category == category)
            
        # Limitar a productos de los últimos 30 días
        date_limit = datetime.now() - timedelta(days=30)
        query = query.where(Product.created_at >= date_limit)
        
        # Limitar número de resultados
        query = query.limit(n_recommendations * 2)
        
        # Ejecutar consulta
        result = await db.execute(query)
        products = result.scalars().all()
        
        # Crear recomendaciones
        recommendations = []
        
        for i, product in enumerate(products):
            recommendations.append({
                "item_id": product.id,
                "name": product.name,
                "category": product.category,
                "price": product.price,
                "description": product.description,
                "image_url": product.image_url,
                "average_rating": product.average_rating or 0.0,
                "score": 0.9 - (i * 0.01),  # Score decreciente
                "model_type": "fallback",
                "fallback_reason": "new_arrival",
                "created_at": product.created_at,
                "rank": i + 1
            })
            
        log_info(f"Generadas {len(recommendations)} recomendaciones de productos nuevos")
        return recommendations[:n_recommendations]
        
    async def _get_segment_recommendations(
        self,
        db: AsyncSession,
        account_id: int,
        user_id: int,
        n_recommendations: int,
        category: Optional[str] = None,
        user_preferences: Optional[Dict[int, float]] = None,
        user_segment: Optional[str] = None,
        user_onboarding_preferences: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Obtiene recomendaciones basadas en el segmento del usuario."""
        if not user_segment:
            return []
            
        log_info(f"Generando recomendaciones por segmento para user_id={user_id}, segmento={user_segment}")
        
        # Consulta para obtener usuarios del mismo segmento
        from src.db.models import EndUser
        
        segment_users_query = select(EndUser.id).where(
            (EndUser.account_id == account_id) &
            (EndUser.segment == user_segment) &
            (EndUser.id != user_id)
        ).limit(100)
        
        segment_users_result = await db.execute(segment_users_query)
        segment_user_ids = [user.id for user in segment_users_result]
        
        if not segment_user_ids:
            return []
            
        # Consulta para obtener interacciones de usuarios del mismo segmento
        segment_interactions_query = select(
            Interaction.item_id,
            func.count(Interaction.id).label("interaction_count")
        ).where(
            (Interaction.account_id == account_id) &
            (Interaction.user_id.in_(segment_user_ids))
        ).group_by(
            Interaction.item_id
        ).order_by(
            func.count(Interaction.id).desc()
        )
        
        # Filtrar por categoría si es necesario
        if category:
            product_subquery = select(Product.id).where(
                (Product.account_id == account_id) & 
                (Product.category == category)
            )
            segment_interactions_query = segment_interactions_query.where(
                Interaction.item_id.in_(product_subquery)
            )
            
        # Limitar número de resultados
        segment_interactions_query = segment_interactions_query.limit(n_recommendations * 2)
        
        # Ejecutar consulta
        segment_result = await db.execute(segment_interactions_query)
        segment_items = segment_result.all()
        
        # Obtener información de productos
        recommendations = []
        
        if segment_items:
            item_ids = [item.item_id for item in segment_items]
            
            products_query = select(Product).where(
                (Product.account_id == account_id) & 
                (Product.id.in_(item_ids))
            )
            
            products_result = await db.execute(products_query)
            products = products_result.scalars().all()
            
            # Crear diccionario para acceso rápido
            products_dict = {p.id: p for p in products}
            
            # Crear recomendaciones
            for i, item in enumerate(segment_items):
                if item.item_id in products_dict:
                    product = products_dict[item.item_id]
                    
                    recommendations.append({
                        "item_id": product.id,
                        "name": product.name,
                        "category": product.category,
                        "price": product.price,
                        "description": product.description,
                        "image_url": product.image_url,
                        "average_rating": product.average_rating or 0.0,
                        "score": 0.95 - (i * 0.01),  # Score decreciente
                        "model_type": "fallback",
                        "fallback_reason": "segment",
                        "user_segment": user_segment,
                        "rank": i + 1
                    })
                    
        log_info(f"Generadas {len(recommendations)} recomendaciones por segmento")
        return recommendations[:n_recommendations]
        
    async def _get_related_category_recommendations(
        self,
        db: AsyncSession,
        account_id: int,
        user_id: int,
        n_recommendations: int,
        category: Optional[str] = None,
        user_preferences: Optional[Dict[int, float]] = None,
        user_segment: Optional[str] = None,
        user_onboarding_preferences: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Obtiene recomendaciones basadas en categorías preferidas del usuario."""
        if not user_preferences:
            return []
            
        # Obtener productos que el usuario ha interactuado
        user_item_ids = list(user_preferences.keys())
        
        if not user_item_ids:
            return []
            
        # Obtener categorías de los productos con los que el usuario ha interactuado
        products_query = select(Product.category, func.count(Product.id).label("category_count")).where(
            (Product.account_id == account_id) &
            (Product.id.in_(user_item_ids))
        ).group_by(
            Product.category
        ).order_by(
            func.count(Product.id).desc()
        )
        
        # Ejecutar consulta
        products_result = await db.execute(products_query)
        category_counts = products_result.all()
        
        if not category_counts:
            return []
            
        # Filtrar por categoría específica si se proporciona
        if category:
            top_categories = [c.category for c in category_counts if c.category == category]
            if not top_categories:
                return []
        else:
            # Tomar las 3 categorías principales
            top_categories = [c.category for c in category_counts[:3] if c.category]
            
        if not top_categories:
            return []
            
        # Obtener productos populares de las categorías principales que el usuario no ha interactuado
        recommendations = []
        
        for cat in top_categories:
            if len(recommendations) >= n_recommendations:
                break
                
            # Consulta para productos populares de esta categoría
            pop_query = select(
                Product,
                func.count(Interaction.id).label("interaction_count")
            ).join(
                Interaction,
                Product.id == Interaction.item_id
            ).where(
                (Product.account_id == account_id) &
                (Product.category == cat) &
                (~Product.id.in_(user_item_ids))
            ).group_by(
                Product.id
            ).order_by(
                func.count(Interaction.id).desc()
            ).limit(n_recommendations)
            
            # Ejecutar consulta
            pop_result = await db.execute(pop_query)
            popular_products = pop_result.all()
            
            # Añadir a recomendaciones
            for i, (product, count) in enumerate(popular_products):
                recommendations.append({
                    "item_id": product.id,
                    "name": product.name,
                    "category": product.category,
                    "price": product.price,
                    "description": product.description,
                    "image_url": product.image_url,
                    "average_rating": product.average_rating or 0.0,
                    "score": 0.9 - (i * 0.01),  # Score decreciente
                    "model_type": "fallback",
                    "fallback_reason": "category_popular",
                    "rank": len(recommendations) + 1
                })
                
        # Si no hay suficientes recomendaciones, completar con productos aleatorios de las categorías
        if len(recommendations) < n_recommendations:
            remaining = n_recommendations - len(recommendations)
            existing_ids = {r["item_id"] for r in recommendations}
            
            # Consulta para productos aleatorios de estas categorías
            random_query = select(Product).where(
                (Product.account_id == account_id) &
                (Product.category.in_(top_categories)) &
                (~Product.id.in_(user_item_ids)) &
                (~Product.id.in_(existing_ids))
            ).limit(remaining * 2)
            
            # Ejecutar consulta
            random_result = await db.execute(random_query)
            random_products = random_result.scalars().all()
            
            # Mezclar para aleatorizar
            random.shuffle(random_products)
            
            # Añadir a recomendaciones
            for i, product in enumerate(random_products[:remaining]):
                recommendations.append({
                    "item_id": product.id,
                    "name": product.name,
                    "category": product.category,
                    "price": product.price,
                    "description": product.description,
                    "image_url": product.image_url,
                    "average_rating": product.average_rating or 0.0,
                    "score": 0.7 - (i * 0.01),  # Score más bajo para productos aleatorios
                    "model_type": "fallback",
                    "fallback_reason": "category_random",
                    "rank": len(recommendations) + 1
                })
                
        log_info(f"Generadas {len(recommendations)} recomendaciones por categoría relacionada")
        return recommendations[:n_recommendations]

    async def _get_preference_based_recommendations(
        self,
        db: AsyncSession,
        account_id: int,
        user_id: int,
        n_recommendations: int,
        category: Optional[str] = None,
        user_preferences: Optional[Dict[int, float]] = None,
        user_segment: Optional[str] = None,
        user_onboarding_preferences: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Obtiene recomendaciones basadas en las preferencias de onboarding del usuario."""
        if not user_onboarding_preferences:
            return []

        log_info(f"Generando recomendaciones basadas en preferencias para user_id={user_id}")

        # Extraer preferencias del usuario
        preferred_categories = user_onboarding_preferences.get("preferred_categories", [])
        disliked_categories = user_onboarding_preferences.get("disliked_categories", [])
        preferred_brands = user_onboarding_preferences.get("preferred_brands", [])
        disliked_brands = user_onboarding_preferences.get("disliked_brands", [])
        price_range_min = user_onboarding_preferences.get("price_range_min")
        price_range_max = user_onboarding_preferences.get("price_range_max")

        # Construir consulta base
        query = select(Product).where(Product.account_id == account_id)

        # Aplicar filtros de preferencias
        if preferred_categories:
            query = query.where(Product.category.in_(preferred_categories))
        elif disliked_categories:
            query = query.where(~Product.category.in_(disliked_categories))

        # Filtrar por rango de precios si está disponible
        if price_range_min is not None:
            query = query.where(Product.price >= price_range_min)
        if price_range_max is not None:
            query = query.where(Product.price <= price_range_max)

        # Filtrar por categoría específica si se proporciona
        if category:
            query = query.where(Product.category == category)

        # Ordenar por popularidad (usando interacciones) y limitar
        query = query.join(
            Interaction, Product.id == Interaction.item_id, isouter=True
        ).group_by(Product.id).order_by(
            func.count(Interaction.id).desc(),
            Product.created_at.desc()
        ).limit(n_recommendations * 2)  # Obtener más candidatos para filtrar

        # Ejecutar consulta
        result = await db.execute(query)
        products = result.scalars().all()

        recommendations = []
        for i, product in enumerate(products[:n_recommendations]):
            # Calcular score basado en coincidencia con preferencias
            score = 0.9

            # Bonus por categoría preferida
            if preferred_categories and product.category in preferred_categories:
                score += 0.1

            # Penalty por categoría no deseada
            if disliked_categories and product.category in disliked_categories:
                score -= 0.2

            # Bonus por rango de precio
            if (price_range_min is None or product.price >= price_range_min) and \
               (price_range_max is None or product.price <= price_range_max):
                score += 0.05

            # Asegurar que el score esté en rango válido
            score = max(0.1, min(1.0, score - (i * 0.01)))

            recommendations.append({
                "item_id": product.id,
                "name": product.name,
                "category": product.category,
                "price": product.price,
                "description": product.description,
                "image_url": product.image_url,
                "average_rating": product.average_rating or 0.0,
                "score": score,
                "model_type": "fallback",
                "fallback_reason": "onboarding_preferences",
                "matched_preferences": {
                    "category_match": product.category in preferred_categories if preferred_categories else False,
                    "price_in_range": (price_range_min is None or product.price >= price_range_min) and
                                     (price_range_max is None or product.price <= price_range_max)
                },
                "rank": i + 1
            })

        log_info(f"Generadas {len(recommendations)} recomendaciones basadas en preferencias")
        return recommendations