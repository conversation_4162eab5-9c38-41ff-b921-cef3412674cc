#!/usr/bin/env python3
"""
Script para corregir todas las referencias de Product.id a Product.product_id
en el código base después de la migración del modelo Product.
"""

import os
import re

def fix_product_id_references():
    """Corrige todas las referencias de Product.id a Product.product_id"""
    
    # Archivos que necesitan corrección
    files_to_fix = [
        "src/ml_pipeline/fallback_handler.py",
        "src/ml_pipeline/training_pipeline.py",
        "src/services/interaction_service.py",
        "src/api/v1/endpoints/products.py",
    ]
    
    # Patrones a reemplazar
    patterns = [
        (r'Product\.id', 'Product.product_id'),
        (r'product\.id', 'product.product_id'),
        (r'item\.id', 'item.product_id'),  # Para casos donde item es un Product
    ]
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"Archivo no encontrado: {file_path}")
            continue
            
        print(f"Procesando: {file_path}")
        
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Aplicar reemplazos
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # Solo escribir si hubo cambios
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✓ Actualizado: {file_path}")
        else:
            print(f"  - Sin cambios: {file_path}")

if __name__ == "__main__":
    fix_product_id_references()
    print("Corrección de referencias completada.")
