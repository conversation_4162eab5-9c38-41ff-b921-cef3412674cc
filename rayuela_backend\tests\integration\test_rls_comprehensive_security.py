"""
Comprehensive RLS Security Integration Tests

This test suite validates that Row-Level Security (RLS) policies are correctly
implemented and enforced across all tenant-scoped tables.

Critical for multi-tenant security - these tests MUST pass before deployment.
"""

import pytest
import asyncio
from typing import List, Dict, Any
from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError

from src.db.session import get_db
from src.db.models import Account
from tests.conftest import TestClient


class TestRLSComprehensiveSecurity:
    """Comprehensive RLS security validation tests."""
    
    # All tables that MUST have RLS enabled
    REQUIRED_RLS_TABLES = [
        "products",
        "end_users", 
        "interactions",
        "searches",
        "recommendations",
        "artifact_metadata",
        "model_metrics", 
        "training_jobs",
        "batch_ingestion_jobs",
        "training_metrics",
        "system_users",
        "system_user_roles",
        "audit_logs",
        "notifications",
        "account_usage_metrics",
        "subscriptions",
        "endpoint_metrics",
        "api_keys",
        "orders",
        "order_items",
        "roles",
        "permissions",
        "role_permissions",
    ]
    
    # Required policy operations
    REQUIRED_OPERATIONS = ["SELECT", "INSERT", "UPDATE", "DELETE"]

    @pytest.mark.asyncio
    async def test_all_tenant_tables_have_rls_enabled(self):
        """CRITICAL: Verify RLS is enabled on all tenant-scoped tables."""
        async for db in get_db():
            missing_rls = []
            
            for table_name in self.REQUIRED_RLS_TABLES:
                # Check if table exists first
                table_exists = await db.execute(
                    text("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = :table_name
                        )
                    """),
                    {"table_name": table_name}
                )
                
                if not table_exists.scalar():
                    continue  # Skip non-existent tables
                
                # Check if RLS is enabled
                result = await db.execute(
                    text("""
                        SELECT c.relrowsecurity, c.relforcerowsecurity
                        FROM pg_catalog.pg_class c
                        JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                        WHERE c.relname = :table_name AND n.nspname = 'public'
                    """),
                    {"table_name": table_name}
                )
                
                row = result.fetchone()
                if not row or not row[0]:  # RLS not enabled
                    missing_rls.append(table_name)
            
            assert not missing_rls, (
                f"CRITICAL SECURITY FAILURE: Tables missing RLS: {missing_rls}. "
                f"This is a blocking security issue that must be fixed immediately."
            )

    @pytest.mark.asyncio
    async def test_all_tenant_tables_have_complete_policies(self):
        """CRITICAL: Verify all tenant tables have complete RLS policies."""
        async for db in get_db():
            tables_missing_policies = []
            
            for table_name in self.REQUIRED_RLS_TABLES:
                # Check if table exists
                table_exists = await db.execute(
                    text("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = :table_name
                        )
                    """),
                    {"table_name": table_name}
                )
                
                if not table_exists.scalar():
                    continue
                
                # Get policies for this table
                policies_result = await db.execute(
                    text("""
                        SELECT p.polcmd
                        FROM pg_policy p
                        JOIN pg_class c ON p.polrelid = c.oid
                        JOIN pg_namespace n ON c.relnamespace = n.oid
                        WHERE c.relname = :table_name AND n.nspname = 'public'
                    """),
                    {"table_name": table_name}
                )
                
                existing_operations = [row[0] for row in policies_result.fetchall()]
                missing_operations = set(self.REQUIRED_OPERATIONS) - set(existing_operations)
                
                if missing_operations:
                    tables_missing_policies.append({
                        "table": table_name,
                        "missing_operations": list(missing_operations)
                    })
            
            assert not tables_missing_policies, (
                f"CRITICAL SECURITY FAILURE: Tables with incomplete policies: {tables_missing_policies}. "
                f"All tables must have SELECT, INSERT, UPDATE, DELETE policies."
            )

    @pytest.mark.asyncio
    async def test_rls_policies_use_correct_tenant_filter(self):
        """CRITICAL: Verify all policies use correct tenant_id filter."""
        async for db in get_db():
            invalid_policies = []
            
            for table_name in self.REQUIRED_RLS_TABLES:
                # Check if table exists
                table_exists = await db.execute(
                    text("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = :table_name
                        )
                    """),
                    {"table_name": table_name}
                )
                
                if not table_exists.scalar():
                    continue
                
                # Check policy expressions
                policies_result = await db.execute(
                    text("""
                        SELECT 
                            p.polname,
                            p.polcmd,
                            pg_catalog.pg_get_expr(p.polqual, p.polrelid) AS using_expr,
                            pg_catalog.pg_get_expr(p.polwithcheck, p.polrelid) AS check_expr
                        FROM pg_policy p
                        JOIN pg_class c ON p.polrelid = c.oid
                        JOIN pg_namespace n ON c.relnamespace = n.oid
                        WHERE c.relname = :table_name AND n.nspname = 'public'
                    """),
                    {"table_name": table_name}
                )
                
                for policy in policies_result.fetchall():
                    policy_name, operation, using_expr, check_expr = policy
                    
                    # Check USING expression
                    if using_expr and "current_setting('app.tenant_id')" not in using_expr:
                        invalid_policies.append({
                            "table": table_name,
                            "policy": policy_name,
                            "issue": "USING clause missing tenant_id filter"
                        })
                    
                    # Check WITH CHECK expression for INSERT/UPDATE
                    if operation in ["INSERT", "UPDATE"] and check_expr:
                        if "current_setting('app.tenant_id')" not in check_expr:
                            invalid_policies.append({
                                "table": table_name,
                                "policy": policy_name,
                                "issue": "WITH CHECK clause missing tenant_id filter"
                            })
            
            assert not invalid_policies, (
                f"CRITICAL SECURITY FAILURE: Policies with incorrect tenant filters: {invalid_policies}. "
                f"All policies must filter by current_setting('app.tenant_id')."
            )

    @pytest.mark.asyncio
    async def test_tenant_isolation_enforcement(self):
        """CRITICAL: Test that RLS actually enforces tenant isolation."""
        async for db in get_db():
            # Test with a table that should exist and have data isolation
            test_table = "products"  # Using products as it's a core table
            
            # Check if table exists
            table_exists = await db.execute(
                text("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_name = :table_name
                    )
                """),
                {"table_name": test_table}
            )
            
            if not table_exists.scalar():
                pytest.skip(f"Table {test_table} does not exist")
            
            # Set tenant context to tenant 1
            await db.execute(text("SET app.tenant_id = '1'"))
            
            # Try to query - should work without error
            try:
                result = await db.execute(text(f"SELECT COUNT(*) FROM {test_table}"))
                count_tenant_1 = result.scalar()
            except Exception as e:
                pytest.fail(f"RLS policy preventing legitimate access to {test_table}: {e}")
            
            # Set tenant context to tenant 2
            await db.execute(text("SET app.tenant_id = '2'"))
            
            # Query again - should work but potentially return different results
            try:
                result = await db.execute(text(f"SELECT COUNT(*) FROM {test_table}"))
                count_tenant_2 = result.scalar()
            except Exception as e:
                pytest.fail(f"RLS policy preventing legitimate access to {test_table}: {e}")
            
            # Reset tenant context
            await db.execute(text("RESET app.tenant_id"))

    @pytest.mark.asyncio
    async def test_database_roles_exist(self):
        """CRITICAL: Verify required database roles exist."""
        async for db in get_db():
            required_roles = ["app_role", "maintenance_role"]
            missing_roles = []
            
            for role_name in required_roles:
                result = await db.execute(
                    text("SELECT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = :role_name)"),
                    {"role_name": role_name}
                )
                
                if not result.scalar():
                    missing_roles.append(role_name)
            
            assert not missing_roles, (
                f"CRITICAL SECURITY FAILURE: Missing database roles: {missing_roles}. "
                f"Required roles must exist for proper RLS operation."
            )

    @pytest.mark.asyncio
    async def test_tenant_extension_functions_exist(self):
        """CRITICAL: Verify tenant extension functions exist."""
        async for db in get_db():
            # Check if app schema exists
            schema_exists = await db.execute(
                text("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.schemata 
                        WHERE schema_name = 'app'
                    )
                """)
            )
            
            assert schema_exists.scalar(), (
                "CRITICAL SECURITY FAILURE: 'app' schema does not exist. "
                "Tenant extension functions are missing."
            )
            
            # Check if set_tenant_id function exists
            function_exists = await db.execute(
                text("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.routines 
                        WHERE routine_schema = 'app' 
                        AND routine_name = 'set_tenant_id'
                    )
                """)
            )
            
            assert function_exists.scalar(), (
                "CRITICAL SECURITY FAILURE: app.set_tenant_id() function does not exist. "
                "Tenant context setting is broken."
            )

    @pytest.mark.asyncio
    async def test_no_rls_on_global_tables(self):
        """Verify global tables do NOT have RLS (they should be accessible across tenants)."""
        async for db in get_db():
            global_tables = ["accounts"]  # Global tables that should NOT have RLS
            
            for table_name in global_tables:
                # Check if table exists
                table_exists = await db.execute(
                    text("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = :table_name
                        )
                    """),
                    {"table_name": table_name}
                )
                
                if not table_exists.scalar():
                    continue
                
                # Check RLS status
                result = await db.execute(
                    text("""
                        SELECT c.relrowsecurity
                        FROM pg_catalog.pg_class c
                        JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                        WHERE c.relname = :table_name AND n.nspname = 'public'
                    """),
                    {"table_name": table_name}
                )
                
                row = result.fetchone()
                if row and row[0]:  # RLS is enabled
                    pytest.fail(
                        f"CONFIGURATION ERROR: Global table '{table_name}' has RLS enabled. "
                        f"Global tables should be accessible across all tenants."
                    )

    def test_rls_verification_in_ci_cd(self):
        """Verify that RLS verification is properly integrated in CI/CD."""
        import os
        
        # Check if CI/CD configuration includes RLS verification
        ci_config_path = os.path.join(os.path.dirname(__file__), "../../cloudbuild-tests.yaml")
        
        if os.path.exists(ci_config_path):
            with open(ci_config_path, 'r') as f:
                ci_config = f.read()
                
            assert "verify_rls_comprehensive" in ci_config, (
                "CRITICAL: RLS verification is not integrated in CI/CD pipeline. "
                "This must be added to prevent deployment of insecure configurations."
            )
        else:
            pytest.skip("CI/CD configuration file not found")


# Additional security validation tests
class TestRLSSecurityValidation:
    """Additional security validation tests for edge cases."""
    
    @pytest.mark.asyncio
    async def test_rls_cannot_be_bypassed_with_null_tenant(self):
        """Test that RLS policies handle NULL tenant_id correctly."""
        async for db in get_db():
            # Set tenant_id to NULL
            await db.execute(text("SET app.tenant_id = NULL"))
            
            # Try to access tenant-scoped data - should return no results or error
            test_table = "products"
            
            table_exists = await db.execute(
                text("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_name = :table_name
                    )
                """),
                {"table_name": test_table}
            )
            
            if table_exists.scalar():
                result = await db.execute(text(f"SELECT COUNT(*) FROM {test_table}"))
                count = result.scalar()
                
                # With NULL tenant_id, should get 0 results (no access)
                assert count == 0, (
                    f"SECURITY VULNERABILITY: NULL tenant_id allows access to {count} records. "
                    f"RLS policies must prevent access when tenant_id is NULL."
                )

    @pytest.mark.asyncio
    async def test_rls_policies_are_permissive_not_restrictive(self):
        """Verify RLS policies are permissive (allow access) not restrictive (deny access)."""
        async for db in get_db():
            for table_name in ["products", "end_users"]:  # Test key tables
                table_exists = await db.execute(
                    text("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = :table_name
                        )
                    """),
                    {"table_name": table_name}
                )
                
                if not table_exists.scalar():
                    continue
                
                # Check policy types
                policies_result = await db.execute(
                    text("""
                        SELECT p.polpermissive
                        FROM pg_policy p
                        JOIN pg_class c ON p.polrelid = c.oid
                        JOIN pg_namespace n ON c.relnamespace = n.oid
                        WHERE c.relname = :table_name AND n.nspname = 'public'
                    """),
                    {"table_name": table_name}
                )
                
                for row in policies_result.fetchall():
                    is_permissive = row[0]
                    assert is_permissive, (
                        f"SECURITY CONFIGURATION ERROR: Table '{table_name}' has restrictive policies. "
                        f"All RLS policies should be permissive (USING/WITH CHECK) not restrictive."
                    )
