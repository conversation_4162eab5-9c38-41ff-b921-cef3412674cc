"use client";

import { PlanCard } from './PlanCard';
import { AccountInfo, AccountUsage, PlanInfo } from '@/lib/api';
import { sortPlansByHierarchy } from '@/lib/billing-utils';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';

interface PlansGridProps {
  plans?: Record<string, PlanInfo> | null;
  currentPlan?: string | null;
  accountData?: AccountInfo | null;
  usageData?: AccountUsage | null;
}

export function PlansGrid({ plans, currentPlan, accountData, usageData }: PlansGridProps) {
  if (!plans || Object.keys(plans).length === 0) {
    return (
      <Alert>
        <InfoIcon className="h-4 w-4" />
        <AlertDescription>
          No se pudieron cargar los planes disponibles. Por favor, intenta recargar la página.
        </AlertDescription>
      </Alert>
    );
  }

  // Sort plans by hierarchy (FREE -> STARTER -> PRO -> ENTERPRISE)
  const sortedPlans = sortPlansByHierarchy(plans);

  return (
    <div className="space-y-6">
      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {sortedPlans.map(([planId, plan]) => (
          <PlanCard
            key={planId}
            planId={planId}
            plan={plan}
            currentPlan={currentPlan}
            isPopular={plan.recommended}
          />
        ))}
      </div>

      {/* Additional Information */}
      <div className="space-y-4">
        {/* Plan Change Information */}
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            <strong>Cambios de plan:</strong> Los cambios de plan se aplican inmediatamente. 
            Las actualizaciones se prorratean y las degradaciones se aplican en el próximo ciclo de facturación.
          </AlertDescription>
        </Alert>

        {/* Enterprise Contact Information */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="text-center space-y-4">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
              ¿Necesitas algo más personalizado?
            </h3>
            <p className="text-blue-700 dark:text-blue-300 max-w-2xl mx-auto">
              Nuestro plan Enterprise incluye límites personalizados, soporte dedicado 24/7, 
              implementación personalizada y SLA garantizado. Perfecto para grandes organizaciones 
              con necesidades específicas.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="text-sm text-blue-600 dark:text-blue-400">
                ✓ Límites personalizados &nbsp;&nbsp;
                ✓ Soporte dedicado &nbsp;&nbsp;
                ✓ SLA garantizado
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-8">
          <div className="space-y-4">
            <h4 className="font-semibold">Preguntas Frecuentes</h4>
            
            <div className="space-y-3">
              <div>
                <h5 className="font-medium text-sm">¿Puedo cambiar de plan en cualquier momento?</h5>
                <p className="text-sm text-muted-foreground">
                  Sí, puedes actualizar o degradar tu plan en cualquier momento desde tu dashboard.
                </p>
              </div>
              
              <div>
                <h5 className="font-medium text-sm">¿Qué sucede si excedo mi límite?</h5>
                <p className="text-sm text-muted-foreground">
                  Te notificaremos cuando te acerques al límite. Las solicitudes adicionales pueden ser limitadas hasta que actualices tu plan.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold">Métodos de Pago</h4>
            
            <div className="space-y-3">
              <div>
                <h5 className="font-medium text-sm">¿Qué métodos de pago aceptan?</h5>
                <p className="text-sm text-muted-foreground">
                  Aceptamos tarjetas de crédito y débito a través de Mercado Pago, incluyendo Visa, Mastercard y American Express.
                </p>
              </div>
              
              <div>
                <h5 className="font-medium text-sm">¿Es seguro el procesamiento de pagos?</h5>
                <p className="text-sm text-muted-foreground">
                  Sí, todos los pagos se procesan de forma segura a través de Mercado Pago con encriptación de nivel bancario.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
