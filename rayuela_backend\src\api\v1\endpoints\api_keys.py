"""
Endpoints para la gestión de API Keys (Multi-API Key Support).
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Path
from sqlalchemy.ext.asyncio import AsyncSession

from src.db import models
from src.db.session import get_db
from src.db.schemas.api_key import (
    ApiKeyResponse,
    NewApiKeyResponse,
    ApiKeyCreate,
    ApiKeyUpdate,
    ApiKeyListResponse
)
from src.core.deps import get_current_account
from src.services.api_key_service import ApiKeyService

router = APIRouter()


@router.get("/", response_model=ApiKeyListResponse)
async def list_api_keys(
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Lista todas las API Keys activas para la cuenta actual.

    Retorna una lista de todas las API Keys activas asociadas a la cuenta,
    incluyendo información como nombre, prefijo, últimos caracteres, fecha de creación
    y último uso.

    **Nota**: No se incluyen las API Keys completas por seguridad.
    """
    try:
        api_key_service = ApiKeyService(db)
        api_keys = await api_key_service.list_api_keys(current_account.account_id)

        return ApiKeyListResponse(
            api_keys=[ApiKeyResponse.model_validate(key) for key in api_keys],
            total=len(api_keys)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al listar las API Keys: {str(e)}",
        )


@router.post("/", response_model=NewApiKeyResponse, status_code=status.HTTP_201_CREATED)
async def create_api_key(
    api_key_data: ApiKeyCreate,
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Crea una nueva API Key para la cuenta actual.

    Este endpoint permite crear múltiples API Keys para una cuenta, lo que mejora
    la seguridad y flexibilidad:
    - Diferentes claves para diferentes entornos (desarrollo, staging, producción)
    - Claves específicas para diferentes miembros del equipo
    - Posibilidad de revocar claves específicas sin afectar otras integraciones

    **IMPORTANTE**:
    - Requiere autenticación JWT (debes estar logueado)
    - La API Key completa solo se devolverá una vez
    - Puedes crear múltiples API Keys activas
    - Cada API Key puede tener un nombre descriptivo

    **Autenticación requerida**: JWT token en el header Authorization: Bearer <token>

    Returns:
    - **id**: ID único de la API Key
    - **api_key**: Tu nueva API Key completa (solo se muestra una vez)
    - **name**: Nombre descriptivo de la API Key
    - **prefix**: Prefijo de la API Key para identificación
    - **created_at**: Fecha y hora de creación
    - **message**: Mensaje informativo sobre el uso seguro
    """
    try:
        api_key_service = ApiKeyService(db)
        api_key, api_key_model = await api_key_service.create_api_key(
            account_id=current_account.account_id,
            name=api_key_data.name
        )

        return NewApiKeyResponse(
            id=api_key_model.id,
            api_key=api_key,
            name=api_key_model.name,
            prefix=api_key_model.api_key_prefix,
            created_at=api_key_model.created_at,
            message="Esta es tu nueva API Key. Guárdala en un lugar seguro, solo se mostrará una vez.",
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al crear la API Key: {str(e)}",
        )


@router.get("/current", response_model=ApiKeyResponse)
async def get_current_api_key(
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Obtiene información sobre la API Key actual.
    No devuelve la API Key completa, solo metadatos como:
    - Prefijo de la API Key
    - Últimos caracteres
    - Fecha de creación
    - Último uso (si está disponible)

    Esta información permite identificar la API Key sin comprometer seguridad.
    """
    try:
        api_key_service = ApiKeyService(db)

        # Get the first active API key for this account
        api_keys = await api_key_service.list_api_keys(current_account.account_id)

        if not api_keys:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No API key found for this account",
            )

        # Return the first active API key
        first_key = api_keys[0]
        return ApiKeyResponse(
            id=first_key.id,
            name=first_key.name,
            prefix=first_key.api_key_prefix,
            last_chars=first_key.api_key_last_chars,
            created_at=first_key.created_at,
            is_active=first_key.is_active,
            last_used=first_key.last_used,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al obtener información de la API Key: {str(e)}",
        )


@router.delete("/", status_code=status.HTTP_204_NO_CONTENT)
async def revoke_api_key(
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Revoca todas las API Keys de la cuenta actual.
    Esta acción es irreversible. Después de revocar, tendrás que generar nuevas API Keys.
    """
    try:
        api_key_service = ApiKeyService(db)

        # Revoke all API keys for this account
        revoked_count = await api_key_service.revoke_all_api_keys(current_account.account_id)

        if revoked_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active API keys found to revoke",
            )

        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al revocar las API Keys: {str(e)}",
        )


@router.put("/{api_key_id}", response_model=ApiKeyResponse)
async def update_api_key(
    api_key_id: int = Path(..., description="ID de la API Key a actualizar"),
    api_key_data: ApiKeyUpdate = ...,
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Actualiza los metadatos de una API Key específica.

    Permite actualizar información como el nombre descriptivo de la API Key.
    No se puede cambiar la clave en sí, solo sus metadatos.

    **IMPORTANTE**:
    - Solo puedes actualizar API Keys que pertenecen a tu cuenta
    - No se puede cambiar la API Key en sí, solo metadatos
    - La API Key debe estar activa para poder actualizarla
    """
    try:
        api_key_service = ApiKeyService(db)
        updated_api_key = await api_key_service.update_api_key(
            api_key_id=api_key_id,
            account_id=current_account.account_id,
            name=api_key_data.name
        )

        if not updated_api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API Key no encontrada o no pertenece a tu cuenta"
            )

        return ApiKeyResponse.model_validate(updated_api_key)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al actualizar la API Key: {str(e)}",
        )


@router.delete("/{api_key_id}", status_code=status.HTTP_204_NO_CONTENT)
async def revoke_specific_api_key(
    api_key_id: int = Path(..., description="ID de la API Key a revocar"),
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Revoca una API Key específica.

    Esta acción es irreversible. La API Key será desactivada y no podrá
    utilizarse para autenticar solicitudes.

    **IMPORTANTE**:
    - Solo puedes revocar API Keys que pertenecen a tu cuenta
    - Esta acción es irreversible
    - La API Key dejará de funcionar inmediatamente
    - Otras API Keys de la cuenta no se verán afectadas
    """
    try:
        api_key_service = ApiKeyService(db)
        success = await api_key_service.revoke_api_key(
            api_key_id=api_key_id,
            account_id=current_account.account_id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API Key no encontrada o no pertenece a tu cuenta"
            )

        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al revocar la API Key: {str(e)}",
        )
