from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List
from src.db import schemas
from src.db.session import get_db
from src.core.deps import (
    get_current_account,
    get_limit_service,
    get_current_active_user,
    require_product_read,
    require_product_create,
    require_product_update,
    require_product_delete,
)
from src.services import LimitService
from src.utils.pagination import get_pagination_params
from src.db.models.product import Product
from src.db.models.system_user import SystemUser
from src.db.repositories import ProductRepository  # Importado desde el nuevo paquete
from src.core.exceptions import (
    ResourceNotFoundError,
    DuplicateEntryError,
    ProductNotFoundException,
    LimitExceededError,
)
from src.utils.base_logger import log_info, log_warning, log_error
from src.utils.security import verify_resource_ownership

router = APIRouter()


@router.post("/", response_model=schemas.Product, status_code=status.HTTP_201_CREATED)
async def create_product(
    product_data: schemas.ProductCreate,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
    _: None = Depends(require_product_create),
):
    try:
        await limit_service.validate_product_limit()
    except LimitExceededError as e:
        raise HTTPException(status_code=429, detail=str(e))

    repository = ProductRepository(db, account_id=account.account_id)

    try:
        # --- Inicio de la unidad de trabajo ---
        async with db.begin():  # <--- GESTOR DE TRANSACCIÓN
            new_product = await repository.create(product_data)
            # Podrías llamar a otros repositorios aquí si fuera necesario,
            # todo dentro de la misma transacción.
            # Ejemplo: await some_other_repo.update_inventory_log(new_product.id)

        # --- Fin de la unidad de trabajo (Commit automático si no hubo error) ---

        # Refrescar el objeto DESPUÉS del commit para obtener valores generados por la DB
        await db.refresh(new_product)
        log_info(
            f"Product {new_product.product_id} created successfully for account {account.account_id}"
        )
        return new_product

    except IntegrityError as e:
        # Error específico: Ej. si intentas crear un producto con un ID/nombre que ya existe
        # y tienes una constraint UNIQUE. db.begin() ya hizo rollback.
        log_warning(
            f"Integrity error creating product for account {account.account_id}: {e.orig}"
        )
        # e.orig contiene el error original de la DB (psycopg/asyncpg)
        # Puedes inspeccionar e.orig para dar detalles más específicos si quieres
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Product conflict. It might already exist or violate a constraint. Details: {e.orig}",
        )
    except HTTPException:
        # Si una dependencia (como limit_validator) lanza HTTPException, relanzarla
        raise
    except Exception as e:
        # Cualquier otro error inesperado. db.begin() ya hizo rollback.
        # El ErrorHandlingMiddleware lo capturará y devolverá un 500.
        log_error(
            f"Unexpected error creating product for account {account.account_id}: {e}",
            exc_info=True,
        )
        raise  # Relanzar para que el middleware lo maneje


@router.get("/", response_model=schemas.PaginatedResponse[schemas.Product])
async def read_products(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
    category: str = None,
    _: None = Depends(require_product_read),
):
    """
    Obtener productos paginados para la cuenta actual
    """
    skip, limit = pagination
    repository = ProductRepository(db, account_id=account.account_id)

    # Optimizar consulta para usar particiones e índices
    query = (
        select(Product)
        .where(Product.account_id == account.account_id)
    )

    if category:
        query = query.where(Product.category == category)

    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    products = result.scalars().all()

    return {"items": products, "total": len(products), "skip": skip, "limit": limit}


@router.get("/{product_id}", response_model=schemas.Product)
async def get_product(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: schemas.AccountResponse = Depends(get_current_account),
    _: None = Depends(require_product_read),
):
    """Get a product by ID."""
    # Usar la función de verificación de propiedad
    product = await verify_resource_ownership(
        db=db,
        model=Product,
        resource_id=product_id,
        account_id=current_account.account_id,
        error_class=ProductNotFoundException
    )
    return product


@router.put("/{product_id}", response_model=schemas.Product)
async def update_product(
    product_id: int,
    product_update: schemas.ProductUpdate,
    db: AsyncSession = Depends(get_db),
    current_account: schemas.AccountResponse = Depends(
        get_current_account
    ),  # Usar el schema correcto
    _: None = Depends(require_product_update),
):
    try:
        # --- Inicio de la unidad de trabajo ---
        async with db.begin():
            # Obtener el producto DENTRO de la transacción asegura que trabajamos
            # con datos consistentes y bloquea la fila si es necesario (depende del isolation level).
            # Usar get() es eficiente para PKs. Asegúrate que la PK sea (account_id, id) o ajusta.
            # Si la PK es solo 'id', necesitas filtrar por account_id explícitamente.
            # Asumamos PK compuesta (account_id, id) como en los modelos:
            product = await db.get(Product, (current_account.account_id, product_id))

            if not product:
                # Si no se encuentra, db.begin() hará rollback (aunque no haya cambios)
                # y la excepción será capturada fuera.
                raise ProductNotFoundException(product_id)  # Lanza 404

            # Aplicar los cambios
            updated = False
            for field, value in product_update.model_dump(exclude_unset=True).items():
                if (
                    hasattr(product, field)
                    and value is not None
                    and getattr(product, field) != value
                ):
                    setattr(product, field, value)
                    updated = True

            if not updated:
                # Opcional: Si no hubo cambios, puedes evitar el commit/refresh
                # simplemente saliendo del bloque. O devolver 304 Not Modified.
                # Por simplicidad, dejamos que el commit ocurra (no hará nada si no hay cambios).
                db.commit()

            # Podrías hacer otras operaciones relacionadas aquí
            # await some_other_repo.log_product_update(product.id, changes=...)

        # --- Fin de la unidad de trabajo (Commit automático si no hubo error) ---

        # Refrescar DESPUÉS del commit si necesitas timestamps actualizados, etc.
        await db.refresh(product)
        log_info(
            f"Product {product.product_id} updated successfully for account {current_account.account_id}"
        )
        return product

    except ProductNotFoundException:
        # Relanzar la excepción específica para que devuelva 404
        raise
    except IntegrityError as e:
        # Ej: si actualizas a un valor que viola una constraint UNIQUE
        log_warning(
            f"Integrity error updating product {product_id} for account {current_account.account_id}: {e.orig}"
        )
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Product update conflict. Details: {e.orig}",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_error(
            f"Unexpected error updating product {product_id} for account {current_account.account_id}: {e}",
            exc_info=True,
        )
        raise  # Dejar que el middleware maneje el 500


@router.patch("/{product_id}/inventory", response_model=schemas.Product)
async def update_inventory(
    product_id: int,
    inventory_update: schemas.InventoryUpdate,
    db: AsyncSession = Depends(get_db),
    current_account: schemas.AccountResponse = Depends(get_current_account),
    _: None = Depends(require_product_update),
):
    """Update product inventory."""
    try:
        async with db.begin():
            # Obtener el producto dentro de la transacción
            product = await db.get(Product, (current_account.account_id, product_id))
            if not product:
                raise ProductNotFoundException(product_id)

            # Actualizar el inventario
            product.inventory_count = inventory_update.inventory_count
            await db.refresh(product)

        log_info(
            f"Inventory updated for product {product_id} to {inventory_update.inventory_count}"
        )
        return product
    except ProductNotFoundException:
        raise
    except Exception as e:
        log_error(f"Error updating inventory: {str(e)}")
        raise
