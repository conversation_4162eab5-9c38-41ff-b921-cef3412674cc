"""
Configuración de pytest para los tests de integración.
"""
import asyncio
import os
import sys
from pathlib import Path
from datetime import datetime

# Agregar el directorio raíz al PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from typing import AsyncGenerator, Generator, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import create_engine

from src.core.config import settings
from src.db.base import Base
from src.db.session import get_db
from main import app  # Importar desde el directorio raíz

# Configurar base de datos de prueba
TEST_DATABASE_URL = os.getenv(
    "TEST_DATABASE_URL",
    "postgresql+asyncpg://postgres:postgres@localhost:5432/rayuela_test"
)

# Sobreescribir configuración para tests
settings.POSTGRES_USER = "postgres"  # Usar el usuario real de la base de datos
settings.POSTGRES_PASSWORD = "postgres"  # Usar la contraseña real de la base de datos
settings.POSTGRES_SERVER = "localhost"
settings.POSTGRES_PORT = 5432
settings.POSTGRES_DB = "rayuela_test"
settings.REDIS_URL = "redis://localhost:6379/1"  # Usar base de datos 1 para tests
settings.ENV = "test"


@pytest.fixture(scope="session")
def event_loop():
    """Crear un event loop para tests asíncronos"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def async_db_engine():
    """Crear engine para tests"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def async_db_session(async_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Crear sesión de base de datos para tests"""
    async_session = sessionmaker(
        async_db_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        yield session


@pytest_asyncio.fixture(scope="function")
async def client(async_db_session: AsyncSession) -> AsyncGenerator[TestClient, None]:
    """Fixture para el cliente de prueba."""
    async def override_get_db():
        try:
            yield async_db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest_asyncio.fixture(scope="function")
async def test_accounts(async_db_session: AsyncSession) -> AsyncGenerator[Dict[int, Any], None]:
    """Fixture para crear cuentas de prueba."""
    from src.db.models import Account

    accounts = {}
    for i in range(2):
        account = Account(
            name=f"Test Account {i}",
            email=f"test{i}@example.com",
            status="active"
        )
        async_db_session.add(account)
        await async_db_session.commit()
        await async_db_session.refresh(account)
        accounts[account.id] = account

    yield accounts

    # Limpiar después de las pruebas
    for account in accounts.values():
        await async_db_session.delete(account)
    await async_db_session.commit()


@pytest_asyncio.fixture(scope="function")
async def test_users(async_db_session: AsyncSession, test_accounts: Dict[int, Any]) -> AsyncGenerator[Dict[int, Any], None]:
    """Fixture para crear usuarios de prueba."""
    from src.db.models import User

    users = {}
    for account in test_accounts.values():
        user = User(
            account_id=account.id,
            email=f"user@{account.email}",
            status="active"
        )
        async_db_session.add(user)
        await async_db_session.commit()
        await async_db_session.refresh(user)
        users[user.id] = user

    yield users

    # Limpiar después de las pruebas
    for user in users.values():
        await async_db_session.delete(user)
    await async_db_session.commit()


@pytest_asyncio.fixture(scope="function")
async def test_products(async_db_session: AsyncSession, test_accounts: Dict[int, Any]) -> AsyncGenerator[Dict[int, Any], None]:
    """Fixture para crear productos de prueba."""
    from src.db.models import Product

    products = {}
    for account in test_accounts.values():
        product = Product(
            account_id=account.id,
            name=f"Test Product {account.id}",
            status="active"
        )
        async_db_session.add(product)
        await async_db_session.commit()
        await async_db_session.refresh(product)
        products[product.id] = product

    yield products

    # Limpiar después de las pruebas
    for product in products.values():
        await async_db_session.delete(product)
    await async_db_session.commit()


@pytest.fixture(scope="function")
def test_account() -> Dict[str, Any]:
    """Fixture para crear una cuenta de prueba"""
    return {
        "id": 1,
        "name": "Test Account",
        "is_active": True,
    }


@pytest.fixture(scope="function")
def test_user() -> Dict[str, Any]:
    """Fixture para crear un usuario de prueba"""
    return {
        "id": 1,
        "account_id": 1,
        "email": "<EMAIL>",
        "status": "active",
    }


@pytest.fixture(scope="function")
def test_product() -> Dict[str, Any]:
    """Fixture para crear un producto de prueba"""
    return {
        "id": 1,
        "account_id": 1,
        "name": "Test Product",
        "status": "active",
    }


@pytest.fixture(scope="function")
def test_interaction() -> Dict[str, Any]:
    """Fixture para crear una interacción de prueba"""
    return {
        "id": 1,
        "account_id": 1,
        "user_id": 1,
        "product_id": 1,
        "type": "view",
        "timestamp": "2024-01-01T00:00:00",
    }


@pytest.fixture(scope="function")
def test_training_job() -> Dict[str, Any]:
    """Fixture para crear un job de entrenamiento de prueba"""
    return {
        "id": 1,
        "account_id": 1,
        "status": "pending",
        "created_at": "2024-01-01T00:00:00",
    }


@pytest.fixture(scope="function")
def mock_redis():
    """Fixture para mockear Redis"""
    class MockRedis:
        def __init__(self):
            self._store = {}

        async def get(self, key):
            return self._store.get(key)

        async def set(self, key, value, expire=None):
            self._store[key] = value

        async def delete(self, key):
            if key in self._store:
                del self._store[key]

        async def close(self):
            self._store.clear()

    return MockRedis()


@pytest.fixture(scope="function")
def mock_recommender():
    """Mock para el sistema de recomendaciones"""
    class MockRecommender:
        def __init__(self):
            self.initialized = False
            self.trained = False

        def is_initialized(self):
            return self.initialized

        def is_trained(self):
            return self.trained

        async def initialize(self):
            self.initialized = True

        async def train_models(self, db, account_id):
            self.trained = True

        async def get_recommendations(self, *args, **kwargs):
            return []

        def get_metrics(self):
            return {}

        def get_parameters(self):
            return {}

    return MockRecommender()


# Agregar fixtures adicionales que faltan en los tests
@pytest_asyncio.fixture(scope="function")
async def db_session(async_db_session: AsyncSession) -> AsyncSession:
    """Alias para async_db_session para compatibilidad con tests existentes."""
    return async_db_session


@pytest.fixture(scope="function")
def override_get_db():
    """Fixture para override de get_db."""
    def _override():
        return None
    return _override


@pytest.fixture(scope="function")
def test_admin_user() -> Dict[str, Any]:
    """Fixture para crear un usuario administrador de prueba."""
    return {
        "id": 1,
        "account_id": 1,
        "email": "<EMAIL>",
        "is_admin": True,
        "status": "active",
    }


@pytest_asyncio.fixture(scope="function")
async def test_end_users(async_db_session: AsyncSession, test_accounts: Dict[int, Any]) -> AsyncGenerator[Dict[int, Any], None]:
    """Fixture para crear end users de prueba."""
    from src.db.models import EndUser

    end_users = {}
    for account in test_accounts.values():
        end_user = EndUser(
            account_id=account.id,
            external_id=f"external_user_{account.id}",
            metadata={"test": True}
        )
        async_db_session.add(end_user)
        await async_db_session.commit()
        await async_db_session.refresh(end_user)
        end_users[end_user.id] = end_user

    yield end_users

    # Limpiar después de las pruebas
    for end_user in end_users.values():
        await async_db_session.delete(end_user)
    await async_db_session.commit()


@pytest_asyncio.fixture(scope="function")
async def test_batch_job(async_db_session: AsyncSession, test_accounts: Dict[int, Any]) -> AsyncGenerator[Any, None]:
    """Fixture para crear un batch job de prueba."""
    from src.db.models import BatchIngestionJob
    from src.db.enums import BatchIngestionJobStatus

    account = list(test_accounts.values())[0]  # Usar la primera cuenta disponible
    batch_job = BatchIngestionJob(
        account_id=account.id,
        job_name="test_batch_job",
        status=BatchIngestionJobStatus.PENDING,
        file_path="/tmp/test_batch.json",
        records_processed=0
    )
    async_db_session.add(batch_job)
    await async_db_session.commit()
    await async_db_session.refresh(batch_job)

    yield batch_job

    # Limpiar después de las pruebas
    await async_db_session.delete(batch_job)
    await async_db_session.commit()


@pytest_asyncio.fixture(scope="function")
async def test_interactions(async_db_session: AsyncSession, test_accounts: Dict[int, Any], test_products: Dict[int, Any], test_end_users: Dict[int, Any]) -> AsyncGenerator[Dict[int, Any], None]:
    """Fixture para crear interactions de prueba."""
    from src.db.models import Interaction

    interactions = {}
    account = list(test_accounts.values())[0]
    products = list(test_products.values())
    end_users = list(test_end_users.values())
    
    for i, (product, end_user) in enumerate(zip(products[:3], end_users[:3])):
        interaction = Interaction(
            account_id=account.id,
            end_user_id=end_user.id,
            product_id=product.id,
            interaction_type="view",
            value=1.0 + i * 0.1,
            created_at=datetime.now()
        )
        async_db_session.add(interaction)
        await async_db_session.commit()
        await async_db_session.refresh(interaction)
        interactions[interaction.id] = interaction

    yield interactions

    # Limpiar después de las pruebas
    for interaction in interactions.values():
        await async_db_session.delete(interaction)
    await async_db_session.commit()

# Import utilities
try:
    from tests.integration.utils.mock_celery import mock_celery, mock_celery_task
    from tests.integration.utils.mock_external_services import mock_gcs, mock_redis, mock_mercadopago
except ImportError:
    pass  # Utilities might not be available in all test contexts
