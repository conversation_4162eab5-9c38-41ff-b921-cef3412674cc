# Row-Level Security (RLS) Implementation Guide

## 🎯 Overview

This document provides a comprehensive guide to the Row-Level Security (RLS) implementation in Rayuela, ensuring complete tenant isolation at the database level.

## 🔒 Security Architecture

### Multi-Layer Security Approach

1. **Application Layer**: TenantMiddleware sets `app.tenant_id`
2. **ORM Layer**: BaseRepository filters by account_id
3. **Database Layer**: RLS policies enforce tenant isolation
4. **Verification Layer**: Automated tests and CI/CD validation

### RLS as the Last Line of Defense

RLS provides **database-level tenant isolation** that works even if:
- Application code has bugs
- ORM queries are malformed
- Direct database access occurs
- Middleware fails to set tenant context

## 📋 Implementation Status

### ✅ Completed Components

- **Migration**: `add_comprehensive_rls_policies.py` - Creates RLS policies for all tenant tables
- **Verification Scripts**: Comprehensive validation and testing tools
- **CI/CD Integration**: Automated RLS verification in build pipeline
- **Test Suite**: Integration tests for security validation
- **Documentation**: Complete implementation guide

### 🔧 Covered Tables

All tenant-scoped tables have RLS policies:

#### Core Data Tables
- `products` - Product catalog data
- `end_users` - End user profiles
- `interactions` - User-product interactions
- `searches` - Search queries
- `recommendations` - Generated recommendations

#### ML & Training Tables
- `artifact_metadata` - Model metadata
- `model_metrics` - Model performance metrics
- `training_jobs` - Training job records
- `batch_ingestion_jobs` - Data ingestion jobs
- `training_metrics` - Training performance data

#### System & Audit Tables
- `system_users` - System user accounts
- `system_user_roles` - User role assignments
- `audit_logs` - Audit trail records
- `notifications` - System notifications

#### Billing & Usage Tables
- `account_usage_metrics` - Usage tracking
- `subscriptions` - Subscription data
- `endpoint_metrics` - API usage metrics

#### API & Commerce Tables
- `api_keys` - API key management
- `orders` - Order records
- `order_items` - Order line items

#### RBAC Tables
- `roles` - Role definitions
- `permissions` - Permission definitions
- `role_permissions` - Role-permission mappings

## 🚀 Deployment Instructions

### 1. Apply RLS Migration

```bash
cd rayuela_backend

# Check current status
python -m scripts.check_rls_status

# Deploy RLS policies
python -m scripts.deploy_rls_policies

# Or manually apply migration
alembic upgrade head
```

### 2. Verify Implementation

```bash
# Comprehensive RLS verification
python -m scripts.maintenance.verify_rls_comprehensive

# Policy-specific verification
python -m scripts.maintenance.verify_rls_policies

# Isolation testing
python -m scripts.testing.test_rls_isolation
```

### 3. Run Security Tests

```bash
# Run comprehensive security tests
pytest tests/integration/test_rls_comprehensive_security.py -v

# Run all multi-tenancy tests
pytest tests/integration/test_multi_tenancy_comprehensive.py -v
```

## 🔍 RLS Policy Structure

Each tenant-scoped table has 4 policies:

### SELECT Policy
```sql
CREATE POLICY {table}_select_policy ON {table}
FOR SELECT
USING (account_id = current_setting('app.tenant_id')::integer);
```

### INSERT Policy
```sql
CREATE POLICY {table}_insert_policy ON {table}
FOR INSERT
WITH CHECK (account_id = current_setting('app.tenant_id')::integer);
```

### UPDATE Policy
```sql
CREATE POLICY {table}_update_policy ON {table}
FOR UPDATE
USING (account_id = current_setting('app.tenant_id')::integer)
WITH CHECK (account_id = current_setting('app.tenant_id')::integer);
```

### DELETE Policy
```sql
CREATE POLICY {table}_delete_policy ON {table}
FOR DELETE
USING (account_id = current_setting('app.tenant_id')::integer);
```

## 🛡️ Security Validation

### Automated Validation in CI/CD

The CI/CD pipeline includes:

1. **RLS Verification Step**: Validates all policies are active
2. **Security Tests**: Comprehensive tenant isolation tests
3. **Policy Validation**: Ensures correct tenant filtering
4. **Blocking Deployment**: Fails build if RLS issues detected

### Manual Validation

```sql
-- Check RLS is enabled
SELECT schemaname, tablename, rowsecurity, relforcerowsecurity 
FROM pg_tables t
JOIN pg_class c ON c.relname = t.tablename
WHERE tablename IN ('products', 'end_users', 'interactions');

-- Check policies exist
SELECT schemaname, tablename, policyname, cmd 
FROM pg_policies 
WHERE tablename IN ('products', 'end_users', 'interactions');

-- Test tenant isolation
SET app.tenant_id = '1';
SELECT COUNT(*) FROM products;  -- Should only see tenant 1 data

SET app.tenant_id = '2';
SELECT COUNT(*) FROM products;  -- Should only see tenant 2 data
```

## 🔧 Troubleshooting

### Common Issues

#### 1. RLS Not Enabled
```bash
# Check status
python -m scripts.maintenance.verify_rls_comprehensive

# Re-apply migration
alembic upgrade head
```

#### 2. Missing Policies
```bash
# Verify policies
python -m scripts.maintenance.verify_rls_policies

# Check specific table
SELECT * FROM pg_policies WHERE tablename = 'your_table';
```

#### 3. Tenant Context Not Set
```python
# In application code, ensure tenant is set
async with db.begin():
    await db.execute(text("SET app.tenant_id = :tenant_id"), {"tenant_id": account_id})
    # Your queries here
```

### Emergency Procedures

#### Disable RLS (Emergency Only)
```sql
-- ONLY in emergency - removes security!
ALTER TABLE table_name DISABLE ROW LEVEL SECURITY;
```

#### Re-enable RLS
```sql
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;
ALTER TABLE table_name FORCE ROW LEVEL SECURITY;
```

## 📊 Monitoring & Maintenance

### Regular Checks

1. **Weekly**: Run RLS verification scripts
2. **Before Deployments**: Full security test suite
3. **After Schema Changes**: Verify new tables have RLS
4. **Quarterly**: Security audit and penetration testing

### Performance Considerations

- RLS policies use indexes on `account_id` columns
- Partitioning by `account_id` improves performance
- Monitor query plans for policy overhead
- Consider policy optimization for high-traffic tables

## 🚨 Security Alerts

### Critical Actions Required

If RLS verification fails:

1. **STOP** - Do not deploy to production
2. **INVESTIGATE** - Check which tables/policies failed
3. **FIX** - Apply missing policies immediately
4. **VERIFY** - Re-run all security tests
5. **DOCUMENT** - Record incident and resolution

### Escalation

RLS failures are **CRITICAL SECURITY ISSUES**:
- Block all deployments
- Notify security team immediately
- Conduct post-incident review
- Update procedures to prevent recurrence

## 📚 Additional Resources

- [PostgreSQL RLS Documentation](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [Multi-Tenant Security Best Practices](../TODO.md#security-best-practices)
- [Tenant Middleware Documentation](../../src/middleware/tenant/README.md)
- [Database Schema Documentation](../SCHEMA.md)
