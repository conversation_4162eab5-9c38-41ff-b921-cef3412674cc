#!/bin/bash

# Verify VPC Security Configuration Script
# Validates that VPC Connector, Cloud SQL, and Redis are properly configured for private access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="${GCP_PROJECT_ID:-$(gcloud config get-value project)}"
REGION="${GCP_REGION:-us-central1}"
VPC_CONNECTOR_NAME="rayuela-vpc-connector"

echo -e "${BLUE}🔍 Verificando Configuración de Seguridad VPC para Rayuela${NC}"
echo "======================================================"
echo "📋 Proyecto: $PROJECT_ID"
echo "🌍 Región: $REGION"
echo ""

# Function to check command success
check_result() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1${NC}"
        return 1
    fi
}

# Function to check if VPC Connector exists
check_vpc_connector() {
    echo -e "${BLUE}1. Verificando VPC Connector${NC}"
    echo "-----------------------------"
    
    if gcloud compute networks vpc-access connectors describe $VPC_CONNECTOR_NAME \
        --region=$REGION --project=$PROJECT_ID &>/dev/null; then
        echo -e "${GREEN}✅ VPC Connector '$VPC_CONNECTOR_NAME' existe${NC}"
        
        # Get connector details
        echo "📋 Detalles del VPC Connector:"
        gcloud compute networks vpc-access connectors describe $VPC_CONNECTOR_NAME \
            --region=$REGION --project=$PROJECT_ID \
            --format="table(name,state,network,ipCidrRange,minInstances,maxInstances)"
        echo ""
        return 0
    else
        echo -e "${RED}❌ VPC Connector '$VPC_CONNECTOR_NAME' no existe${NC}"
        echo -e "${YELLOW}💡 Para crear el VPC Connector, ejecuta:${NC}"
        echo "   ./scripts/setup-vpc-connector.sh"
        echo ""
        return 1
    fi
}

# Function to check Cloud SQL instances
check_cloud_sql() {
    echo -e "${BLUE}2. Verificando Cloud SQL (PostgreSQL)${NC}"
    echo "-----------------------------------"
    
    # Get all SQL instances
    INSTANCES=$(gcloud sql instances list --project=$PROJECT_ID --format="value(name)" 2>/dev/null)
    
    if [ -z "$INSTANCES" ]; then
        echo -e "${YELLOW}⚠️ No se encontraron instancias de Cloud SQL${NC}"
        echo ""
        return 1
    fi
    
    for instance in $INSTANCES; do
        echo "🔍 Verificando instancia: $instance"
        
        # Check if instance has private IP
        PRIVATE_IP=$(gcloud sql instances describe $instance --project=$PROJECT_ID \
            --format="value(ipAddresses[].ipAddress)" \
            --filter="ipAddresses.type=PRIVATE" 2>/dev/null)
        
        # Check if instance has public IP
        PUBLIC_IP=$(gcloud sql instances describe $instance --project=$PROJECT_ID \
            --format="value(ipAddresses[].ipAddress)" \
            --filter="ipAddresses.type=PRIMARY" 2>/dev/null)
        
        # Check authorized networks
        AUTH_NETWORKS=$(gcloud sql instances describe $instance --project=$PROJECT_ID \
            --format="value(settings.ipConfiguration.authorizedNetworks[].value)" 2>/dev/null)
        
        if [ -n "$PRIVATE_IP" ]; then
            echo -e "${GREEN}✅ IP Privada configurada: $PRIVATE_IP${NC}"
        else
            echo -e "${RED}❌ No tiene IP privada configurada${NC}"
        fi
        
        if [ -n "$PUBLIC_IP" ]; then
            echo -e "${YELLOW}⚠️ IP Pública encontrada: $PUBLIC_IP${NC}"
            if [ -n "$AUTH_NETWORKS" ] && [ "$AUTH_NETWORKS" != "0.0.0.0/0" ]; then
                echo -e "${YELLOW}📋 Redes autorizadas: $AUTH_NETWORKS${NC}"
            elif [ "$AUTH_NETWORKS" = "0.0.0.0/0" ]; then
                echo -e "${RED}❌ CRÍTICO: Permite acceso desde cualquier IP (0.0.0.0/0)${NC}"
            fi
        else
            echo -e "${GREEN}✅ No tiene IP pública${NC}"
        fi
        
        echo ""
    done
}

# Function to check Redis instances
check_redis() {
    echo -e "${BLUE}3. Verificando Cloud Memorystore (Redis)${NC}"
    echo "--------------------------------------"
    
    # Get all Redis instances
    REDIS_INSTANCES=$(gcloud redis instances list --region=$REGION --project=$PROJECT_ID \
        --format="value(name)" 2>/dev/null)
    
    if [ -z "$REDIS_INSTANCES" ]; then
        echo -e "${YELLOW}⚠️ No se encontraron instancias de Redis${NC}"
        echo ""
        return 1
    fi
    
    for instance in $REDIS_INSTANCES; do
        echo "🔍 Verificando instancia Redis: $instance"
        
        # Get instance details
        INSTANCE_INFO=$(gcloud redis instances describe $instance \
            --region=$REGION --project=$PROJECT_ID \
            --format="value(host,authorizedNetwork)" 2>/dev/null)
        
        if [ -n "$INSTANCE_INFO" ]; then
            HOST=$(echo "$INSTANCE_INFO" | cut -d$'\t' -f1)
            AUTH_NETWORK=$(echo "$INSTANCE_INFO" | cut -d$'\t' -f2)
            
            echo -e "${GREEN}✅ Redis Host: $HOST${NC}"
            echo -e "${GREEN}✅ Red Autorizada: $AUTH_NETWORK${NC}"
            
            # Check if it's using private IP (10.x.x.x, 172.x.x.x, or 192.168.x.x)
            if [[ $HOST =~ ^10\.|^172\.|^192\.168\. ]]; then
                echo -e "${GREEN}✅ Usando IP privada${NC}"
            else
                echo -e "${YELLOW}⚠️ IP no parece ser privada${NC}"
            fi
        else
            echo -e "${RED}❌ No se pudo obtener información de la instancia${NC}"
        fi
        
        echo ""
    done
}

# Function to check Cloud Run services VPC configuration
check_cloud_run_vpc() {
    echo -e "${BLUE}4. Verificando Configuración VPC en Cloud Run${NC}"
    echo "--------------------------------------------"
    
    SERVICES=("rayuela-backend" "rayuela-frontend" "rayuela-worker-maintenance" "rayuela-beat")
    
    for service in "${SERVICES[@]}"; do
        echo "🔍 Verificando servicio: $service"
        
        if gcloud run services describe $service --region=$REGION --project=$PROJECT_ID &>/dev/null; then
            VPC_CONFIG=$(gcloud run services describe $service \
                --region=$REGION --project=$PROJECT_ID \
                --format="value(spec.template.metadata.annotations['run.googleapis.com/vpc-access-connector'])" 2>/dev/null)
            
            if [ -n "$VPC_CONFIG" ]; then
                echo -e "${GREEN}✅ VPC Connector configurado: $VPC_CONFIG${NC}"
            else
                echo -e "${RED}❌ VPC Connector no configurado${NC}"
            fi
        else
            echo -e "${YELLOW}⚠️ Servicio no encontrado (aún no desplegado)${NC}"
        fi
        echo ""
    done
}

# Function to provide security recommendations
provide_recommendations() {
    echo -e "${BLUE}5. Recomendaciones de Seguridad${NC}"
    echo "------------------------------"
    
    echo -e "${YELLOW}📋 Para completar la configuración segura:${NC}"
    echo ""
    
    echo -e "${YELLOW}🔧 Si el VPC Connector no existe:${NC}"
    echo "   ./scripts/setup-vpc-connector.sh"
    echo ""
    
    echo -e "${YELLOW}🔒 Para configurar Cloud SQL solo con IP privada:${NC}"
    echo "   ./scripts/configure-sql-private.sh"
    echo ""
    
    echo -e "${YELLOW}🔒 Para configurar Redis solo con red privada:${NC}"
    echo "   ./scripts/configure-redis-private.sh"
    echo ""
    
    echo -e "${YELLOW}🚀 Para desplegar con la nueva configuración:${NC}"
    echo "   gcloud builds submit --config cloudbuild-deploy-production.yaml"
    echo ""
    
    echo -e "${GREEN}💡 Después del despliegue, ejecuta este script nuevamente para verificar${NC}"
}

# Main execution
echo "🚀 Iniciando verificaciones..."
echo ""

VPC_OK=0
SQL_OK=0
REDIS_OK=0
CLOUDRUN_OK=0

check_vpc_connector && VPC_OK=1 || true
check_cloud_sql && SQL_OK=1 || true
check_redis && REDIS_OK=1 || true
check_cloud_run_vpc && CLOUDRUN_OK=1 || true

echo ""
echo "======================================================"
echo -e "${BLUE}📊 Resumen de Verificación${NC}"
echo "======================================================"

if [ $VPC_OK -eq 1 ]; then
    echo -e "${GREEN}✅ VPC Connector${NC}"
else
    echo -e "${RED}❌ VPC Connector${NC}"
fi

if [ $SQL_OK -eq 1 ]; then
    echo -e "${GREEN}✅ Cloud SQL${NC}"
else
    echo -e "${RED}❌ Cloud SQL${NC}"
fi

if [ $REDIS_OK -eq 1 ]; then
    echo -e "${GREEN}✅ Redis${NC}"
else
    echo -e "${RED}❌ Redis${NC}"
fi

if [ $CLOUDRUN_OK -eq 1 ]; then
    echo -e "${GREEN}✅ Cloud Run VPC${NC}"
else
    echo -e "${RED}❌ Cloud Run VPC${NC}"
fi

echo ""
provide_recommendations

if [ $VPC_OK -eq 1 ] && [ $SQL_OK -eq 1 ] && [ $REDIS_OK -eq 1 ] && [ $CLOUDRUN_OK -eq 1 ]; then
    echo -e "${GREEN}🎉 Configuración de seguridad VPC completa!${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️ Se requieren configuraciones adicionales${NC}"
    exit 1
fi 