#!/usr/bin/env python
"""
Script para ejecutar tests unitarios directamente sin usar pytest.
Este script permite ejecutar tests unitarios específicos sin cargar toda la aplicación.
"""

import os
import sys
import argparse
import importlib.util
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.utils.base_logger import logger


def run_direct_test(test_path, verbose=False):
    """
    Ejecutar un test unitario directamente.
    
    Args:
        test_path: Ruta al archivo de test
        verbose: Mostrar salida detallada
    
    Returns:
        Código de salida (0 si todos los tests pasan, 1 si hay errores)
    """
    # Verificar que el archivo existe
    test_file = Path(test_path)
    if not test_file.exists():
        logger.error(f"El archivo {test_path} no existe.")
        return 1
    
    try:
        # Cargar el módulo de test
        spec = importlib.util.spec_from_file_location("test_module", test_file)
        test_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(test_module)
        
        # Buscar funciones de test
        test_functions = [
            name for name in dir(test_module) 
            if name.startswith("test_") and callable(getattr(test_module, name))
        ]
        
        if not test_functions:
            logger.error(f"No se encontraron funciones de test en {test_path}")
            return 1
        
        logger.info(f"Ejecutando {len(test_functions)} tests de {test_path}:")
        
        # Ejecutar cada función de test
        failures = 0
        for test_name in test_functions:
            test_func = getattr(test_module, test_name)
            logger.info(f"  - Ejecutando {test_name}...")
            try:
                test_func()
                logger.info(f"    ✅ PASÓ")
            except Exception as e:
                logger.error(f"    ❌ FALLÓ: {e}")
                failures += 1
        
        # Mostrar resumen
        if failures == 0:
            logger.info(f"\nTodos los {len(test_functions)} tests pasaron correctamente.")
            return 0
        else:
            logger.error(f"\n{failures} de {len(test_functions)} tests fallaron.")
            return 1
    
    except Exception as e:
        logger.error(f"Error al ejecutar el test: {e}")
        return 1


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Ejecutar tests unitarios directamente")
    parser.add_argument("test_path", type=str, help="Ruta al archivo de test")
    parser.add_argument("--verbose", "-v", action="store_true", help="Mostrar salida detallada")
    
    args = parser.parse_args()
    
    return run_direct_test(args.test_path, args.verbose)


if __name__ == "__main__":
    sys.exit(main())
