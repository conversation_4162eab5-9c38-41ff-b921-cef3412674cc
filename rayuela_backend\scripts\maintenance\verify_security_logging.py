#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to verify security logging configuration.
This script validates that sensitive data exposure vulnerabilities have been addressed.
"""

import os
import sys
import importlib.util
from pathlib import Path


def print_status(message, status="INFO"):
    """Print a status message with color coding."""
    colors = {
        "SUCCESS": "\033[92m",  # Green
        "ERROR": "\033[91m",    # Red
        "WARNING": "\033[93m",  # Yellow
        "INFO": "\033[94m",     # Blue
    }
    reset = "\033[0m"
    print(f"{colors.get(status, '')}{message}{reset}")


def load_gunicorn_config():
    """Load the Gunicorn configuration module."""
    config_path = Path(__file__).parent.parent / "gunicorn_conf.py"
    spec = importlib.util.spec_from_file_location("gunicorn_conf", config_path)
    gunicorn_conf = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(gunicorn_conf)
    return gunicorn_conf


def verify_gunicorn_logging():
    """Verify Gunicorn logging configuration."""
    print_status("🔍 Verifying Gunicorn logging configuration...", "INFO")
    
    success = True
    
    # Test production environment
    os.environ["ENV"] = "production"
    gunicorn_conf = load_gunicorn_config()
    
    # Check production access log format
    prod_format = gunicorn_conf.access_log_format
    if "%(q)s" not in prod_format and "%(r)s" not in prod_format:
        print_status("✓ Production access log format excludes sensitive data", "SUCCESS")
    else:
        print_status("✗ Production access log format may expose sensitive data", "ERROR")
        success = False
    
    # Check that essential components are present
    essential = ["%(h)s", "%(t)s", "%(m)s", "%(U)s", "%(s)s"]
    if all(comp in prod_format for comp in essential):
        print_status("✓ Production access log format includes essential components", "SUCCESS")
    else:
        print_status("✗ Production access log format missing essential components", "ERROR")
        success = False
    
    # Test development environment
    os.environ["ENV"] = "development"
    gunicorn_conf = load_gunicorn_config()  # Reload the module

    dev_format = gunicorn_conf.access_log_format
    if "%(r)s" in dev_format and "%(f)s" in dev_format:
        print_status("✓ Development access log format includes debugging info", "SUCCESS")
    else:
        print_status("✗ Development access log format missing debugging info", "ERROR")
        success = False
    
    return success


def verify_error_handling():
    """Verify error handling middleware configuration."""
    print_status("🔍 Verifying error handling middleware...", "INFO")
    
    success = True
    
    # Check if error handling middleware imports settings
    middleware_path = Path(__file__).parent.parent / "src" / "middleware" / "error_handling.py"
    
    try:
        with open(middleware_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "from src.core.config import settings" in content:
            print_status("✓ Error handling middleware imports settings", "SUCCESS")
        else:
            print_status("✗ Error handling middleware missing settings import", "ERROR")
            success = False
        
        if 'settings.ENV == "production"' in content:
            print_status("✓ Error handling middleware has environment-based logic", "SUCCESS")
        else:
            print_status("✗ Error handling middleware missing environment checks", "ERROR")
            success = False
        
        if "Internal server error" in content:
            print_status("✓ Error handling middleware has generic error messages", "SUCCESS")
        else:
            print_status("✗ Error handling middleware missing generic error messages", "ERROR")
            success = False
            
    except FileNotFoundError:
        print_status("✗ Error handling middleware file not found", "ERROR")
        success = False
    
    return success


def verify_api_key_usage():
    """Verify API key usage patterns in documentation."""
    print_status("🔍 Verifying API key usage patterns...", "INFO")
    
    success = True
    
    # Check PHP documentation
    php_doc_path = Path(__file__).parent.parent / "docs" / "quickstart" / "php" / "README.md"
    
    try:
        with open(php_doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that API keys are used in headers, not URLs
        if "'X-API-Key'" in content or '"X-API-Key"' in content:
            print_status("✓ API keys are used in headers (X-API-Key)", "SUCCESS")
        else:
            print_status("✗ API key header usage not found in documentation", "ERROR")
            success = False
        
        # Check that no API keys are in URL examples
        if "api_key=" not in content and "?key=" not in content:
            print_status("✓ No API keys found in URL parameters", "SUCCESS")
        else:
            print_status("⚠ Potential API keys in URL parameters found", "WARNING")
            # This is a warning, not an error, as it might be in comments
    
    except FileNotFoundError:
        print_status("⚠ PHP documentation not found, skipping API key verification", "WARNING")
    
    return success


def verify_test_coverage():
    """Verify that tests exist for the security features."""
    print_status("🔍 Verifying test coverage...", "INFO")
    
    success = True
    
    # Check for error handling middleware tests
    middleware_test_path = Path(__file__).parent.parent / "tests" / "middleware" / "test_error_handling_middleware.py"
    if middleware_test_path.exists():
        print_status("✓ Error handling middleware tests exist", "SUCCESS")
    else:
        print_status("✗ Error handling middleware tests missing", "ERROR")
        success = False
    
    # Check for Gunicorn config tests
    gunicorn_test_path = Path(__file__).parent.parent / "tests" / "test_gunicorn_config.py"
    if gunicorn_test_path.exists():
        print_status("✓ Gunicorn configuration tests exist", "SUCCESS")
    else:
        print_status("✗ Gunicorn configuration tests missing", "ERROR")
        success = False
    
    return success


def main():
    """Main verification function."""
    print_status("🛡️ Security Logging Verification", "INFO")
    print_status("=" * 50, "INFO")
    
    all_success = True
    
    # Run all verifications
    all_success &= verify_gunicorn_logging()
    print()
    all_success &= verify_error_handling()
    print()
    all_success &= verify_api_key_usage()
    print()
    all_success &= verify_test_coverage()
    
    print()
    print_status("=" * 50, "INFO")
    
    if all_success:
        print_status("🎉 All security logging verifications passed!", "SUCCESS")
        print_status("✅ Sensitive data exposure vulnerability has been addressed", "SUCCESS")
        return 0
    else:
        print_status("❌ Some verifications failed", "ERROR")
        print_status("🔧 Please review and fix the issues above", "ERROR")
        return 1


if __name__ == "__main__":
    sys.exit(main())
