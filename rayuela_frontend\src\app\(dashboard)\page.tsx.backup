// Ruta: src/app/(dashboard)/page.tsx
"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth, usePlans, useAccountInfo, useUsageSummary } from '@/lib/hooks';
import { getApiHealth, AccountInfo, AccountUsage, getMyAccount } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { KeyIcon, BookOpenIcon, XIcon, BarChart3Icon, CreditCardIcon, ExternalLinkIcon, DatabaseIcon } from 'lucide-react';
import Link from 'next/link';
import { format, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';
import { ApiStatus } from '@/components/dashboard/ApiStatus';
import { QuickActions } from '@/components/dashboard/QuickActions';
// PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist
import GettingStartedChecklist from '@/components/dashboard/GettingStartedChecklist';

// Función para formatear bytes a una unidad legible
function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Función para formatear números grandes
function formatNumber(num: number): string {
  return new Intl.NumberFormat().format(num);
}

export default function DashboardPage() {
  const { token, apiKey } = useAuth();
  const [isNewUser, setIsNewUser] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [dismissedOnboarding, setDismissedOnboarding] = useState(false);
  // PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist

  // Usar el hook para obtener los planes
  const { plans, isLoading: plansLoading, error: plansError, getPlanById, getPlanName } = usePlans();

  useEffect(() => {
    // Verificar si es un nuevo usuario basado en la fecha de creación de la API Key
    const checkIfNewUser = async () => {
      if (!token || !apiKey) {
        setIsLoading(false);
        return;
      }

      try {
        const accountData = await getMyAccount(token, apiKey);

        // Verificar si la API Key es reciente (menos de 24 horas) o no existe
        if (!accountData.api_key_created_at) {
          setIsNewUser(true);
        } else {
          const keyCreatedAt = new Date(accountData.api_key_created_at);
          const now = new Date();
          const hoursSinceCreation = (now.getTime() - keyCreatedAt.getTime()) / (1000 * 60 * 60);

          setIsNewUser(hoursSinceCreation < 24);

          // PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist
        }
      } catch (error) {
        console.error("Error al obtener datos de la cuenta:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkIfNewUser();

    // Verificar si el usuario ya ha descartado el onboarding
    const hasUserDismissedOnboarding = localStorage.getItem('dismissedOnboarding');
    if (hasUserDismissedOnboarding === 'true') {
      setDismissedOnboarding(true);
    }
  }, [token, apiKey]);

  const handleDismissOnboarding = () => {
    setDismissedOnboarding(true);
    localStorage.setItem('dismissedOnboarding', 'true');
  };

  // PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist

  // Obtener datos de la cuenta usando el hook personalizado
  const {
    accountData,
    error: accountError,
    isLoading: isAccountLoading
  } = useAccountInfo({
    revalidateOnFocus: false
  });

  // Obtener datos de uso usando el hook personalizado
  const {
    usageData,
    error: usageError,
    isLoading: isUsageLoading
  } = useUsageSummary({
    revalidateOnFocus: false
  });

  // Estado de carga combinado
  const isDataLoading = isAccountLoading || isUsageLoading;
    return (
      <div>
        {/* PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist */}

        {/* Checklist de Primeros Pasos */}
        <GettingStartedChecklist />

        {/* Banner de Onboarding para nuevos usuarios */}
        {isNewUser && !dismissedOnboarding && !isLoading && (
          <Alert className="mb-6 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800">
            <div className="flex justify-between items-start">
              <div>
                <AlertTitle className="text-blue-800 dark:text-blue-300 flex items-center">
                  ¡Bienvenido a Rayuela!
                </AlertTitle>
                <AlertDescription className="mt-2 text-blue-700 dark:text-blue-400">
                  Para comenzar a utilizar nuestra API, te recomendamos seguir estos pasos:
                  <ol className="list-none mt-3 space-y-3">
                    <li className="flex items-center">
                      <span className="flex items-center justify-center bg-blue-600 text-white rounded-full w-6 h-6 mr-2 font-bold text-sm">1</span>
                      <Link href="/api-keys" className="text-blue-700 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 font-medium underline underline-offset-2">
                        Gestiona tus API Keys
                      </Link>
                      <span className="ml-2">para generar o ver tus claves de acceso</span>
                    </li>
                    <li className="flex items-center">
                      <span className="flex items-center justify-center bg-blue-600 text-white rounded-full w-6 h-6 mr-2 font-bold text-sm">2</span>
                      <a
                        href="https://docs.rayuela.ai/quickstart"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-700 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 font-medium underline underline-offset-2"
                      >
                        Consulta nuestra guía de inicio rápido
                      </a>
                      <span className="ml-2">para aprender a integrar la API</span>
                    </li>
                    <li className="flex items-center">
                      <span className="flex items-center justify-center bg-blue-600 text-white rounded-full w-6 h-6 mr-2 font-bold text-sm">3</span>
                      <Link href="/usage" className="text-blue-700 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 font-medium underline underline-offset-2">
                        Monitorea tu uso
                      </Link>
                      <span className="ml-2">para ver estadísticas y límites de tu cuenta</span>
                    </li>
                  </ol>
                </AlertDescription>
                <div className="mt-5 flex flex-wrap gap-3">
                  <Button asChild size="sm" className="bg-blue-600 hover:bg-blue-700">
                    <Link href="/api-keys" className="flex items-center">
                      <KeyIcon className="mr-2 h-4 w-4" />
                      Gestionar API Keys
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="sm" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                    <a
                      href="https://docs.rayuela.ai/quickstart"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center"
                    >
                      <BookOpenIcon className="mr-2 h-4 w-4" />
                      Ver Guía de Inicio
                    </a>
                  </Button>
                  <Button asChild variant="outline" size="sm" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                    <Link href="/usage" className="flex items-center">
                      <BarChart3Icon className="mr-2 h-4 w-4" />
                      Ver Uso
                    </Link>
                  </Button>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="text-blue-700 hover:bg-blue-100 hover:text-blue-800"
                onClick={handleDismissOnboarding}
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
          </Alert>
        )}

        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
              Dashboard Overview
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Welcome to your dashboard. Here you'll find an overview
              of your API usage, billing information, and manage your API keys.
            </p>
          </div>
          <ApiStatus className="hidden md:flex" />
        </div>

        {/* Widgets del Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-6">
          {/* Widget de Uso Rápido - Versión simplificada */}
          <Card className="col-span-1 md:col-span-2 lg:col-span-2 border-2 border-blue-50 dark:border-blue-900/30 shadow-sm hover:shadow-md transition-all duration-300">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <BarChart3Icon className="h-5 w-5 mr-2 text-blue-500" />
                Resumen de Uso
              </CardTitle>
              <CardDescription>
                Vista rápida de tu consumo actual
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isDataLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ) : usageError ? (
                <p className="text-red-500 text-sm">Error al cargar datos de uso</p>
              ) : usageData && accountData ? (
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">Llamadas API</div>
                    <div className="text-xl font-bold mt-1 text-blue-800 dark:text-blue-200">
                      {formatNumber(usageData.api_calls.used)}
                    </div>
                    <div className="text-xs text-blue-600/70 dark:text-blue-400/70 mt-1">
                      {Math.round((usageData.api_calls.used / (plans && plans[usageData.subscription.plan]?.limits.api_calls || 1)) * 100)}% del límite
                    </div>
                  </div>
                  <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-sm text-green-700 dark:text-green-300 font-medium">Almacenamiento</div>
                    <div className="text-xl font-bold mt-1 text-green-800 dark:text-green-200">
                      {formatBytes(usageData.storage.used_bytes)}
                    </div>
                    <div className="text-xs text-green-600/70 dark:text-green-400/70 mt-1">
                      {Math.round((usageData.storage.used_bytes / (plans && plans[usageData.subscription.plan]?.limits.storage_bytes || 1)) * 100)}% del límite
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No hay datos de uso disponibles</p>
              )}
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link href="/usage" className="flex items-center justify-center">
                  <BarChart3Icon className="mr-2 h-4 w-4" />
                  Ver Panel de Uso Completo
                </Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Widget de API Keys - También prominente */}
          <Card className="col-span-1 md:col-span-1 lg:col-span-1 border-2 border-amber-50 dark:border-amber-900/30 shadow-sm hover:shadow-md transition-all duration-300">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <KeyIcon className="h-5 w-5 mr-2 text-amber-500" />
                API Key
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isDataLoading ? (
                <Skeleton className="h-8 w-full" />
              ) : accountError ? (
                <p className="text-red-500 text-sm">Error al cargar datos</p>
              ) : accountData ? (
                <div>
                  {accountData.api_key_prefix && accountData.api_key_last_chars ? (
                    <div className="font-mono text-sm p-2 bg-gray-100 dark:bg-gray-700 rounded border break-all">
                      {accountData.api_key_prefix}_...{accountData.api_key_last_chars}
                    </div>
                  ) : (
                    <p className="text-amber-600 text-sm">No se ha generado ninguna API Key</p>
                  )}
                  {accountData.api_key_created_at && (
                    <p className="text-xs text-gray-500 mt-2">
                      Generada: {format(parseISO(accountData.api_key_created_at), 'PPP', { locale: es })}
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-gray-500">No hay datos disponibles</p>
              )}
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm">
                <Link href="/api-keys" className="flex items-center">
                  <ExternalLinkIcon className="mr-2 h-4 w-4" />
                  Gestionar Keys
                </Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Acciones Rápidas */}
          <Card className="col-span-1 md:col-span-3 lg:col-span-1">
            <QuickActions />
          </Card>
        </div>

        {/* Segunda fila de widgets */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Widget de Plan Actual */}
          <Card className="col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <CreditCardIcon className="h-5 w-5 mr-2 text-purple-500" />
                Plan Actual
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isDataLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
              ) : accountError ? (
                <p className="text-red-500 text-sm">Error al cargar datos</p>
              ) : (accountData?.subscription || usageData) ? (
                <div>
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold">
                      {(() => {
                        // Intentamos obtener el plan primero desde accountData, luego desde usageData
                        const planType = accountData?.subscription?.plan || 
                                        (usageData ? usageData.subscription.plan : null);
                        
                        return plans && planType ? plans[planType]?.name || planType : 'Desconocido';
                      })()}
                    </span>
                    {(accountData?.subscription?.is_active || (usageData && usageData.subscription.is_active)) ? (
                      <Badge className="bg-green-100 text-green-800">Activo</Badge>
                    ) : (
                      <Badge variant="destructive">Inactivo</Badge>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {(() => {
                      const expiryDate = accountData?.subscription?.expires_at || 
                                        (usageData ? usageData.subscription.expires_at : null);
                      
                      if (expiryDate) {
                        return <>Renovación: {format(parseISO(expiryDate), 'PPP', { locale: es })}</>;
                      }
                      return 'Sin fecha de renovación';
                    })()}
                  </p>
                </div>
              ) : (
                <p className="text-gray-500">No hay datos disponibles</p>
              )}
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm">
                <Link href="/billing" className="flex items-center">
                  <ExternalLinkIcon className="mr-2 h-4 w-4" />
                  Gestionar Plan
                </Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Widget de Documentación */}
          <Card className="col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <BookOpenIcon className="h-5 w-5 mr-2 text-green-500" />
                Documentación
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Consulta nuestra documentación completa para aprender a integrar la API en tus aplicaciones.
              </p>
              <div className="mt-2 space-y-1">
                <p className="text-sm flex items-center">
                  <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  Guías de inicio rápido
                </p>
                <p className="text-sm flex items-center">
                  <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  Referencia de API
                </p>
                <p className="text-sm flex items-center">
                  <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  Ejemplos de código
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm">
                <a
                  href="https://docs.rayuela.ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center"
                >
                  <ExternalLinkIcon className="mr-2 h-4 w-4" />
                  Ver Documentación
                </a>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }