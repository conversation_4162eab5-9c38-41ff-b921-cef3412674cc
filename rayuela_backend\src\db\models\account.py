from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    Boolean,
    func,
    Identity,
)
from sqlalchemy.orm import relationship
from src.db.base import Base


class Account(Base):
    __tablename__ = "accounts"

    # account_id = Column(Integer, primary_key=True, autoincrement=False)
    account_id = Column(Integer, Identity(start=1, increment=1), primary_key=True)

    name = Column(String, nullable=False)
    # stripe_customer_id eliminado
    mercadopago_customer_id = Column(
        String(255), index=True, nullable=True, comment="Mercado Pago Customer ID"
    )
    created_at = Column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=func.now(),
        server_default=func.now(),
        onupdate=func.now(),
    )
    is_active = Column(Boolean, default=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # No table-level constraints needed since API keys are now managed in separate table

    # Relationships
    model_metadata = relationship(
        "ModelMetadata",
        back_populates="account",
        passive_deletes=True,
        foreign_keys="[ModelMetadata.account_id]",
    )
    system_users = relationship(
        "SystemUser", back_populates="account", passive_deletes=True
    )
    roles = relationship("Role", back_populates="account", passive_deletes=True)
    permissions = relationship(
        "Permission", back_populates="account", passive_deletes=True
    )
    subscription = relationship(
        "Subscription", back_populates="account", uselist=False, passive_deletes=True
    )
    usage_metrics = relationship(
        "AccountUsageMetrics",
        back_populates="account",
        uselist=False,
        passive_deletes=True,
    )
    end_users = relationship("EndUser", back_populates="account", passive_deletes=True)
    products = relationship("Product", back_populates="account", passive_deletes=True)
    interactions = relationship(
        "Interaction", back_populates="account", passive_deletes=True
    )
    searches = relationship("Search", back_populates="account", passive_deletes=True)
    notifications = relationship(
        "Notification", back_populates="account", passive_deletes=True
    )
    training_jobs = relationship(
        "TrainingJob", back_populates="account", passive_deletes=True
    )
    audit_log = relationship("AuditLog", back_populates="account", passive_deletes=True)
    model_metrics = relationship(
        "ModelMetric", back_populates="account", passive_deletes=True
    )
    system_user_roles = relationship(
        "SystemUserRole", back_populates="account", passive_deletes=True
    )
    recommendations = relationship(
        "Recommendation", back_populates="account", passive_deletes=True
    )
    artifact_metadata = relationship(
        "ModelMetadata",
        back_populates="account",
        passive_deletes=True,
        overlaps="model_metadata",
    )
    training_metrics = relationship(
        "TrainingMetrics", back_populates="account", passive_deletes=True
    )
    batch_ingestion_jobs = relationship(
        "BatchIngestionJob", back_populates="account", passive_deletes=True
    )
    orders = relationship("Order", back_populates="account", passive_deletes=True)
    api_keys = relationship("ApiKey", back_populates="account", passive_deletes=True)
