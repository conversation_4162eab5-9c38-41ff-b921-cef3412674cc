import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.schemas.recommendation import PageType, DeviceType, RecommendationContext
from src.ml_pipeline.post_processing_service import PostProcessingService
from src.ml_pipeline.serving_engine import ServingEngine
from main import app


@pytest.fixture
def mock_db():
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def mock_post_processing_service():
    service = PostProcessingService()
    service.contextual_filter = MagicMock()
    service.contextual_reranker = MagicMock()
    service.diversification_engine = MagicMock()
    service.recency_booster = MagicMock()
    service.ltr_applier = MagicMock()
    return service


@pytest.fixture
def mock_serving_engine():
    engine = ServingEngine(MagicMock())
    engine.model_loader = AsyncMock()
    engine.collab_recommender = AsyncMock()
    engine.content_recommender = AsyncMock()
    engine.hybrid_recommender = MagicMock()
    return engine


@pytest.mark.asyncio
async def test_context_filters():
    """Test that apply_filters correctly filters recommendations based on context"""
    service = PostProcessingService()
    
    # Create test recommendations
    recommendations = [
        {"item_id": 1, "category_id": 10, "score": 0.9},
        {"item_id": 2, "category_id": 20, "score": 0.8},
        {"item_id": 3, "category_id": 10, "score": 0.7},
        {"item_id": 4, "category_id": 30, "score": 0.6},
    ]
    
    # Test filtering by source_item_id in product detail page
    context = RecommendationContext(
        page_type=PageType.PRODUCT_DETAIL,
        source_item_id=1
    )
    
    filtered = service.contextual_filter.apply_filters(recommendations, context)
    assert len(filtered) == 3
    assert all(r["item_id"] != 1 for r in filtered)
    
    # Test filtering by category_id in category page
    context = RecommendationContext(
        page_type=PageType.CATEGORY,
        category_id=10
    )
    
    filtered = service.contextual_filter.apply_filters(recommendations, context)
    assert len(filtered) == 2
    assert all(r["category_id"] == 10 for r in filtered)
    
    # Test filtering by cart_item_ids
    context = RecommendationContext(
        cart_item_ids=[2, 3]
    )
    
    filtered = service.contextual_filter.apply_filters(recommendations, context)
    assert len(filtered) == 2
    assert all(r["item_id"] not in [2, 3] for r in filtered)
    
    # Test filtering by custom attributes
    context = RecommendationContext(
        custom_attributes={"filter_category_id": "10"}
    )
    
    filtered = service.contextual_filter.apply_filters(recommendations, context)
    assert len(filtered) == 2
    assert all(r["category_id"] == 10 for r in filtered)


@pytest.mark.asyncio
async def test_context_reranking():
    """Test that apply_reranking correctly re-ranks recommendations based on context"""
    service = PostProcessingService()
    
    # Create test recommendations
    recommendations = [
        {"item_id": 1, "category": "electronics", "price": 100, "average_rating": 4.5, "score": 0.9},
        {"item_id": 2, "category": "books", "price": 20, "average_rating": 4.0, "score": 0.8},
        {"item_id": 3, "category": "electronics", "price": 90, "average_rating": 3.5, "score": 0.7},
        {"item_id": 4, "category": "clothing", "price": 50, "average_rating": 4.8, "score": 0.6},
    ]
    
    # Test boosting similar products in product detail page
    context = RecommendationContext(
        page_type=PageType.PRODUCT_DETAIL,
        source_item_id=1
    )
    
    # Add source product to recommendations for the test
    recommendations_with_source = recommendations.copy()
    
    reranked = service.contextual_reranker.apply_reranking(recommendations_with_source, context, user_id=123)
    
    # Check that electronics products got boosted
    electronics_items = [r for r in reranked if r["category"] == "electronics"]
    for item in electronics_items:
        if item["item_id"] != 1:  # Skip source item
            assert item.get("context_boosted") is True
            assert item["score"] > 0.7  # Original score was 0.7
    
    # Test device-specific boosting
    context = RecommendationContext(
        device=DeviceType.MOBILE
    )
    
    reranked = service.contextual_reranker.apply_reranking(recommendations, context, user_id=123)
    
    # Check that high-rated and low-price items got boosted on mobile
    for item in reranked:
        if item["average_rating"] >= 4.0 or item["price"] < 50:
            assert item["score"] > recommendations[recommendations.index(item)]["score"]
    
    # Test recently viewed boosting
    context = RecommendationContext(
        recently_viewed_ids=[2, 4]
    )
    
    reranked = service.contextual_reranker.apply_reranking(recommendations, context, user_id=123)
    
    # Check that recently viewed items got a small boost
    for item in reranked:
        if item["item_id"] in [2, 4]:
            assert item["score"] > recommendations[recommendations.index(item)]["score"]


@pytest.mark.asyncio
async def test_api_endpoint_with_context(mock_db, mock_post_processing_service, mock_serving_engine):
    """Test that the API endpoint correctly handles the context parameter"""
    from httpx import AsyncClient
    from src.api.v1.endpoints.recommendations import router
    
    # Mock dependencies
    with patch("src.api.v1.endpoints.recommendations.get_current_account") as mock_get_account:
        mock_get_account.return_value = MagicMock(account_id=1)
        
        with patch("src.api.v1.endpoints.recommendations.get_db") as mock_get_db:
            mock_get_db.return_value = mock_db
            
            with patch("src.api.v1.endpoints.recommendations.get_pagination_params") as mock_pagination:
                mock_pagination.return_value = (0, 10)
                
                with patch("src.api.v1.endpoints.recommendations.get_limit_service") as mock_limit_service:
                    mock_limit_service.return_value = MagicMock()
                    
                    with patch("src.api.v1.endpoints.recommendations.serving_engine") as mock_serving:
                        mock_serving.get_candidate_recommendations.return_value = [
                            {"item_id": 1, "name": "Test Product", "score": 0.9}
                        ]
                        
                        with patch("src.api.v1.endpoints.recommendations.post_processing") as mock_processing:
                            mock_processing.apply_post_processing.return_value = [
                                {"item_id": 1, "name": "Test Product", "score": 0.95}
                            ]
                            
                            # Create test client
                            client = AsyncClient(app=app, base_url="http://test")
                            
                            # Test with context parameter
                            context = {
                                "page_type": "product_detail",
                                "device": "mobile",
                                "source_item_id": 123
                            }
                            
                            response = client.post(
                                "/api/v1/recommendations/personalized/query",
                                json={
                                    "user_id": 1,
                                    "context": context
                                }
                            )
                            
                            assert response.status_code == 200
                            assert "items" in response.json()
                            
                            # Verify context was passed to serving engine
                            mock_serving.get_candidate_recommendations.assert_called_once()
                            # Verify context was passed to post processing
                            mock_processing.apply_post_processing.assert_called_once()
