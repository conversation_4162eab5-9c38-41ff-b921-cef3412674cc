// src/app/(public)/signup/page.tsx
"use client";

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";

import { registerUser } from '@/lib/api'; // Función para registrar usuario
import { toast } from "sonner";
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// Esquema de validación con Zod
const signupSchema = z.object({
  accountName: z.string().min(1, { message: "Nombre de cuenta es requerido." }),
  email: z.string().email({ message: "Email inválido." }),
  password: z.string().min(8, { message: "Contraseña debe tener al menos 8 caracteres." }),
  confirmPassword: z.string().min(8, { message: "Confirma tu contraseña." }),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: "Debes aceptar los términos y condiciones."
  })
}).refine((data) => data.password === data.confirmPassword, {
  message: "Las contraseñas no coinciden",
  path: ["confirmPassword"],
});

type SignupFormValues = z.infer<typeof signupSchema>;

export default function SignupPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const [registeredEmail, setRegisteredEmail] = useState("");
  const router = useRouter();

  const form = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      accountName: '',
      email: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false
    },
  });

  const onSubmit = async (data: SignupFormValues) => {
    setIsLoading(true);
    try {
      // Llamar a la API para registrar el usuario
      await registerUser(data.accountName, data.email, data.password);

      // Guardar el email registrado para mostrarlo en el mensaje
      setRegisteredEmail(data.email);
      setIsRegistered(true);
      toast.success("¡Cuenta creada! Verifica tu email para continuar.");

    } catch (error: any) {
      console.error("Signup failed:", error);
      toast.error(error.message || "Error al crear la cuenta.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>{isRegistered ? "Verificación Pendiente" : "Crear Cuenta"}</CardTitle>
          <CardDescription>
            {isRegistered
              ? "Revisa tu email para verificar tu cuenta"
              : "Regístrate en Rayuela."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isRegistered ? (
            <Alert className="bg-blue-50 border-blue-200">
              <AlertTitle className="text-blue-800">Verificación de email requerida</AlertTitle>
              <AlertDescription className="text-blue-700">
                <p>Hemos enviado un email de verificación a <strong>{registeredEmail}</strong>.</p>
                <p className="mt-2">Por favor, revisa tu bandeja de entrada y haz clic en el enlace de verificación para activar tu cuenta.</p>
              </AlertDescription>
            </Alert>
          ) : (
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <Label htmlFor="accountName">Nombre de la Cuenta</Label>
              <Input
                id="accountName"
                type="text"
                placeholder="Mi Empresa Inc."
                {...form.register('accountName')}
                disabled={isLoading}
              />
              {form.formState.errors.accountName && (
                <p className="text-red-500 text-sm mt-1">{form.formState.errors.accountName.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...form.register('email')}
                disabled={isLoading}
              />
              {form.formState.errors.email && (
                <p className="text-red-500 text-sm mt-1">{form.formState.errors.email.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="password">Contraseña</Label>
              <Input
                id="password"
                type="password"
                placeholder="********"
                {...form.register('password')}
                disabled={isLoading}
              />
              {form.formState.errors.password && (
                <p className="text-red-500 text-sm mt-1">{form.formState.errors.password.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="confirmPassword">Confirmar Contraseña</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="********"
                {...form.register('confirmPassword')}
                disabled={isLoading}
              />
              {form.formState.errors.confirmPassword && (
                <p className="text-red-500 text-sm mt-1">{form.formState.errors.confirmPassword.message}</p>
              )}
            </div>

            <div className="flex items-start space-x-2">
              <Checkbox 
                id="acceptTerms" 
                checked={form.watch('acceptTerms')}
                onCheckedChange={(checked: boolean) => 
                  form.setValue('acceptTerms', checked)
                }
                disabled={isLoading}
              />
              <div className="grid gap-1.5 leading-none">
                <Label
                  htmlFor="acceptTerms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Acepto los <Link href="/legal/terms" className="text-blue-600 hover:underline" target="_blank">Términos y Condiciones</Link> y la <Link href="/legal/privacy" className="text-blue-600 hover:underline" target="_blank">Política de Privacidad</Link>
                </Label>
                {form.formState.errors.acceptTerms && (
                  <p className="text-red-500 text-sm">{form.formState.errors.acceptTerms.message}</p>
                )}
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Creando cuenta...' : 'Crear Cuenta'}
            </Button>
          </form>
          )}

          {!isRegistered && (
            <p className="mt-4 text-center text-sm text-gray-600">
              ¿Ya tienes cuenta?{' '}
              <Link href="/login" className="font-medium text-indigo-600 hover:text-indigo-500">
                Inicia sesión
              </Link>
            </p>
          )}
        </CardContent>

        {isRegistered && (
          <CardFooter>
            <Button
              onClick={() => router.push('/login')}
              className="w-full"
            >
              Ir a iniciar sesión
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}