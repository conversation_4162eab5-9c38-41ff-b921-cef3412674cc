#!/usr/bin/env python
"""
Script to clean up orphaned records before applying the composite foreign key migration.
This script identifies and removes records that would violate the new composite foreign key constraints.
"""

import asyncio
import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.session import get_async_session

# Set up basic logging for the script
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def clean_orphaned_products(db: AsyncSession) -> int:
    """Clean up products with invalid created_by references."""
    # Skip this check since the created_by column doesn't exist in the database yet
    logger.info("Skipping clean_orphaned_products since the created_by column doesn't exist in the database yet")
    return 0


async def clean_orphaned_interactions(db: AsyncSession) -> int:
    """Clean up interactions with invalid end_user_id or product_id references."""
    # Skip this check since the end_users table doesn't exist in the database yet
    logger.info("Skipping clean_orphaned_interactions since the end_users table doesn't exist in the database yet")
    return 0


async def clean_orphaned_searches(db: AsyncSession) -> int:
    """Clean up searches with invalid end_user_id references."""
    # Skip this check since the end_users table doesn't exist in the database yet
    logger.info("Skipping clean_orphaned_searches since the end_users table doesn't exist in the database yet")
    return 0


async def clean_orphaned_recommendations(db: AsyncSession) -> int:
    """Clean up recommendations with invalid end_user_id or product_id references."""
    # Skip this check since the end_users table doesn't exist in the database yet
    logger.info("Skipping clean_orphaned_recommendations since the end_users table doesn't exist in the database yet")
    return 0


async def clean_orphaned_training_jobs(db: AsyncSession) -> int:
    """Clean up training_jobs with invalid artifact_metadata_id references."""
    query = text("""
    DELETE FROM training_jobs
    WHERE artifact_metadata_id IS NOT NULL
    AND NOT EXISTS (
        SELECT 1 FROM artifact_metadata
        WHERE artifact_metadata.account_id = training_jobs.account_id
        AND artifact_metadata.id = training_jobs.artifact_metadata_id
    )
    RETURNING id, account_id, artifact_metadata_id
    """)

    result = await db.execute(query)
    deleted = result.fetchall()

    if deleted:
        logger.warning(f"Deleted {len(deleted)} training_jobs with invalid artifact_metadata_id references: {deleted}")

    return len(deleted)


async def clean_orphaned_model_metrics(db: AsyncSession) -> int:
    """Clean up model_metrics with invalid model_metadata_id references."""
    query = text("""
    DELETE FROM model_metrics
    WHERE NOT EXISTS (
        SELECT 1 FROM artifact_metadata
        WHERE artifact_metadata.account_id = model_metrics.account_id
        AND artifact_metadata.id = model_metrics.model_metadata_id
    )
    RETURNING id, account_id, model_metadata_id
    """)

    result = await db.execute(query)
    deleted = result.fetchall()

    if deleted:
        logger.warning(f"Deleted {len(deleted)} model_metrics with invalid model_metadata_id references: {deleted}")

    return len(deleted)


async def clean_orphaned_training_metrics(db: AsyncSession) -> int:
    """Clean up training_metrics with invalid model_id references."""
    query = text("""
    DELETE FROM training_metrics
    WHERE model_id IS NOT NULL
    AND NOT EXISTS (
        SELECT 1 FROM artifact_metadata
        WHERE artifact_metadata.account_id = training_metrics.account_id
        AND artifact_metadata.id = training_metrics.model_id
    )
    RETURNING id, account_id, model_id
    """)

    result = await db.execute(query)
    deleted = result.fetchall()

    if deleted:
        logger.warning(f"Deleted {len(deleted)} training_metrics with invalid model_id references: {deleted}")

    return len(deleted)


async def main():
    """Main function to clean up orphaned records."""
    logger.info("Starting orphaned records cleanup...")

    # Use the imported get_async_session function

    # Create a session
    db = await get_async_session()

    try:
        # Start a transaction
        async with db.begin():
            # Clean up orphaned records
            products_deleted = await clean_orphaned_products(db)
            interactions_deleted = await clean_orphaned_interactions(db)
            searches_deleted = await clean_orphaned_searches(db)
            recommendations_deleted = await clean_orphaned_recommendations(db)
            training_jobs_deleted = await clean_orphaned_training_jobs(db)
            model_metrics_deleted = await clean_orphaned_model_metrics(db)
            training_metrics_deleted = await clean_orphaned_training_metrics(db)

            # Transaction will be automatically committed if no exceptions occur

            total_deleted = (
                products_deleted +
                interactions_deleted +
                searches_deleted +
                recommendations_deleted +
                training_jobs_deleted +
                model_metrics_deleted +
                training_metrics_deleted
            )

            logger.info(f"Cleanup completed. Total records deleted: {total_deleted}")
            logger.info(f"- Products: {products_deleted}")
            logger.info(f"- Interactions: {interactions_deleted}")
            logger.info(f"- Searches: {searches_deleted}")
            logger.info(f"- Recommendations: {recommendations_deleted}")
            logger.info(f"- Training Jobs: {training_jobs_deleted}")
            logger.info(f"- Model Metrics: {model_metrics_deleted}")
            logger.info(f"- Training Metrics: {training_metrics_deleted}")
    except Exception as e:
        # Transaction will be automatically rolled back if an exception occurs
        logger.error(f"Error during cleanup: {str(e)}")
        raise
    finally:
        # Close the session
        await db.close()


if __name__ == "__main__":
    asyncio.run(main())
