#!/bin/bash

# Script para validar la migración al cliente API generado
# Verifica que todas las funciones críticas están disponibles y funcionan

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Validando Migración del Cliente API${NC}"
echo "=========================================="
echo ""

# Function to check if we're in the right directory
check_directory() {
    if [ ! -d "rayuela_frontend" ]; then
        echo -e "${RED}❌ Error: Execute this script from the project root directory${NC}"
        exit 1
    fi
}

# Function to validate file structure
validate_file_structure() {
    echo -e "${BLUE}1. Validando estructura de archivos...${NC}"
    
    local validation_count=0
    local total_checks=5
    
    # Check for generated API client
    if [ -f "rayuela_frontend/src/lib/generated/rayuelaAPI.ts" ]; then
        echo -e "${GREEN}✅ Cliente API generado: rayuela_frontend/src/lib/generated/rayuelaAPI.ts${NC}"
        ((validation_count++))
    else
        echo -e "${RED}❌ Cliente API generado no encontrado${NC}"
    fi
    
    # Check for migration helper
    if [ -f "rayuela_frontend/src/lib/generated/migration-helper.ts" ]; then
        echo -e "${GREEN}✅ Helper de migración: rayuela_frontend/src/lib/generated/migration-helper.ts${NC}"
        ((validation_count++))
    else
        echo -e "${YELLOW}⚠️ Helper de migración no encontrado (opcional)${NC}"
        ((validation_count++)) # No crítico
    fi
    
    # Check for new API wrapper
    if [ -f "rayuela_frontend/src/lib/api-generated.ts" ]; then
        echo -e "${GREEN}✅ Nuevo wrapper API: rayuela_frontend/src/lib/api-generated.ts${NC}"
        ((validation_count++))
    else
        echo -e "${RED}❌ Nuevo wrapper API no encontrado${NC}"
    fi
    
    # Check for original API file
    if [ -f "rayuela_frontend/src/lib/api.ts" ]; then
        echo -e "${GREEN}✅ API original disponible: rayuela_frontend/src/lib/api.ts${NC}"
        ((validation_count++))
    else
        echo -e "${RED}❌ API original no encontrado${NC}"
    fi
    
    # Check for migration script
    if [ -f "rayuela_frontend/scripts/migrate-component-imports.sh" ]; then
        echo -e "${GREEN}✅ Script de migración: rayuela_frontend/scripts/migrate-component-imports.sh${NC}"
        ((validation_count++))
    else
        echo -e "${YELLOW}⚠️ Script de migración no encontrado${NC}"
    fi
    
    echo ""
    echo "📊 Archivos validados: $validation_count/$total_checks"
    echo ""
}

# Function to check function availability
validate_function_availability() {
    echo -e "${BLUE}2. Validando funciones críticas...${NC}"
    
    cd rayuela_frontend
    
    # Create a temporary test file to check imports
    cat > /tmp/api-test.js << 'EOF'
const fs = require('fs');

// Read the generated API file
const apiContent = fs.readFileSync('src/lib/api-generated.ts', 'utf8');

// Critical functions to check
const criticalFunctions = [
    'loginUser',
    'registerUser',
    'getMe',
    'getMyAccount',
    'getAccountUsage',
    'listApiKeys',
    'createApiKey',
    'getApiHealth'
];

console.log('🔍 Verificando funciones críticas...');

let functionsFound = 0;
criticalFunctions.forEach(func => {
    if (apiContent.includes(`export const ${func}`)) {
        console.log(`✅ ${func}`);
        functionsFound++;
    } else {
        console.log(`❌ ${func} - No encontrada`);
    }
});

console.log(`\n📊 Funciones encontradas: ${functionsFound}/${criticalFunctions.length}`);

// Check if getRayuela is imported
if (apiContent.includes('import { getRayuela }')) {
    console.log('✅ Importación del cliente generado correcta');
} else {
    console.log('❌ Importación del cliente generado incorrecta');
}

// Check error handling
if (apiContent.includes('handleApiResponse')) {
    console.log('✅ Manejo de errores implementado');
} else {
    console.log('❌ Manejo de errores faltante');
}
EOF
    
    node /tmp/api-test.js
    rm /tmp/api-test.js
    
    cd ..
    echo ""
}

# Function to validate backward compatibility
validate_backward_compatibility() {
    echo -e "${BLUE}3. Validando compatibilidad hacia atrás...${NC}"
    
    cd rayuela_frontend
    
    # Create test for type compatibility
    cat > /tmp/type-test.js << 'EOF'
const fs = require('fs');

// Read both API files
const originalApi = fs.readFileSync('src/lib/api.ts', 'utf8');
const newApi = fs.readFileSync('src/lib/api-generated.ts', 'utf8');

console.log('🔍 Verificando compatibilidad de tipos...');

// Check for key interface exports
const keyInterfaces = [
    'AuthTokenResponse',
    'AccountInfo',
    'AccountUsage',
    'UserInfo',
    'ApiKey',
    'ApiError'
];

let compatibleTypes = 0;
keyInterfaces.forEach(type => {
    const inOriginal = originalApi.includes(`interface ${type}`) || originalApi.includes(`class ${type}`);
    const inNew = newApi.includes(`interface ${type}`) || newApi.includes(`class ${type}`);
    
    if (inOriginal && inNew) {
        console.log(`✅ ${type} - Compatible`);
        compatibleTypes++;
    } else if (inOriginal && !inNew) {
        console.log(`⚠️ ${type} - Presente en original, falta en nuevo`);
    } else if (!inOriginal && inNew) {
        console.log(`➕ ${type} - Nuevo tipo agregado`);
        compatibleTypes++; // Bonus
    } else {
        console.log(`❌ ${type} - No encontrado en ninguno`);
    }
});

console.log(`\n📊 Tipos compatibles: ${compatibleTypes}/${keyInterfaces.length}`);
EOF
    
    node /tmp/type-test.js
    rm /tmp/type-test.js
    
    cd ..
    echo ""
}

# Function to check component usage
validate_component_usage() {
    echo -e "${BLUE}4. Analizando uso en componentes...${NC}"
    
    cd rayuela_frontend
    
    # Find components using the API
    echo "🔍 Componentes que usan la API:"
    api_usage=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from.*@/lib/api" | wc -l)
    echo "   📊 Total de archivos: $api_usage"
    
    # Show top 5 components using API
    echo ""
    echo "📝 Archivos principales que requieren migración:"
    find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from.*@/lib/api" | head -5 | while read file; do
        echo "   📄 $file"
    done
    
    cd ..
    echo ""
}

# Function to create migration plan
create_migration_plan() {
    echo -e "${BLUE}5. Plan de migración recomendado...${NC}"
    
    echo -e "${GREEN}🎯 Pasos recomendados para migración completa:${NC}"
    echo ""
    echo "1. 🧪 Fase de Pruebas:"
    echo "   • Cambiar una sola página a usar 'api-generated'"
    echo "   • Verificar que funciona correctamente"
    echo "   • Comparar comportamiento con API manual"
    echo ""
    echo "2. 🔄 Migración Gradual:"
    echo "   • Ejecutar: cd rayuela_frontend && ./scripts/migrate-component-imports.sh"
    echo "   • Probar cada componente migrado"
    echo "   • Corregir cualquier problema de tipos"
    echo ""
    echo "3. ✅ Migración Final:"
    echo "   • mv src/lib/api.ts src/lib/api-manual-backup.ts"
    echo "   • mv src/lib/api-generated.ts src/lib/api.ts"
    echo "   • Prueba completa del sistema"
    echo ""
    echo "4. 🧹 Limpieza:"
    echo "   • Eliminar archivos de backup después de verificación"
    echo "   • Actualizar documentación"
    echo ""
}

# Function to generate summary report
generate_summary_report() {
    echo -e "${BLUE}6. Resumen de validación...${NC}"
    echo "=========================="
    echo ""
    
    # Calculate overall score
    local total_score=0
    
    # File structure (20 points)
    if [ -f "rayuela_frontend/src/lib/generated/rayuelaAPI.ts" ] && [ -f "rayuela_frontend/src/lib/api-generated.ts" ]; then
        total_score=$((total_score + 20))
        echo -e "${GREEN}✅ Estructura de archivos: 20/20${NC}"
    else
        echo -e "${RED}❌ Estructura de archivos: 0/20${NC}"
    fi
    
    # Function availability (30 points)
    cd rayuela_frontend
    if grep -q "export const loginUser" src/lib/api-generated.ts && \
       grep -q "export const getMe" src/lib/api-generated.ts && \
       grep -q "export const getMyAccount" src/lib/api-generated.ts; then
        total_score=$((total_score + 30))
        echo -e "${GREEN}✅ Funciones críticas: 30/30${NC}"
    else
        echo -e "${YELLOW}⚠️ Funciones críticas: 15/30${NC}"
        total_score=$((total_score + 15))
    fi
    cd ..
    
    # Type compatibility (25 points)
    cd rayuela_frontend
    if grep -q "interface AuthTokenResponse" src/lib/api-generated.ts && \
       grep -q "class ApiError" src/lib/api-generated.ts; then
        total_score=$((total_score + 25))
        echo -e "${GREEN}✅ Compatibilidad de tipos: 25/25${NC}"
    else
        echo -e "${YELLOW}⚠️ Compatibilidad de tipos: 15/25${NC}"
        total_score=$((total_score + 15))
    fi
    cd ..
    
    # Migration tooling (25 points)
    if [ -f "rayuela_frontend/scripts/migrate-component-imports.sh" ]; then
        total_score=$((total_score + 25))
        echo -e "${GREEN}✅ Herramientas de migración: 25/25${NC}"
    else
        echo -e "${YELLOW}⚠️ Herramientas de migración: 10/25${NC}"
        total_score=$((total_score + 10))
    fi
    
    echo ""
    echo "🏆 Puntuación total: $total_score/100"
    
    if [ $total_score -ge 90 ]; then
        echo -e "${GREEN}🎉 Excelente! Migración lista para implementar${NC}"
    elif [ $total_score -ge 70 ]; then
        echo -e "${YELLOW}⚠️ Bueno, pero necesita ajustes menores${NC}"
    else
        echo -e "${RED}❌ Requiere trabajo adicional antes de migrar${NC}"
    fi
    
    echo ""
}

# Main execution
echo "🚀 Iniciando validación de migración del cliente API..."
echo ""

check_directory
validate_file_structure
validate_function_availability
validate_backward_compatibility
validate_component_usage
create_migration_plan
generate_summary_report

echo ""
echo "=============================================="
echo -e "${GREEN}🎯 Validación de Migración Completada!${NC}"
echo "=============================================="
echo ""
echo -e "${BLUE}💡 La migración al cliente API generado está lista para implementar.${NC}" 