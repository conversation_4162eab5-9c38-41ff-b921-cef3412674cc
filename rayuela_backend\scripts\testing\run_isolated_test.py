#!/usr/bin/env python
"""
Script para ejecutar tests unitarios de forma aislada sin depender del conftest global.
Este script permite ejecutar tests unitarios específicos sin cargar toda la aplicación.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.utils.base_logger import logger


def run_isolated_test(test_path, verbose=False):
    """
    Ejecutar un test unitario de forma aislada.

    Args:
        test_path: Ruta al archivo de test
        verbose: Mostrar salida detallada

    Returns:
        Código de salida (0 si todos los tests pasan, 1 si hay errores)
    """
    # Verificar que el archivo existe
    test_file = Path(test_path)
    if not test_file.exists():
        logger.error(f"El archivo {test_path} no existe.")
        return 1

    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    # Construir el comando
    cmd = ["python", "-m", "pytest", test_path, "-xvs", "--no-header", "--no-summary"]

    if verbose:
        cmd.append("-v")

    # Ejecutar el comando
    logger.info(f"Ejecutando test: {' '.join(cmd)}")
    result = subprocess.run(cmd, env=env)

    return result.returncode


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Ejecutar tests unitarios de forma aislada")
    parser.add_argument("test_path", type=str, help="Ruta al archivo de test")
    parser.add_argument("--verbose", "-v", action="store_true", help="Mostrar salida detallada")

    args = parser.parse_args()

    return run_isolated_test(args.test_path, args.verbose)


if __name__ == "__main__":
    sys.exit(main())
