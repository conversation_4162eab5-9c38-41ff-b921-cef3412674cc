#!/bin/bash
# Script to migrate component imports from manual API to generated API

echo "🔄 Migrating component imports..."

# Find all files that import from '@/lib/api'
find src -name "*.tsx" -o -name "*.ts" | while read file; do
    if grep -q "from.*['\"]@/lib/api['\"]" "$file"; then
        echo "📝 Updating: $file"
        
        # Create backup
        cp "$file" "$file.backup"
        
        # Replace import statement
        sed -i "s|from ['\"]@/lib/api['\"]|from '@/lib/api-generated'|g" "$file"
        
        # Note: Manual verification might be needed for complex imports
        echo "   ✅ Import updated (backup created)"
    fi
done

echo "✅ Component migration completed"
echo "⚠️ Please verify the changes and test thoroughly"
echo "💡 Backups were created with .backup extension"
