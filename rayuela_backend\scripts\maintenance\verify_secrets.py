#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar que todos los secretos estén correctamente configurados
en Google Cloud Secret Manager.

Uso:
    python -m scripts.verify_secrets --project-id your-project-id
"""

import argparse
import subprocess
import json
import sys
from typing import Dict, List, Optional


class SecretVerifier:
    """Clase para verificar la configuración de secretos en Secret Manager."""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        
        # Lista de secretos requeridos para el funcionamiento de la aplicación
        self.required_secrets = [
            "DB_PASSWORD",
            "POSTGRES_SERVER", 
            "POSTGRES_USER",
            "POSTGRES_DB",
            "POSTGRES_PORT",
            "REDIS_PASSWORD",
            "REDIS_HOST",
            "REDIS_PORT", 
            "REDIS_URL",
            "SECRET_KEY",
            "GCS_BUCKET_NAME"
        ]
        
        # Lista de secretos opcionales
        self.optional_secrets = [
            "APP_DB_PASSWORD",
            "MAINTENANCE_DB_PASSWORD",
            "MERCADOPAGO_ACCESS_TOKEN",
            "MERCADOPAGO_PUBLIC_KEY", 
            "MERCADOPAGO_WEBHOOK_SECRET",
            "STRIPE_API_KEY",
            "STRIPE_WEBHOOK_SECRET"
        ]

    def check_gcloud_auth(self) -> bool:
        """Verificar que gcloud esté autenticado."""
        try:
            result = subprocess.run(
                ["gcloud", "auth", "list", "--filter=status:ACTIVE", "--format=value(account)"],
                capture_output=True,
                text=True,
                check=True
            )
            if result.stdout.strip():
                print(f"✅ Autenticado como: {result.stdout.strip()}")
                return True
            else:
                print("❌ No hay cuentas autenticadas en gcloud")
                return False
        except subprocess.CalledProcessError:
            print("❌ Error al verificar autenticación de gcloud")
            return False

    def list_existing_secrets(self) -> List[str]:
        """Listar todos los secretos existentes en el proyecto."""
        try:
            result = subprocess.run([
                "gcloud", "secrets", "list",
                "--project", self.project_id,
                "--format=value(name)"
            ], capture_output=True, text=True, check=True)
            
            secrets = [line.strip().split('/')[-1] for line in result.stdout.strip().split('\n') if line.strip()]
            return secrets
        except subprocess.CalledProcessError as e:
            print(f"❌ Error listando secretos: {e}")
            return []

    def check_secret_access(self, secret_name: str) -> bool:
        """Verificar que se pueda acceder a un secreto."""
        try:
            result = subprocess.run([
                "gcloud", "secrets", "versions", "access", "latest",
                "--secret", secret_name,
                "--project", self.project_id
            ], capture_output=True, text=True, check=True)
            
            # Verificar que el secreto no esté vacío
            value = result.stdout.strip()
            if value:
                return True
            else:
                print(f"   ⚠️ Secreto {secret_name} está vacío")
                return False
        except subprocess.CalledProcessError:
            print(f"   ❌ No se puede acceder al secreto {secret_name}")
            return False

    def get_secret_metadata(self, secret_name: str) -> Optional[Dict]:
        """Obtener metadatos de un secreto."""
        try:
            result = subprocess.run([
                "gcloud", "secrets", "describe", secret_name,
                "--project", self.project_id,
                "--format=json"
            ], capture_output=True, text=True, check=True)
            
            return json.loads(result.stdout)
        except subprocess.CalledProcessError:
            return None

    def verify_all_secrets(self) -> bool:
        """Verificar todos los secretos requeridos y opcionales."""
        print(f"🔍 Verificando secretos en proyecto: {self.project_id}")
        print()
        
        # Verificar autenticación
        if not self.check_gcloud_auth():
            print("Por favor, ejecuta: gcloud auth login")
            return False
        
        # Listar secretos existentes
        existing_secrets = self.list_existing_secrets()
        print(f"📋 Secretos encontrados en el proyecto: {len(existing_secrets)}")
        
        if not existing_secrets:
            print("❌ No se encontraron secretos en el proyecto")
            return False
        
        # Verificar secretos requeridos
        print("\n🔐 Verificando secretos requeridos:")
        missing_required = []
        inaccessible_required = []
        
        for secret in self.required_secrets:
            if secret in existing_secrets:
                print(f"   ✅ {secret}: Existe")
                if not self.check_secret_access(secret):
                    inaccessible_required.append(secret)
            else:
                print(f"   ❌ {secret}: No encontrado")
                missing_required.append(secret)
        
        # Verificar secretos opcionales
        print("\n🔧 Verificando secretos opcionales:")
        missing_optional = []
        
        for secret in self.optional_secrets:
            if secret in existing_secrets:
                print(f"   ✅ {secret}: Existe")
                self.check_secret_access(secret)
            else:
                print(f"   ⚠️ {secret}: No encontrado (opcional)")
                missing_optional.append(secret)
        
        # Verificar secretos adicionales no esperados
        all_expected = set(self.required_secrets + self.optional_secrets)
        unexpected = [s for s in existing_secrets if s not in all_expected]
        
        if unexpected:
            print("\n📝 Secretos adicionales encontrados:")
            for secret in unexpected:
                print(f"   ℹ️ {secret}: Secreto adicional")
        
        # Resumen de verificación
        print("\n📊 RESUMEN DE VERIFICACIÓN:")
        print(f"   ✅ Secretos requeridos encontrados: {len(self.required_secrets) - len(missing_required)}/{len(self.required_secrets)}")
        print(f"   ✅ Secretos opcionales encontrados: {len(self.optional_secrets) - len(missing_optional)}/{len(self.optional_secrets)}")
        
        if missing_required:
            print(f"   ❌ Secretos requeridos faltantes: {missing_required}")
        
        if inaccessible_required:
            print(f"   ❌ Secretos requeridos inaccesibles: {inaccessible_required}")
        
        if missing_optional:
            print(f"   ⚠️ Secretos opcionales faltantes: {missing_optional}")
        
        # Determinar si la verificación fue exitosa
        success = len(missing_required) == 0 and len(inaccessible_required) == 0
        
        if success:
            print("\n🎉 ¡Verificación exitosa! Todos los secretos requeridos están configurados correctamente.")
        else:
            print("\n❌ Verificación fallida. Algunos secretos requeridos faltan o son inaccesibles.")
            print("\n🔗 Para configurar los secretos faltantes, ejecuta:")
            print(f"   python -m scripts.setup_secrets --project-id {self.project_id}")
        
        return success

    def show_secret_details(self, secret_name: str):
        """Mostrar detalles de un secreto específico."""
        metadata = self.get_secret_metadata(secret_name)
        if metadata:
            print(f"\n📋 Detalles del secreto: {secret_name}")
            print(f"   Nombre completo: {metadata.get('name', 'N/A')}")
            print(f"   Fecha de creación: {metadata.get('createTime', 'N/A')}")
            print(f"   Política de replicación: {metadata.get('replication', {}).get('automatic', 'N/A')}")
            
            labels = metadata.get('labels', {})
            if labels:
                print(f"   Etiquetas: {labels}")
        else:
            print(f"❌ No se pudieron obtener detalles del secreto: {secret_name}")


def main():
    parser = argparse.ArgumentParser(description="Verificar secretos en Google Cloud Secret Manager")
    parser.add_argument("--project-id", required=True, help="ID del proyecto de Google Cloud")
    parser.add_argument("--secret", help="Verificar un secreto específico")
    parser.add_argument("--details", action="store_true", help="Mostrar detalles de los secretos")
    
    args = parser.parse_args()
    
    verifier = SecretVerifier(args.project_id)
    
    if args.secret:
        # Verificar un secreto específico
        verifier.show_secret_details(args.secret)
        success = verifier.check_secret_access(args.secret)
    else:
        # Verificar todos los secretos
        success = verifier.verify_all_secrets()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
