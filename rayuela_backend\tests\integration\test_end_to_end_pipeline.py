"""Integration tests for the end-to-end recommendation pipeline flow."""

import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import Fast<PERSON><PERSON>
from httpx import AsyncClient
from httpx import AsyncClient
import json
from datetime import datetime, timedelta

from main import app
from src.core.deps import get_db, get_current_account, get_limit_service
from src.services import LimitService

from tests.integration.conftest import mock_celery_tasks, execute_mock_tasks
from tests.integration.conftest import mock_gcs
from tests.conftest import db_session, override_get_db, test_account, test_admin_user

from src.db.models import Account, SystemUser, Product, EndUser, Interaction, TrainingJob
from src.db.enums import TrainingJobStatus, BatchIngestionJobStatus
from src.services.data_ingestion_service import DataIngestionService


@pytest.fixture
def client(override_get_db):
    """Create a test client with overridden dependencies."""
    app.dependency_overrides[get_db] = override_get_db
    with Async<PERSON>lient(app=app, base_url="http://test") as client:
        return client
    app.dependency_overrides = {}


@pytest_asyncio.fixture(scope="function")
async def async_client(override_get_db, test_account, test_admin_user):
    """Create an async test client with overridden dependencies."""

    # Override dependencies
    async def override_get_current_account():
        return {"account_id": test_account.id, "name": test_account.name}

    async def override_get_limit_service(db=None):
        if db is None:
            db = override_get_db()
        return LimitService(db=db, account_id=test_account.id)

    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_current_account] = override_get_current_account
    app.dependency_overrides[get_limit_service] = override_get_limit_service

    async with AsyncClient(app=app, base_url="http://test") as client:
        return client

    app.dependency_overrides = {}


@pytest.mark.asyncio
async def test_complete_end_to_end_flow(
    async_client, db_session, test_account, mock_celery_tasks, mock_gcs
):
    """
    Test the complete end-to-end flow from data ingestion to recommendations.

    This test covers:
    1. Batch data ingestion
    2. Training a model
    3. Getting recommendations
    4. Recording interactions
    5. Getting updated recommendations
    """
    # 1. Ingest batch data
    batch_data = {
        "users": [
            {"external_id": "user_1", "metadata": {"age": 25, "gender": "male"}},
            {"external_id": "user_2", "metadata": {"age": 30, "gender": "female"}},
            {"external_id": "user_3", "metadata": {"age": 35, "gender": "male"}},
            {"external_id": "user_4", "metadata": {"age": 40, "gender": "female"}},
            {"external_id": "user_5", "metadata": {"age": 45, "gender": "other"}},
        ],
        "products": [
            {
                "external_id": "product_1",
                "name": "Test Product 1",
                "description": "Description for Test Product 1",
                "category": "Category 1",
                "price": 10.0,
                "metadata": {"color": "red", "size": "small"},
            },
            {
                "external_id": "product_2",
                "name": "Test Product 2",
                "description": "Description for Test Product 2",
                "category": "Category 1",
                "price": 20.0,
                "metadata": {"color": "blue", "size": "medium"},
            },
            {
                "external_id": "product_3",
                "name": "Test Product 3",
                "description": "Description for Test Product 3",
                "category": "Category 2",
                "price": 30.0,
                "metadata": {"color": "green", "size": "large"},
            },
            {
                "external_id": "product_4",
                "name": "Test Product 4",
                "description": "Description for Test Product 4",
                "category": "Category 2",
                "price": 40.0,
                "metadata": {"color": "yellow", "size": "small"},
            },
            {
                "external_id": "product_5",
                "name": "Test Product 5",
                "description": "Description for Test Product 5",
                "category": "Category 3",
                "price": 50.0,
                "metadata": {"color": "purple", "size": "medium"},
            },
        ],
        "interactions": [
            {
                "end_user_external_id": "user_1",
                "product_external_id": "product_1",
                "interaction_type": "view",
                "rating": 4.5,
                "metadata": {"timestamp": "2023-01-01T12:00:00Z", "device": "mobile"},
            },
            {
                "end_user_external_id": "user_1",
                "product_external_id": "product_2",
                "interaction_type": "purchase",
                "rating": 5.0,
                "metadata": {"timestamp": "2023-01-02T12:00:00Z", "device": "desktop"},
            },
            {
                "end_user_external_id": "user_2",
                "product_external_id": "product_1",
                "interaction_type": "like",
                "rating": 3.5,
                "metadata": {"timestamp": "2023-01-03T12:00:00Z", "device": "tablet"},
            },
            {
                "end_user_external_id": "user_2",
                "product_external_id": "product_3",
                "interaction_type": "purchase",
                "rating": 4.0,
                "metadata": {"timestamp": "2023-01-04T12:00:00Z", "device": "mobile"},
            },
            {
                "end_user_external_id": "user_3",
                "product_external_id": "product_2",
                "interaction_type": "view",
                "rating": 3.0,
                "metadata": {"timestamp": "2023-01-05T12:00:00Z", "device": "desktop"},
            },
            {
                "end_user_external_id": "user_3",
                "product_external_id": "product_4",
                "interaction_type": "purchase",
                "rating": 4.5,
                "metadata": {"timestamp": "2023-01-06T12:00:00Z", "device": "tablet"},
            },
            {
                "end_user_external_id": "user_4",
                "product_external_id": "product_3",
                "interaction_type": "like",
                "rating": 5.0,
                "metadata": {"timestamp": "2023-01-07T12:00:00Z", "device": "mobile"},
            },
            {
                "end_user_external_id": "user_4",
                "product_external_id": "product_5",
                "interaction_type": "purchase",
                "rating": 4.0,
                "metadata": {"timestamp": "2023-01-08T12:00:00Z", "device": "desktop"},
            },
            {
                "end_user_external_id": "user_5",
                "product_external_id": "product_4",
                "interaction_type": "view",
                "rating": 3.5,
                "metadata": {"timestamp": "2023-01-09T12:00:00Z", "device": "tablet"},
            },
            {
                "end_user_external_id": "user_5",
                "product_external_id": "product_1",
                "interaction_type": "purchase",
                "rating": 4.5,
                "metadata": {"timestamp": "2023-01-10T12:00:00Z", "device": "mobile"},
            },
        ],
    }

    # Call the batch data ingestion endpoint
    response = await async_client.post("/api/v1/data/batch", json=batch_data)

    # Check response
    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "processing"
    assert "job_id" in data

    # Execute the mock Celery task for data ingestion
    batch_job_id = data["job_id"]
    await execute_mock_tasks(mock_celery_tasks, db_session)

    # Check the batch job status
    response = await async_client.get(f"/api/v1/data/batch/{batch_job_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"

    # 2. Train a model
    training_data = {
        "model_type": "collaborative",
        "parameters": {"factors": 10, "iterations": 5, "regularization": 0.1},
    }

    # Call the training endpoint
    response = await async_client.post("/api/v1/pipeline/train", json=training_data)

    # Check response
    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "processing"
    assert "job_id" in data

    # Execute the mock Celery task for training
    training_job_id = data["job_id"]
    await execute_mock_tasks(mock_celery_tasks, db_session)

    # Check the training job status
    response = await async_client.get(f"/api/v1/pipeline/jobs/{training_job_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"

    # 3. Get recommendations for a user
    # Mock the recommendation service to return predictable recommendations
    with patch(
        "src.api.v1.endpoints.recommendations.get_recommendation_service"
    ) as mock_get_service:
        mock_service = AsyncMock()

        # First set of recommendations
        mock_service.get_recommendations.return_value = [
            {"product_id": 3, "score": 0.95},  # product_3
            {"product_id": 5, "score": 0.85},  # product_5
            {"product_id": 4, "score": 0.75},  # product_4
        ]
        mock_get_service.return_value = mock_service

        # Call the recommendations endpoint
        response = await async_client.get("/api/v1/recommendations/user/user_1?limit=3")

        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
        assert data[0]["product_id"] == 3
        assert data[0]["score"] == 0.95
        assert data[0]["product"]["external_id"] == "product_3"

    # 4. Record a new interaction
    interaction_data = {
        "end_user_external_id": "user_1",
        "product_external_id": "product_3",
        "interaction_type": "purchase",
        "rating": 5.0,
        "metadata": {"timestamp": "2023-01-11T12:00:00Z", "device": "mobile"},
    }

    # Call the interaction endpoint
    response = await async_client.post("/api/v1/interactions", json=interaction_data)

    # Check response
    assert response.status_code == 201
    data = response.json()
    assert data["end_user_external_id"] == "user_1"
    assert data["product_external_id"] == "product_3"
    assert data["interaction_type"] == "purchase"

    # 5. Get updated recommendations
    # Mock the recommendation service to return updated recommendations
    with patch(
        "src.api.v1.endpoints.recommendations.get_recommendation_service"
    ) as mock_get_service:
        mock_service = AsyncMock()

        # Updated recommendations after new interaction
        mock_service.get_recommendations.return_value = [
            {"product_id": 5, "score": 0.95},  # product_5
            {"product_id": 4, "score": 0.90},  # product_4
            {"product_id": 2, "score": 0.80},  # product_2
        ]
        mock_get_service.return_value = mock_service

        # Call the recommendations endpoint again
        response = await async_client.get(
            "/api/v1/recommendations/user/user_1?limit=3&exclude_viewed=true"
        )

        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
        assert data[0]["product_id"] == 5
        assert data[0]["score"] == 0.95
        assert data[0]["product"]["external_id"] == "product_5"

    # 6. Get similar products
    # Mock the recommendation service to return similar products
    with patch(
        "src.api.v1.endpoints.recommendations.get_recommendation_service"
    ) as mock_get_service:
        mock_service = AsyncMock()

        # Similar products
        mock_service.get_similar_products.return_value = [
            {"product_id": 2, "score": 0.90},  # product_2
            {"product_id": 4, "score": 0.85},  # product_4
            {"product_id": 5, "score": 0.80},  # product_5
        ]
        mock_get_service.return_value = mock_service

        # Call the similar products endpoint
        response = await async_client.get(
            "/api/v1/recommendations/similar/product_3?limit=3"
        )

        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
        assert data[0]["product_id"] == 2
        assert data[0]["score"] == 0.90
        assert data[0]["product"]["external_id"] == "product_2"

    # 7. Get recommendations by category
    # Mock the recommendation service to return recommendations filtered by category
    with patch(
        "src.api.v1.endpoints.recommendations.get_recommendation_service"
    ) as mock_get_service:
        mock_service = AsyncMock()

        # Recommendations filtered by category
        mock_service.get_recommendations.return_value = [
            {"product_id": 3, "score": 0.95},  # product_3
            {"product_id": 4, "score": 0.85},  # product_4
        ]
        mock_get_service.return_value = mock_service

        # Call the recommendations endpoint with category filter
        response = await async_client.get(
            "/api/v1/recommendations/user/user_1?limit=3&category=Category%202"
        )

        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["product_id"] == 3
        assert data[0]["score"] == 0.95
        assert data[0]["product"]["external_id"] == "product_3"
        assert data[0]["product"]["category"] == "Category 2"
        assert data[1]["product_id"] == 4
        assert data[1]["score"] == 0.85
        assert data[1]["product"]["external_id"] == "product_4"
        assert data[1]["product"]["category"] == "Category 2"


@pytest.mark.asyncio
async def test_end_to_end_flow_with_retraining(
    async_client, db_session, test_account, mock_celery_tasks, mock_gcs
):
    """
    Test the end-to-end flow with retraining after new interactions.

    This test covers:
    1. Initial data ingestion
    2. Training a model
    3. Getting recommendations
    4. Ingesting new interactions
    5. Retraining the model
    6. Getting updated recommendations
    """
    # 1. Ingest initial batch data
    initial_batch_data = {
        "users": [
            {"external_id": "user_1", "metadata": {"age": 25, "gender": "male"}},
            {"external_id": "user_2", "metadata": {"age": 30, "gender": "female"}},
        ],
        "products": [
            {
                "external_id": "product_1",
                "name": "Test Product 1",
                "description": "Description for Test Product 1",
                "category": "Category 1",
                "price": 10.0,
                "metadata": {"color": "red", "size": "small"},
            },
            {
                "external_id": "product_2",
                "name": "Test Product 2",
                "description": "Description for Test Product 2",
                "category": "Category 1",
                "price": 20.0,
                "metadata": {"color": "blue", "size": "medium"},
            },
            {
                "external_id": "product_3",
                "name": "Test Product 3",
                "description": "Description for Test Product 3",
                "category": "Category 2",
                "price": 30.0,
                "metadata": {"color": "green", "size": "large"},
            },
        ],
        "interactions": [
            {
                "end_user_external_id": "user_1",
                "product_external_id": "product_1",
                "interaction_type": "view",
                "rating": 4.5,
                "metadata": {"timestamp": "2023-01-01T12:00:00Z", "device": "mobile"},
            },
            {
                "end_user_external_id": "user_1",
                "product_external_id": "product_2",
                "interaction_type": "purchase",
                "rating": 5.0,
                "metadata": {"timestamp": "2023-01-02T12:00:00Z", "device": "desktop"},
            },
            {
                "end_user_external_id": "user_2",
                "product_external_id": "product_1",
                "interaction_type": "like",
                "rating": 3.5,
                "metadata": {"timestamp": "2023-01-03T12:00:00Z", "device": "tablet"},
            },
        ],
    }

    # Call the batch data ingestion endpoint
    response = await async_client.post("/api/v1/data/batch", json=initial_batch_data)

    # Check response
    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "processing"

    # Execute the mock Celery task for data ingestion
    await execute_mock_tasks(mock_celery_tasks, db_session)

    # 2. Train initial model
    training_data = {
        "model_type": "collaborative",
        "parameters": {"factors": 10, "iterations": 5, "regularization": 0.1},
    }

    # Call the training endpoint
    response = await async_client.post("/api/v1/pipeline/train", json=training_data)

    # Check response
    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "processing"

    # Execute the mock Celery task for training
    await execute_mock_tasks(mock_celery_tasks, db_session)

    # 3. Get initial recommendations
    # Mock the recommendation service to return predictable recommendations
    with patch(
        "src.api.v1.endpoints.recommendations.get_recommendation_service"
    ) as mock_get_service:
        mock_service = AsyncMock()

        # Initial recommendations
        mock_service.get_recommendations.return_value = [
            {"product_id": 3, "score": 0.85}  # product_3
        ]
        mock_get_service.return_value = mock_service

        # Call the recommendations endpoint
        response = await async_client.get("/api/v1/recommendations/user/user_1?limit=3")

        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["product_id"] == 3
        assert data[0]["score"] == 0.85

    # 4. Ingest new batch data with additional interactions
    new_batch_data = {
        "users": [{"external_id": "user_3", "metadata": {"age": 35, "gender": "male"}}],
        "products": [
            {
                "external_id": "product_4",
                "name": "Test Product 4",
                "description": "Description for Test Product 4",
                "category": "Category 2",
                "price": 40.0,
                "metadata": {"color": "yellow", "size": "small"},
            }
        ],
        "interactions": [
            {
                "end_user_external_id": "user_1",
                "product_external_id": "product_3",
                "interaction_type": "purchase",
                "rating": 5.0,
                "metadata": {"timestamp": "2023-01-04T12:00:00Z", "device": "mobile"},
            },
            {
                "end_user_external_id": "user_2",
                "product_external_id": "product_3",
                "interaction_type": "view",
                "rating": 4.0,
                "metadata": {"timestamp": "2023-01-05T12:00:00Z", "device": "desktop"},
            },
            {
                "end_user_external_id": "user_3",
                "product_external_id": "product_1",
                "interaction_type": "like",
                "rating": 3.5,
                "metadata": {"timestamp": "2023-01-06T12:00:00Z", "device": "tablet"},
            },
            {
                "end_user_external_id": "user_3",
                "product_external_id": "product_4",
                "interaction_type": "purchase",
                "rating": 4.5,
                "metadata": {"timestamp": "2023-01-07T12:00:00Z", "device": "mobile"},
            },
        ],
    }

    # Call the batch data ingestion endpoint
    response = await async_client.post("/api/v1/data/batch", json=new_batch_data)

    # Check response
    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "processing"

    # Execute the mock Celery task for data ingestion
    await execute_mock_tasks(mock_celery_tasks, db_session)

    # 5. Retrain the model
    # Call the training endpoint again
    response = await async_client.post("/api/v1/pipeline/train", json=training_data)

    # Check response
    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "processing"

    # Execute the mock Celery task for training
    await execute_mock_tasks(mock_celery_tasks, db_session)

    # 6. Get updated recommendations
    # Mock the recommendation service to return updated recommendations
    with patch(
        "src.api.v1.endpoints.recommendations.get_recommendation_service"
    ) as mock_get_service:
        mock_service = AsyncMock()

        # Updated recommendations after retraining
        mock_service.get_recommendations.return_value = [
            {"product_id": 4, "score": 0.95}  # product_4
        ]
        mock_get_service.return_value = mock_service

        # Call the recommendations endpoint again
        response = await async_client.get(
            "/api/v1/recommendations/user/user_1?limit=3&exclude_viewed=true"
        )

        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["product_id"] == 4
        assert data[0]["score"] == 0.95
        assert data[0]["product"]["external_id"] == "product_4"
