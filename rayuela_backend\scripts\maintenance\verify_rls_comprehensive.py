#!/usr/bin/env python3
"""
Script comprehensivo para verificar que todas las tablas relevantes tengan políticas RLS activadas.
Diseñado para ejecutarse en CI/CD y validar todas las operaciones (SELECT, INSERT, UPDATE, DELETE).

Este script debe ejecutarse con acceso de superusuario a la base de datos.
Ejemplo de uso:
    python -m scripts.verify_rls_comprehensive
"""

import asyncio
import logging
import json
import sys
from typing import List, Dict, Any, Tuple, Optional
from sqlalchemy import text
from datetime import datetime

from src.db.session import get_db
from src.core.config import settings

# Configurar logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Lista comprehensiva de tablas que DEBEN tener RLS habilitado
TENANT_SCOPED_TABLES = [
    # Tablas de datos principales
    "products",
    "end_users",
    "interactions",
    "searches",
    "recommendations",
    # Tablas de ML y entrenamiento
    "artifact_metadata",  # ModelMetadata.__tablename__
    "model_metrics",
    "training_jobs",
    "batch_ingestion_jobs",
    "training_metrics",
    # Tablas de sistema y auditoría
    "system_users",
    "system_user_roles",
    "audit_logs",
    "notifications",
    # Tablas de facturación y uso
    "account_usage_metrics",
    "subscriptions",
    "endpoint_metrics",
]

# Tablas globales que NO deberían tener RLS (para referencia)
GLOBAL_TABLES = [
    "accounts",
    "roles",
    "permissions",
    "role_permissions",
]

# Lista de operaciones que deben estar cubiertas por políticas RLS
REQUIRED_OPERATIONS = ["SELECT", "INSERT", "UPDATE", "DELETE"]

# Política esperadas por operación
EXPECTED_POLICY_PATTERNS = {
    "SELECT": ["tenant_select_policy", "account_select_policy"],
    "INSERT": ["tenant_insert_policy", "account_insert_policy"],
    "UPDATE": ["tenant_update_policy", "account_update_policy"],
    "DELETE": ["tenant_delete_policy", "account_delete_policy"],
}


class RLSVerificationResult:
    """Resultado de verificación RLS."""

    def __init__(self):
        self.timestamp = datetime.now().isoformat()
        self.total_tables_checked = 0
        self.tables_with_rls = 0
        self.tables_missing_rls = []
        self.policy_coverage = {}
        self.security_issues = []
        self.warnings = []
        self.recommendations = []
        self.success = True

    def to_dict(self) -> Dict[str, Any]:
        """Convertir resultado a diccionario."""
        return {
            "timestamp": self.timestamp,
            "summary": {
                "total_tables_checked": self.total_tables_checked,
                "tables_with_rls": self.tables_with_rls,
                "tables_missing_rls": len(self.tables_missing_rls),
                "success": self.success,
            },
            "tables_missing_rls": self.tables_missing_rls,
            "policy_coverage": self.policy_coverage,
            "security_issues": self.security_issues,
            "warnings": self.warnings,
            "recommendations": self.recommendations,
        }


async def check_table_exists(table_name: str) -> bool:
    """Verificar si una tabla existe."""
    async with get_db() as db:
        try:
            result = await db.execute(
                text(
                    """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = :table_name
                    )
                """
                ),
                {"table_name": table_name},
            )
            return result.scalar()
        except Exception as e:
            logger.error(f"Error verificando existencia de tabla {table_name}: {e}")
            return False


async def get_tables_with_rls() -> Dict[str, bool]:
    """Obtener estado RLS de todas las tablas relevantes."""
    rls_status = {}

    async with get_db() as db:
        for table in TENANT_SCOPED_TABLES:
            try:
                # Verificar si la tabla existe primero
                if not await check_table_exists(table):
                    logger.warning(f"Tabla {table} no existe en la base de datos")
                    rls_status[table] = False
                    continue

                # Verificar si RLS está habilitado
                result = await db.execute(
                    text(
                        """
                        SELECT c.relrowsecurity
                        FROM pg_catalog.pg_class c
                        JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                        WHERE c.relname = :table_name
                        AND n.nspname = 'public'
                    """
                    ),
                    {"table_name": table},
                )

                row = result.fetchone()
                rls_status[table] = bool(row[0]) if row else False

            except Exception as e:
                logger.error(f"Error verificando RLS para tabla {table}: {e}")
                rls_status[table] = False

    return rls_status


async def get_table_policies(table_name: str) -> List[Dict[str, Any]]:
    """Obtener todas las políticas RLS para una tabla específica."""
    policies = []

    async with get_db() as db:
        try:
            result = await db.execute(
                text(
                    """
                    SELECT 
                        p.polname,
                        p.polcmd,
                        p.polpermissive,
                        p.polroles::text,
                        pg_catalog.pg_get_expr(p.polqual, p.polrelid) AS using_expr,
                        pg_catalog.pg_get_expr(p.polwithcheck, p.polrelid) AS check_expr
                    FROM pg_policy p
                    JOIN pg_class c ON p.polrelid = c.oid
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE c.relname = :table_name
                    AND n.nspname = 'public'
                    ORDER BY p.polname
                """
                ),
                {"table_name": table_name},
            )

            for row in result.fetchall():
                policies.append(
                    {
                        "name": row[0],
                        "operation": row[1],
                        "permissive": row[2],
                        "roles": row[3],
                        "using_expr": row[4],
                        "check_expr": row[5],
                    }
                )

        except Exception as e:
            logger.error(f"Error obteniendo políticas para tabla {table_name}: {e}")

    return policies


async def verify_policy_tenant_filter(policy: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """Verificar que una política use filtro de tenant apropiado."""
    issues = []

    using_expr = policy.get("using_expr", "") or ""
    check_expr = policy.get("check_expr", "") or ""

    # Patrones aceptables para filtro de tenant
    tenant_patterns = [
        "account_id",
        "app.tenant_id",
        "current_setting('app.tenant_id')",
        "session_user",
    ]

    # Verificar expresión USING
    has_tenant_filter = any(
        pattern in using_expr.lower() for pattern in tenant_patterns
    )

    # Verificar expresión CHECK si existe
    if check_expr:
        has_tenant_check = any(
            pattern in check_expr.lower() for pattern in tenant_patterns
        )
        if not has_tenant_check:
            issues.append(
                f"Política {policy['name']}: expresión CHECK no usa filtro de tenant"
            )

    if not has_tenant_filter:
        issues.append(
            f"Política {policy['name']}: expresión USING no usa filtro de tenant"
        )

    # Verificar que no sea demasiado permisiva
    if "true" in using_expr.lower() or using_expr.strip() == "":
        issues.append(
            f"Política {policy['name']}: política demasiado permisiva (permite todo)"
        )

    return len(issues) == 0, issues


async def verify_table_policy_coverage(table_name: str) -> Dict[str, Any]:
    """Verificar cobertura completa de políticas para una tabla."""
    policies = await get_table_policies(table_name)

    coverage = {
        "table": table_name,
        "total_policies": len(policies),
        "operations_covered": [],
        "operations_missing": [],
        "policy_issues": [],
        "security_score": 0,
    }

    # Verificar cobertura por operación
    covered_operations = set()
    for policy in policies:
        operation = policy["operation"]
        covered_operations.add(operation)

        # Verificar que la política use filtro de tenant apropiado
        is_secure, issues = await verify_policy_tenant_filter(policy)
        if not is_secure:
            coverage["policy_issues"].extend(issues)

    coverage["operations_covered"] = list(covered_operations)
    coverage["operations_missing"] = [
        op for op in REQUIRED_OPERATIONS if op not in covered_operations
    ]

    # Calcular score de seguridad
    operations_score = len(covered_operations) / len(REQUIRED_OPERATIONS) * 100
    policy_quality_score = max(0, 100 - len(coverage["policy_issues"]) * 20)
    coverage["security_score"] = min(100, (operations_score + policy_quality_score) / 2)

    return coverage


async def verify_global_tables_no_rls() -> List[Dict[str, Any]]:
    """Verificar que las tablas globales NO tengan RLS habilitado."""
    issues = []

    for table in GLOBAL_TABLES:
        if not await check_table_exists(table):
            continue

        async with get_db() as db:
            try:
                result = await db.execute(
                    text(
                        """
                        SELECT c.relrowsecurity
                        FROM pg_catalog.pg_class c
                        JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                        WHERE c.relname = :table_name
                        AND n.nspname = 'public'
                    """
                    ),
                    {"table_name": table},
                )

                row = result.fetchone()
                if row and row[0]:  # RLS está habilitado
                    issues.append(
                        {
                            "table": table,
                            "issue": "Tabla global tiene RLS habilitado (podría ser intencional)",
                            "severity": "warning",
                        }
                    )

            except Exception as e:
                logger.error(f"Error verificando tabla global {table}: {e}")

    return issues


async def test_rls_enforcement() -> List[Dict[str, Any]]:
    """Probar que RLS se aplique correctamente con diferentes contextos."""
    test_results = []

    # Solo si estamos en entorno de desarrollo/test
    if settings.ENV not in ["test", "development"]:
        logger.info("Saltando tests de enforcement RLS (no en entorno de test)")
        return test_results

    async with get_db() as db:
        try:
            # Test básico: establecer tenant_id y verificar que se aplique
            await db.execute(text("SET app.tenant_id = '123'"))

            # Intentar consulta en tabla con RLS
            test_table = "products"
            if await check_table_exists(test_table):
                result = await db.execute(text(f"SELECT COUNT(*) FROM {test_table}"))
                count_with_tenant = result.scalar()

                # Limpiar tenant_id y verificar diferencia
                await db.execute(text("SET app.tenant_id = ''"))

                result = await db.execute(text(f"SELECT COUNT(*) FROM {test_table}"))
                count_without_tenant = result.scalar()

                test_results.append(
                    {
                        "test": "basic_rls_enforcement",
                        "table": test_table,
                        "with_tenant_count": count_with_tenant,
                        "without_tenant_count": count_without_tenant,
                        "rls_working": count_with_tenant != count_without_tenant,
                    }
                )

        except Exception as e:
            test_results.append(
                {"test": "basic_rls_enforcement", "error": str(e), "rls_working": False}
            )

    return test_results


async def generate_recommendations(result: RLSVerificationResult) -> List[str]:
    """Generar recomendaciones basadas en los resultados."""
    recommendations = []

    if result.tables_missing_rls:
        recommendations.append(
            f"URGENTE: Habilitar RLS en {len(result.tables_missing_rls)} tablas: {', '.join(result.tables_missing_rls)}"
        )

    for table, coverage in result.policy_coverage.items():
        if coverage["operations_missing"]:
            recommendations.append(
                f"Agregar políticas para operaciones {coverage['operations_missing']} en tabla {table}"
            )

        if coverage["security_score"] < 80:
            recommendations.append(
                f"Mejorar políticas de seguridad para tabla {table} (score: {coverage['security_score']:.1f}%)"
            )

    if result.security_issues:
        recommendations.append(
            f"Corregir {len(result.security_issues)} problemas de seguridad identificados"
        )

    # Recomendaciones generales
    recommendations.extend(
        [
            "Ejecutar este script en CI/CD para validación continua",
            "Configurar alertas automáticas para cambios en políticas RLS",
            "Revisar y actualizar políticas RLS después de cada migración",
        ]
    )

    return recommendations


async def main():
    """Función principal de verificación RLS comprehensiva."""
    logger.info("🔒 Iniciando verificación comprehensiva de Row-Level Security...")

    result = RLSVerificationResult()

    try:
        # 1. Verificar estado RLS de tablas tenant-scoped
        logger.info("📋 Verificando estado RLS de tablas tenant-scoped...")
        rls_status = await get_tables_with_rls()

        result.total_tables_checked = len(TENANT_SCOPED_TABLES)
        result.tables_with_rls = sum(1 for enabled in rls_status.values() if enabled)
        result.tables_missing_rls = [
            table for table, enabled in rls_status.items() if not enabled
        ]

        logger.info(
            f"✅ Tablas con RLS: {result.tables_with_rls}/{result.total_tables_checked}"
        )

        if result.tables_missing_rls:
            logger.error(f"❌ Tablas SIN RLS: {', '.join(result.tables_missing_rls)}")
            result.success = False

        # 2. Verificar cobertura de políticas para cada tabla
        logger.info("🔍 Verificando cobertura de políticas...")
        for table in TENANT_SCOPED_TABLES:
            if rls_status.get(table, False):
                coverage = await verify_table_policy_coverage(table)
                result.policy_coverage[table] = coverage

                if coverage["operations_missing"]:
                    result.warnings.append(
                        f"Tabla {table} falta políticas para: {coverage['operations_missing']}"
                    )

                if coverage["policy_issues"]:
                    result.security_issues.extend(coverage["policy_issues"])
                    result.success = False

        # 3. Verificar que tablas globales no tengan RLS inapropiado
        logger.info("🌐 Verificando tablas globales...")
        global_issues = await verify_global_tables_no_rls()
        result.warnings.extend([issue["issue"] for issue in global_issues])

        # 4. Probar enforcement de RLS (solo en test/dev)
        logger.info("🧪 Probando enforcement de RLS...")
        enforcement_tests = await test_rls_enforcement()

        failed_tests = [
            test for test in enforcement_tests if not test.get("rls_working", False)
        ]
        if failed_tests:
            result.security_issues.append("RLS enforcement tests fallaron")
            result.success = False

        # 5. Generar recomendaciones
        result.recommendations = await generate_recommendations(result)

        # 6. Mostrar resumen
        logger.info("\n" + "=" * 80)
        logger.info("📊 RESUMEN DE VERIFICACIÓN RLS")
        logger.info("=" * 80)

        status_emoji = "✅" if result.success else "❌"
        logger.info(
            f"{status_emoji} Estado general: {'EXITOSO' if result.success else 'FALLIDO'}"
        )
        logger.info(f"📋 Tablas verificadas: {result.total_tables_checked}")
        logger.info(f"🔒 Tablas con RLS: {result.tables_with_rls}")
        logger.info(f"⚠️  Problemas de seguridad: {len(result.security_issues)}")
        logger.info(f"📝 Advertencias: {len(result.warnings)}")

        # Mostrar detalles de problemas críticos
        if result.security_issues:
            logger.error("\n🚨 PROBLEMAS CRÍTICOS DE SEGURIDAD:")
            for issue in result.security_issues[:5]:  # Mostrar solo los primeros 5
                logger.error(f"  • {issue}")

        # Mostrar recomendaciones principales
        if result.recommendations:
            logger.info("\n💡 RECOMENDACIONES PRINCIPALES:")
            for rec in result.recommendations[:3]:  # Mostrar solo las primeras 3
                logger.info(f"  • {rec}")

        # 7. Guardar resultados
        output_file = "rls_verification_results.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(result.to_dict(), f, indent=2, ensure_ascii=False)

        logger.info(f"\n💾 Resultados guardados en: {output_file}")

        # 8. Exit code para CI/CD
        if result.success:
            logger.info("🎉 ¡Verificación RLS completada exitosamente!")
            sys.exit(0)
        else:
            logger.error(
                "💥 Verificación RLS falló. Revisar problemas antes de desplegar."
            )
            sys.exit(1)

    except Exception as e:
        logger.error(f"❌ Error durante verificación RLS: {e}")
        result.success = False
        result.security_issues.append(f"Error en verificación: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
