"""
Tests for explicit transaction implementation in UsageMetricsRepository.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone
from sqlalchemy.exc import SQLAlchemyError

from src.db.repositories.usage_metrics import UsageMetricsRepository
from src.db.models import AccountUsageMetrics


@pytest.fixture
def mock_db():
    """Mock database session with transaction support."""
    db = AsyncMock()
    
    # Mock the begin() context manager
    transaction_mock = AsyncMock()
    transaction_mock.__aenter__ = AsyncMock(return_value=transaction_mock)
    transaction_mock.__aexit__ = AsyncMock(return_value=None)
    db.begin.return_value = transaction_mock
    
    # Mock other database operations
    db.execute = AsyncMock()
    db.add = MagicMock()
    db.flush = AsyncMock()
    db.refresh = AsyncMock()
    
    return db


class TestUsageMetricsRepositoryTransactions:
    """Test explicit transactions in UsageMetricsRepository."""

    @pytest.mark.asyncio
    async def test_create_or_update_account_metrics_uses_transaction_for_update(self, mock_db):
        """Test that updating existing metrics uses explicit transaction."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock existing metrics
        existing_metrics = MagicMock()
        repo.get_account_metrics = AsyncMock(return_value=existing_metrics)
        
        # Mock query result for refresh
        mock_result = MagicMock()
        mock_result.scalar_one.return_value = existing_metrics
        mock_db.execute.return_value = mock_result
        
        # Act
        result = await repo.create_or_update_account_metrics(
            account_id=1,
            api_calls_count=100,
            storage_used=500
        )
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.execute.assert_called()
        assert result == existing_metrics

    @pytest.mark.asyncio
    async def test_create_or_update_account_metrics_uses_transaction_for_create(self, mock_db):
        """Test that creating new metrics uses explicit transaction."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock no existing metrics
        repo.get_account_metrics = AsyncMock(return_value=None)
        
        # Mock created metrics
        created_metrics = MagicMock()
        
        # Act
        result = await repo.create_or_update_account_metrics(
            account_id=1,
            api_calls_count=100,
            storage_used=500
        )
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.add.assert_called_once()
        mock_db.flush.assert_called_once()
        mock_db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_increment_api_calls_uses_transaction_for_update(self, mock_db):
        """Test that incrementing API calls for existing metrics uses explicit transaction."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock existing metrics
        existing_metrics = MagicMock()
        repo.get_account_metrics = AsyncMock(return_value=existing_metrics)
        
        # Mock query result for refresh
        mock_result = MagicMock()
        mock_result.scalar_one.return_value = existing_metrics
        mock_db.execute.return_value = mock_result
        
        # Act
        result = await repo.increment_api_calls(account_id=1, count=5)
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.execute.assert_called()
        assert result == existing_metrics

    @pytest.mark.asyncio
    async def test_increment_api_calls_uses_transaction_for_create(self, mock_db):
        """Test that incrementing API calls for new metrics uses explicit transaction."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock no existing metrics
        repo.get_account_metrics = AsyncMock(return_value=None)
        
        # Act
        result = await repo.increment_api_calls(account_id=1, count=5)
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.add.assert_called_once()
        mock_db.flush.assert_called_once()
        mock_db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_reset_billing_period_metrics_uses_transaction_for_update(self, mock_db):
        """Test that resetting billing period for existing metrics uses explicit transaction."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock existing metrics
        existing_metrics = MagicMock()
        repo.get_account_metrics = AsyncMock(return_value=existing_metrics)
        
        # Mock query result for refresh
        mock_result = MagicMock()
        mock_result.scalar_one.return_value = existing_metrics
        mock_db.execute.return_value = mock_result
        
        # Act
        result = await repo.reset_billing_period_metrics(account_id=1)
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.execute.assert_called()
        assert result == existing_metrics

    @pytest.mark.asyncio
    async def test_reset_billing_period_metrics_uses_transaction_for_create(self, mock_db):
        """Test that resetting billing period for new metrics uses explicit transaction."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock no existing metrics
        repo.get_account_metrics = AsyncMock(return_value=None)
        
        # Act
        result = await repo.reset_billing_period_metrics(account_id=1)
        
        # Assert
        mock_db.begin.assert_called_once()
        mock_db.add.assert_called_once()
        mock_db.flush.assert_called_once()
        mock_db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_transaction_error_handling(self, mock_db):
        """Test that transaction errors are properly handled."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock transaction to raise an error
        transaction_mock = AsyncMock()
        transaction_mock.__aenter__ = AsyncMock(side_effect=SQLAlchemyError("Test error"))
        mock_db.begin.return_value = transaction_mock
        
        # Mock existing metrics
        existing_metrics = MagicMock()
        repo.get_account_metrics = AsyncMock(return_value=existing_metrics)
        
        # Mock _handle_error method
        repo._handle_error = AsyncMock()
        
        # Act
        await repo.create_or_update_account_metrics(
            account_id=1,
            api_calls_count=100
        )
        
        # Assert
        mock_db.begin.assert_called_once()
        repo._handle_error.assert_called_once_with(
            "creating or updating account metrics", 
            mock_db.begin.return_value.__aenter__.side_effect
        )

    @pytest.mark.asyncio
    async def test_no_explicit_commit_calls(self, mock_db):
        """Test that explicit commit calls are not made (handled by transaction)."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock existing metrics
        existing_metrics = MagicMock()
        repo.get_account_metrics = AsyncMock(return_value=existing_metrics)
        
        # Mock query result for refresh
        mock_result = MagicMock()
        mock_result.scalar_one.return_value = existing_metrics
        mock_db.execute.return_value = mock_result
        
        # Act
        await repo.create_or_update_account_metrics(
            account_id=1,
            api_calls_count=100
        )
        
        # Assert
        mock_db.begin.assert_called_once()
        # Verify that commit is not called explicitly
        mock_db.commit.assert_not_called()

    @pytest.mark.asyncio
    async def test_no_explicit_rollback_calls(self, mock_db):
        """Test that explicit rollback calls are not made (handled by transaction)."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock existing metrics
        existing_metrics = MagicMock()
        repo.get_account_metrics = AsyncMock(return_value=existing_metrics)
        
        # Mock query result for refresh
        mock_result = MagicMock()
        mock_result.scalar_one.return_value = existing_metrics
        mock_db.execute.return_value = mock_result
        
        # Act
        await repo.increment_api_calls(account_id=1, count=5)
        
        # Assert
        mock_db.begin.assert_called_once()
        # Verify that rollback is not called explicitly
        mock_db.rollback.assert_not_called()

    @pytest.mark.asyncio
    async def test_transaction_context_manager_usage(self, mock_db):
        """Test that transaction context manager is properly used."""
        # Arrange
        repo = UsageMetricsRepository(mock_db)
        
        # Mock no existing metrics
        repo.get_account_metrics = AsyncMock(return_value=None)
        
        # Track context manager calls
        transaction_mock = AsyncMock()
        enter_mock = AsyncMock(return_value=transaction_mock)
        exit_mock = AsyncMock(return_value=None)
        transaction_mock.__aenter__ = enter_mock
        transaction_mock.__aexit__ = exit_mock
        mock_db.begin.return_value = transaction_mock
        
        # Act
        await repo.create_or_update_account_metrics(
            account_id=1,
            api_calls_count=100
        )
        
        # Assert
        mock_db.begin.assert_called_once()
        enter_mock.assert_called_once()
        exit_mock.assert_called_once()
