#!/bin/bash

# Script para migrar del cliente API manual al cliente generado por Orval
# Implementa la Historia de Usuario: Migrar al Cliente API Generado

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 Migrando al Cliente API Generado${NC}"
echo "=========================================="
echo ""

# Function to check if we're in the right directory
check_directory() {
    if [ ! -d "rayuela_frontend" ]; then
        echo -e "${RED}❌ Error: Execute this script from the project root directory${NC}"
        echo "Expected structure:"
        echo "  project-root/"
        echo "  ├── rayuela_backend/"
        echo "  └── rayuela_frontend/"
        exit 1
    fi
}

# Function to verify generated API client exists
verify_generated_client() {
    echo -e "${BLUE}1. Verificando cliente API generado...${NC}"
    
    cd rayuela_frontend
    
    if [ ! -f "src/lib/generated/rayuelaAPI.ts" ]; then
        echo -e "${RED}❌ Cliente API generado no encontrado${NC}"
        echo "🔄 Ejecutando generación automática..."
        
        # Regenerate OpenAPI spec and client
        npm run fetch-openapi || true
        npm run generate-api || true
        
        # Check again
        if [ ! -f "src/lib/generated/rayuelaAPI.ts" ]; then
            echo -e "${RED}❌ No se pudo generar el cliente API${NC}"
            echo "💡 Ejecute primero: ./scripts/fix-openapi-generation.sh"
            exit 1
        fi
    fi
    
    echo -e "${GREEN}✅ Cliente API generado encontrado${NC}"
    
    # Get file stats
    lines=$(wc -l src/lib/generated/rayuelaAPI.ts | cut -d' ' -f1)
    size=$(du -h src/lib/generated/rayuelaAPI.ts | cut -f1)
    
    echo "📊 Estadísticas del cliente generado:"
    echo "   📄 Líneas: $lines"
    echo "   💾 Tamaño: $size"
    
    cd ..
    echo ""
}

# Function to analyze current usage
analyze_current_usage() {
    echo -e "${BLUE}2. Analizando uso actual del API manual...${NC}"
    
    cd rayuela_frontend
    
    # Find all imports from './api' or '@/lib/api'
    echo "🔍 Archivos que usan el API manual:"
    find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from.*['\"]@/lib/api['\"]" | head -10
    
    echo ""
    echo "🔍 Funciones API más utilizadas:"
    find src -name "*.tsx" -o -name "*.ts" | xargs grep -ho "import.*{[^}]*}" | \
        grep -o "[a-zA-Z_][a-zA-Z0-9_]*" | sort | uniq -c | sort -nr | head -10
    
    cd ..
    echo ""
}

# Function to create migration helper
create_migration_helper() {
    echo -e "${BLUE}3. Creando helper de migración...${NC}"
    
    cd rayuela_frontend
    
    # Create migration helper file
    cat > "src/lib/generated/migration-helper.ts" << 'EOF'
/**
 * Helper functions for migrating from manual API client to generated client
 */

import { Configuration } from './rayuelaAPI';

// API Base URL configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

/**
 * Create configuration for the generated API client
 */
export const createApiConfiguration = (token?: string, apiKey?: string): Configuration => {
  const headers: Record<string, string> = {};
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  if (apiKey) {
    headers['X-API-Key'] = apiKey;
  }
  
  return new Configuration({
    basePath: API_BASE_URL,
    headers
  });
};

/**
 * Convert auth options from the old API format to the new format
 */
export const convertAuthOptions = (token?: string | null, apiKey?: string | null) => {
  return createApiConfiguration(token || undefined, apiKey || undefined);
};

/**
 * Error handling wrapper for API calls
 */
export const handleApiResponse = async <T>(apiCall: Promise<{ data: T }>): Promise<T> => {
  try {
    const response = await apiCall;
    return response.data;
  } catch (error: any) {
    // Transform generated client errors to match our manual API error format
    if (error.response) {
      const apiError = {
        message: error.response.data?.message || error.message,
        status: error.response.status,
        errorCode: error.response.data?.error_code || 'API_ERROR',
        details: error.response.data?.details || null
      };
      throw apiError;
    }
    throw error;
  }
};

/**
 * Helper to extract data from generated client responses
 */
export const extractData = <T>(response: { data: T }): T => response.data;

/**
 * Legacy error class for backward compatibility
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public errorCode: string,
    public details?: any[] | null
  ) {
    super(message);
    this.name = 'ApiError';
  }

  static isApiError(error: unknown): error is ApiError {
    return error instanceof ApiError;
  }
}
EOF
    
    echo -e "${GREEN}✅ Helper de migración creado${NC}"
    cd ..
    echo ""
}

# Function to create new API wrapper
create_new_api_wrapper() {
    echo -e "${BLUE}4. Creando nuevo wrapper API...${NC}"
    
    cd rayuela_frontend
    
    # Create new API wrapper that uses generated client
    cat > "src/lib/api-generated.ts" << 'EOF'
/**
 * New API client using generated code from OpenAPI spec
 * This replaces the manual api.ts file with generated, type-safe functions
 */

// Import generated API functions and types
import {
  DefaultApi,
  AuthApi,
  AccountsApi,
  UsageApi,
  SystemUsersApi,
  ApiKeysApi,
  PlansApi,
  HealthApi,
  // Types
  AuthTokenResponse,
  RegisterResponse,
  UserInfoResponse,
  AccountInfoResponse,
  AccountUsageResponse,
  UsageHistoryPoint,
  ApiHealthStatus,
  CheckoutSessionResponse,
  PlanInfo,
  ApiKeyResponse,
  NewApiKeyResponse,
  ApiKeyListResponse,
  ApiKeyCreate,
  ApiKeyUpdate
} from './generated/rayuelaAPI';

import { 
  createApiConfiguration, 
  handleApiResponse, 
  ApiError 
} from './generated/migration-helper';

// --- Authentication Functions ---

export const loginUser = async (credentials: { email: string; password: string }): Promise<AuthTokenResponse> => {
  const authApi = new AuthApi(createApiConfiguration());
  return handleApiResponse(authApi.authLoginPost(credentials));
};

export const registerUser = async (
  accountName: string, 
  email: string, 
  password: string
): Promise<RegisterResponse> => {
  const authApi = new AuthApi(createApiConfiguration());
  const request = {
    account_name: accountName,
    email,
    password
  };
  return handleApiResponse(authApi.authRegisterPost(request));
};

export const logout = async (token: string): Promise<{ message: string }> => {
  const authApi = new AuthApi(createApiConfiguration(token));
  return handleApiResponse(authApi.authLogoutPost());
};

export const requestEmailVerification = async (token: string): Promise<{ message: string }> => {
  const authApi = new AuthApi(createApiConfiguration(token));
  return handleApiResponse(authApi.authSendVerificationEmailPost());
};

export const verifyEmail = async (token: string): Promise<{ message: string }> => {
  const authApi = new AuthApi(createApiConfiguration());
  return handleApiResponse(authApi.authVerifyEmailGet(token));
};

// --- User Functions ---

export const getMe = async (token: string, apiKey?: string | null): Promise<UserInfoResponse> => {
  const usersApi = new SystemUsersApi(createApiConfiguration(token, apiKey || undefined));
  return handleApiResponse(usersApi.systemUsersMeGet());
};

// --- Account Functions ---

export const createAccount = async (data: { name: string }): Promise<AccountInfoResponse> => {
  const accountsApi = new AccountsApi(createApiConfiguration());
  return handleApiResponse(accountsApi.accountsPost(data));
};

export const getCurrentAccountByApiKey = async (apiKey: string): Promise<AccountInfoResponse> => {
  const accountsApi = new AccountsApi(createApiConfiguration(undefined, apiKey));
  return handleApiResponse(accountsApi.accountsCurrentGet());
};

export const getMyAccount = async (token: string, apiKey?: string): Promise<AccountInfoResponse> => {
  const accountsApi = new AccountsApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(accountsApi.accountsCurrentGet());
};

// --- Usage Functions ---

export const getAccountUsage = async (token: string, apiKey: string): Promise<AccountUsageResponse> => {
  const usageApi = new UsageApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(usageApi.usageSummaryGet());
};

export const getUsageHistory = async (
  token: string,
  apiKey: string,
  startDate?: string,
  endDate?: string
): Promise<UsageHistoryPoint[]> => {
  const usageApi = new UsageApi(createApiConfiguration(token, apiKey));
  const params: any = {};
  if (startDate) params.startDate = startDate;
  if (endDate) params.endDate = endDate;
  
  return handleApiResponse(usageApi.usageHistoryGet(params.startDate, params.endDate));
};

// --- API Key Functions ---

export const listApiKeys = async (token: string, apiKey: string): Promise<ApiKeyListResponse> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysGet());
};

export const createApiKey = async (
  token: string, 
  apiKey: string, 
  data: ApiKeyCreate
): Promise<NewApiKeyResponse> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysPost(data));
};

export const updateApiKey = async (
  token: string, 
  apiKey: string, 
  apiKeyId: number, 
  data: ApiKeyUpdate
): Promise<ApiKeyResponse> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysApiKeyIdPatch(apiKeyId, data));
};

export const revokeApiKey = async (
  token: string, 
  apiKey: string, 
  apiKeyId: number
): Promise<void> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysApiKeyIdDelete(apiKeyId));
};

export const getApiKey = async (token: string, apiKey: string): Promise<ApiKeyResponse> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysCurrentGet());
};

export const revokeAllApiKeys = async (token: string, apiKey: string): Promise<void> => {
  const apiKeysApi = new ApiKeysApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(apiKeysApi.apiKeysDelete());
};

// --- Billing Functions ---

export const createCheckoutSession = async (
  priceId: string,
  token: string,
  apiKey: string
): Promise<CheckoutSessionResponse> => {
  const defaultApi = new DefaultApi(createApiConfiguration(token, apiKey));
  const request = { price_id: priceId };
  return handleApiResponse(defaultApi.subscriptionsCheckoutPost(request));
};

export const createBillingPortalSession = async (
  token: string,
  apiKey: string
): Promise<CheckoutSessionResponse> => {
  const defaultApi = new DefaultApi(createApiConfiguration(token, apiKey));
  return handleApiResponse(defaultApi.subscriptionsPortalPost());
};

// --- Plans Functions ---

export const getAvailablePlans = async (): Promise<Record<string, PlanInfo>> => {
  const plansApi = new PlansApi(createApiConfiguration());
  return handleApiResponse(plansApi.plansGet());
};

// --- Health Functions ---

export const getApiHealth = async (): Promise<ApiHealthStatus> => {
  const healthApi = new HealthApi(createApiConfiguration());
  return handleApiResponse(healthApi.healthGet());
};

// --- Legacy Functions for Backward Compatibility ---

export const createLegacyApiKey = (token: string, apiKey: string): Promise<NewApiKeyResponse> =>
  createApiKey(token, apiKey, { name: "Default API Key" });

// Re-export error class for compatibility
export { ApiError };

// Re-export types for compatibility
export type {
  AuthTokenResponse,
  RegisterResponse,
  UserInfoResponse as UserInfo,
  AccountInfoResponse as AccountInfo,
  AccountUsageResponse as AccountUsage,
  UsageHistoryPoint,
  ApiHealthStatus,
  CheckoutSessionResponse,
  PlanInfo,
  ApiKeyResponse as ApiKey,
  NewApiKeyResponse,
  ApiKeyListResponse,
  ApiKeyCreate,
  ApiKeyUpdate
};
EOF
    
    echo -e "${GREEN}✅ Nuevo wrapper API creado${NC}"
    cd ..
    echo ""
}

# Function to create migration script for components
create_component_migration_script() {
    echo -e "${BLUE}5. Creando script de migración de componentes...${NC}"
    
    cd rayuela_frontend
    
    cat > "scripts/migrate-component-imports.sh" << 'EOF'
#!/bin/bash
# Script to migrate component imports from manual API to generated API

echo "🔄 Migrating component imports..."

# Find all files that import from '@/lib/api'
find src -name "*.tsx" -o -name "*.ts" | while read file; do
    if grep -q "from.*['\"]@/lib/api['\"]" "$file"; then
        echo "📝 Updating: $file"
        
        # Create backup
        cp "$file" "$file.backup"
        
        # Replace import statement
        sed -i "s|from ['\"]@/lib/api['\"]|from '@/lib/api-generated'|g" "$file"
        
        # Note: Manual verification might be needed for complex imports
        echo "   ✅ Import updated (backup created)"
    fi
done

echo "✅ Component migration completed"
echo "⚠️ Please verify the changes and test thoroughly"
echo "💡 Backups were created with .backup extension"
EOF
    
    chmod +x scripts/migrate-component-imports.sh
    
    echo -e "${GREEN}✅ Script de migración de componentes creado${NC}"
    cd ..
    echo ""
}

# Function to run tests
run_tests() {
    echo -e "${BLUE}6. Ejecutando pruebas básicas...${NC}"
    
    cd rayuela_frontend
    
    echo "🧪 Verificando sintaxis TypeScript..."
    if npx tsc --noEmit --skipLibCheck 2>/dev/null; then
        echo -e "${GREEN}✅ TypeScript compila correctamente${NC}"
    else
        echo -e "${YELLOW}⚠️ Errores de TypeScript encontrados (revisar manualmente)${NC}"
    fi
    
    echo ""
    echo "📦 Verificando que el cliente generado se puede importar..."
    if node -e "
        try {
            require('./src/lib/generated/rayuelaAPI.ts');
            console.log('✅ Cliente generado se puede importar');
        } catch (e) {
            console.log('❌ Error importando cliente generado:', e.message);
            process.exit(1);
        }
    " 2>/dev/null; then
        echo -e "${GREEN}✅ Cliente generado importable${NC}"
    else
        echo -e "${YELLOW}⚠️ Problemas con la importación del cliente generado${NC}"
    fi
    
    cd ..
    echo ""
}

# Function to provide next steps
provide_next_steps() {
    echo -e "${BLUE}7. Próximos pasos${NC}"
    echo "================"
    echo ""
    
    echo -e "${GREEN}🎯 Migración preparada exitosamente${NC}"
    echo ""
    
    echo -e "${YELLOW}📋 Pasos manuales requeridos:${NC}"
    echo ""
    echo "1. 🔍 Revisar el nuevo cliente API:"
    echo "   cd rayuela_frontend"
    echo "   code src/lib/api-generated.ts"
    echo ""
    echo "2. 🔄 Migrar componentes gradualmente:"
    echo "   cd rayuela_frontend"
    echo "   ./scripts/migrate-component-imports.sh"
    echo ""
    echo "3. 🧪 Probar en desarrollo:"
    echo "   npm run dev"
    echo "   # Verificar que las funciones API funcionan correctamente"
    echo ""
    echo "4. 🔄 Una vez verificado, reemplazar completamente:"
    echo "   mv src/lib/api.ts src/lib/api-manual-backup.ts"
    echo "   mv src/lib/api-generated.ts src/lib/api.ts"
    echo ""
    
    echo -e "${BLUE}🔍 Archivos creados/modificados:${NC}"
    echo "   ✅ src/lib/generated/migration-helper.ts"
    echo "   ✅ src/lib/api-generated.ts"
    echo "   ✅ scripts/migrate-component-imports.sh"
    echo ""
    
    echo -e "${GREEN}💡 Beneficios de la migración:${NC}"
    echo "   🛡️ Seguridad de tipos mejorada"
    echo "   🔄 Sincronización automática con el backend"
    echo "   🐛 Menos errores de integración"
    echo "   ⚡ Desarrollo más rápido"
    echo ""
}

# Main execution
echo "🚀 Iniciando migración al cliente API generado..."
echo ""

check_directory
verify_generated_client
analyze_current_usage
create_migration_helper
create_new_api_wrapper
create_component_migration_script
run_tests
provide_next_steps

echo ""
echo "=============================================="
echo -e "${GREEN}🎉 Migración al Cliente API Preparada!${NC}"
echo "=============================================="
echo ""
echo -e "${BLUE}💡 La infraestructura de migración está lista. Sigue los pasos manuales para completar.${NC}" 