#!/bin/bash
# Regenerate OpenAPI specification and client

echo "🔄 Regenerating OpenAPI specification..."

# Start backend temporarily to generate OpenAPI
cd rayuela_backend
python -c "
from main import app
import json

spec = app.openapi()
with open('../rayuela_frontend/src/lib/openapi/openapi.json', 'w') as f:
    json.dump(spec, f, indent=2)

print('✅ OpenAPI specification generated')
"

# Generate API client
cd ../rayuela_frontend
npx orval --config orval.config.ts

echo "✅ API client regenerated"
