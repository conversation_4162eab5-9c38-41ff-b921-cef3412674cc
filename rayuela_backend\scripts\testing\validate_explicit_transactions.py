#!/usr/bin/env python3
"""
Validation script for explicit transaction implementation.
This script validates that all database write operations use explicit transactions.
"""
import os
import re
import sys
from pathlib import Path
from typing import List, Tuple, Dict


def find_files_with_pattern(directory: Path, pattern: str, file_extensions: List[str]) -> List[Tuple[Path, List[Tuple[int, str]]]]:
    """Find files containing a specific pattern."""
    matches = []
    
    for ext in file_extensions:
        for file_path in directory.rglob(f"*.{ext}"):
            if file_path.is_file():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        file_matches = []
                        for line_num, line in enumerate(lines, 1):
                            if re.search(pattern, line):
                                file_matches.append((line_num, line.strip()))
                        
                        if file_matches:
                            matches.append((file_path, file_matches))
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return matches


def validate_transaction_implementation() -> Dict[str, List]:
    """Validate that explicit transactions are properly implemented."""
    backend_dir = Path("rayuela_backend")
    
    if not backend_dir.exists():
        print("Error: rayuela_backend directory not found")
        return {"errors": ["rayuela_backend directory not found"]}
    
    results = {
        "explicit_commits": [],
        "explicit_rollbacks": [],
        "transaction_blocks": [],
        "errors": []
    }
    
    # Find explicit commit calls (should be removed)
    commit_pattern = r'await\s+.*\.commit\(\)'
    commit_matches = find_files_with_pattern(
        backend_dir / "src", 
        commit_pattern, 
        ["py"]
    )
    
    # Find explicit rollback calls (should be removed)
    rollback_pattern = r'await\s+.*\.rollback\(\)'
    rollback_matches = find_files_with_pattern(
        backend_dir / "src", 
        rollback_pattern, 
        ["py"]
    )
    
    # Find transaction blocks (should be present)
    transaction_pattern = r'async\s+with\s+.*\.begin\(\):'
    transaction_matches = find_files_with_pattern(
        backend_dir / "src", 
        transaction_pattern, 
        ["py"]
    )
    
    results["explicit_commits"] = commit_matches
    results["explicit_rollbacks"] = rollback_matches
    results["transaction_blocks"] = transaction_matches
    
    return results


def print_validation_results(results: Dict[str, List]) -> bool:
    """Print validation results and return True if validation passed."""
    print("=" * 80)
    print("EXPLICIT TRANSACTION VALIDATION RESULTS")
    print("=" * 80)
    
    success = True
    
    # Check for explicit commits (should be empty)
    if results["explicit_commits"]:
        print("\n❌ FOUND EXPLICIT COMMIT CALLS (should be removed):")
        success = False
        for file_path, matches in results["explicit_commits"]:
            print(f"\n  📁 {file_path}")
            for line_num, line in matches:
                print(f"    Line {line_num}: {line}")
    else:
        print("\n✅ No explicit commit calls found (good)")
    
    # Check for explicit rollbacks (should be empty)
    if results["explicit_rollbacks"]:
        print("\n❌ FOUND EXPLICIT ROLLBACK CALLS (should be removed):")
        success = False
        for file_path, matches in results["explicit_rollbacks"]:
            print(f"\n  📁 {file_path}")
            for line_num, line in matches:
                print(f"    Line {line_num}: {line}")
    else:
        print("\n✅ No explicit rollback calls found (good)")
    
    # Check for transaction blocks (should be present)
    if results["transaction_blocks"]:
        print("\n✅ FOUND EXPLICIT TRANSACTION BLOCKS:")
        for file_path, matches in results["transaction_blocks"]:
            print(f"\n  📁 {file_path}")
            for line_num, line in matches:
                print(f"    Line {line_num}: {line}")
    else:
        print("\n⚠️  No explicit transaction blocks found")
    
    # Check for errors
    if results["errors"]:
        print("\n❌ VALIDATION ERRORS:")
        success = False
        for error in results["errors"]:
            print(f"  - {error}")
    
    print("\n" + "=" * 80)
    if success:
        print("✅ VALIDATION PASSED: All database write operations use explicit transactions")
    else:
        print("❌ VALIDATION FAILED: Some issues found with transaction implementation")
    print("=" * 80)
    
    return success


def validate_specific_services() -> Dict[str, bool]:
    """Validate specific services that were updated."""
    backend_dir = Path("rayuela_backend")
    services_to_check = [
        "src/services/billing_webhook_service.py",
        "src/services/usage_meter_service.py", 
        "src/services/email_verification_service.py",
        "src/db/repositories/usage_metrics.py"
    ]
    
    results = {}
    
    for service_path in services_to_check:
        full_path = backend_dir / service_path
        if not full_path.exists():
            results[service_path] = False
            continue
            
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for transaction blocks
            has_transactions = bool(re.search(r'async\s+with\s+.*\.begin\(\):', content))
            
            # Check for explicit commits (should not have any)
            has_commits = bool(re.search(r'await\s+.*\.commit\(\)', content))
            
            # Service is valid if it has transactions and no explicit commits
            results[service_path] = has_transactions and not has_commits
            
        except Exception as e:
            print(f"Error checking {service_path}: {e}")
            results[service_path] = False
    
    return results


def main():
    """Main validation function."""
    print("Validating explicit transaction implementation...")
    
    # General validation
    results = validate_transaction_implementation()
    validation_passed = print_validation_results(results)
    
    # Specific service validation
    print("\n" + "=" * 80)
    print("SPECIFIC SERVICE VALIDATION")
    print("=" * 80)
    
    service_results = validate_specific_services()
    all_services_valid = True
    
    for service_path, is_valid in service_results.items():
        status = "✅" if is_valid else "❌"
        print(f"{status} {service_path}")
        if not is_valid:
            all_services_valid = False
    
    print("\n" + "=" * 80)
    if validation_passed and all_services_valid:
        print("🎉 ALL VALIDATIONS PASSED!")
        print("✅ Explicit transactions are properly implemented")
        return 0
    else:
        print("❌ VALIDATION FAILED!")
        print("Some services still need transaction implementation updates")
        return 1


if __name__ == "__main__":
    sys.exit(main())
