# API Key Global Uniqueness Security Fix

## Overview

This document outlines the critical security fix implemented to address **US-SEC-003: Asegurar Unicidad Global del API Key Hash** - a vulnerability where API key hashes were not globally unique, potentially allowing cross-tenant access.

## Problem Addressed

**Critical Security Vulnerability:** The database constraint `UniqueConstraint("account_id", "api_key_hash")` only enforced uniqueness within each account, not globally across the entire system.

### Vulnerability Details

1. **Non-Global Uniqueness**: Two different accounts could theoretically have the same `api_key_hash`
2. **Cross-Tenant Access Risk**: The `AccountRepository.get_by_api_key()` method could return the wrong account
3. **Data Breach Potential**: An attacker could gain unauthorized access to another tenant's data

### Attack Scenario

```
Account A: account_id=1, api_key_hash="abc123..."
Account B: account_id=2, api_key_hash="abc123..."  # Same hash!

When API key is validated:
- get_by_api_key() searches only by hash
- Could return Account A when Account B's key is used
- Results in unauthorized cross-tenant access
```

## Security Fix Implemented

### ✅ **Database Model Update**

**File:** `src/db/models/account.py`

**Before (Vulnerable):**
```python
__table_args__ = (
    UniqueConstraint("account_id", "api_key_hash", name="uq_account_api_key_hash"),
)
```

**After (Secure):**
```python
__table_args__ = (
    UniqueConstraint("api_key_hash", name="uq_api_key_hash_global"),
    # Global uniqueness for API key hashes to prevent cross-tenant access vulnerabilities
)
```

### ✅ **Database Migration**

**File:** `alembic/versions/enforce_global_api_key_uniqueness.py`

The migration includes:

1. **Duplicate Detection**: Identifies existing duplicate hashes across accounts
2. **Automatic Resolution**: Regenerates API keys for accounts with duplicate hashes
3. **Constraint Update**: Replaces account-scoped with global uniqueness constraint
4. **Safety Measures**: Comprehensive error handling and rollback capabilities

**Migration Features:**
- ✅ Detects and resolves existing duplicates before applying constraint
- ✅ Preserves the first account's API key, regenerates for others
- ✅ Adds descriptive comments for future reference
- ✅ Provides detailed logging of all operations

### ✅ **Comprehensive Testing**

**File:** `tests/security/test_api_key_global_uniqueness.py`

Test coverage includes:
- **Constraint Enforcement**: Verifies database prevents duplicate hashes
- **Different Keys Allowed**: Ensures unique keys work correctly
- **NULL Values Handling**: Multiple accounts can have NULL hashes
- **Correct Lookup**: API key lookup returns the right account
- **Invalid Key Handling**: Invalid keys return None
- **Collision Probability**: Statistical verification of hash uniqueness

## Security Benefits

### 🛡️ **Cross-Tenant Protection**
- **Eliminates** the possibility of cross-tenant access via duplicate hashes
- **Guarantees** each API key hash uniquely identifies one account
- **Prevents** unauthorized data access between tenants

### 🔒 **Data Integrity**
- **Ensures** `get_by_api_key()` always returns the correct account
- **Maintains** tenant isolation at the database level
- **Protects** against accidental or malicious cross-tenant access

### 📊 **Operational Security**
- **Database-level enforcement** prevents application-level bypasses
- **Automatic duplicate resolution** during migration
- **Comprehensive logging** for security auditing

## Implementation Details

### **Uniqueness Enforcement**

The new constraint ensures:
```sql
-- Global uniqueness constraint
ALTER TABLE accounts ADD CONSTRAINT uq_api_key_hash_global 
UNIQUE (api_key_hash);
```

### **Migration Safety**

The migration process:
1. **Scans** for existing duplicate hashes
2. **Reports** any duplicates found
3. **Regenerates** API keys for duplicate accounts (except the first)
4. **Applies** the new global uniqueness constraint
5. **Logs** all operations for audit trail

### **Hash Collision Probability**

With SHA-256 hashes:
- **Hash space**: 2^256 possible values
- **Collision probability**: Negligible for practical purposes
- **Birthday paradox**: Would require ~2^128 keys for 50% collision chance
- **Real-world safety**: Effectively impossible with current technology

## Testing and Verification

### **Database Constraint Testing**

```python
# This should raise IntegrityError
account1 = create_account(api_key_hash="same_hash")
account2 = create_account(api_key_hash="same_hash")  # ❌ Fails
```

### **API Key Lookup Testing**

```python
# Each API key should return exactly one account
account = get_by_api_key("ray_unique_key_123")
assert account.account_id == expected_account_id
```

### **Migration Testing**

The migration includes built-in verification:
- Detects pre-existing duplicates
- Resolves conflicts automatically
- Verifies constraint application
- Provides detailed operation logs

## Compliance and Standards

### ✅ **Security Standards Met**

- **OWASP Top 10** - Addresses A01:2021 Broken Access Control
- **CWE-284** - Improper Access Control mitigation
- **NIST SP 800-63B** - Authentication security guidelines

### ✅ **Best Practices Implemented**

- **Database-level constraints** for security enforcement
- **Comprehensive testing** for all edge cases
- **Safe migration procedures** with automatic conflict resolution
- **Detailed documentation** for future maintenance

## Monitoring and Maintenance

### **Security Monitoring**

1. **Constraint violations** - Monitor for any attempts to create duplicate hashes
2. **API key generation** - Ensure new keys maintain uniqueness
3. **Migration logs** - Review migration results for any issues

### **Code Review Guidelines**

When reviewing API key related code:
- ✅ Verify all API key operations respect global uniqueness
- ✅ Ensure no code bypasses the database constraint
- ✅ Check that error handling properly manages constraint violations
- ✅ Validate that API key generation maintains entropy and uniqueness

### **Future Considerations**

1. **Multiple API keys per account** - If needed, create separate `api_keys` table
2. **API key rotation** - Ensure rotation maintains global uniqueness
3. **Performance monitoring** - Monitor impact of global uniqueness checks
4. **Backup procedures** - Ensure backups maintain constraint integrity

## Migration Execution

### **Pre-Migration Checklist**

- [ ] Backup database before migration
- [ ] Verify no critical operations during migration window
- [ ] Ensure sufficient disk space for constraint creation
- [ ] Review migration script for environment-specific adjustments

### **Migration Command**

```bash
# Apply the migration
alembic upgrade enforce_global_api_key_uniqueness

# Verify constraint was created
psql -c "\d accounts" | grep uq_api_key_hash_global
```

### **Post-Migration Verification**

```bash
# Test constraint enforcement
python -m tests.security.test_api_key_global_uniqueness

# Verify no duplicate hashes exist
psql -c "SELECT api_key_hash, COUNT(*) FROM accounts WHERE api_key_hash IS NOT NULL GROUP BY api_key_hash HAVING COUNT(*) > 1;"
```

---

**Security Impact:** This fix eliminates a critical vulnerability that could have allowed unauthorized cross-tenant access through duplicate API key hashes. The global uniqueness constraint ensures that each API key hash uniquely identifies exactly one account in the entire system, maintaining proper tenant isolation and data security.
