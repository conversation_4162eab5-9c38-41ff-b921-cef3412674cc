// src/lib/api.ts
import { toast } from "sonner"; // Asegúrate que <Toaster /> esté en tu layout

// Tipos de error más específicos
export interface ApiErrorDetails {
  field?: string;
  message: string;
  code?: string;
}

export interface ApiErrorResponse {
  message: string;
  error_code: string;
  details?: ApiErrorDetails[];
  status_code: number;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public errorCode: string,
    public details?: ApiErrorDetails[] | null
  ) {
    super(message);
    this.name = 'ApiError';
  }

  static isApiError(error: unknown): error is ApiError {
    return error instanceof ApiError;
  }

  static fromResponse(response: ApiErrorResponse): ApiError {
    return new ApiError(
      response.message,
      response.status_code,
      response.error_code,
      response.details
    );
  }
}

// Obtén la URL base de las variables de entorno (¡IMPORTANTE!)
// Asegúrate de tener NEXT_PUBLIC_API_URL en tu .env.local o variables de entorno del despliegue
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

console.log("API Base URL:", API_BASE_URL); // Para depuración

interface FetchOptions extends RequestInit {
  token?: string | null; // Para autenticación JWT (SystemUser)
  apiKey?: string | null; // Para autenticación de Cuenta (X-API-Key header)
}

// --- Core Fetch Function ---

/**
 * Función centralizada para realizar llamadas a la API del backend.
 * Maneja la autenticación (JWT o API Key), headers y errores básicos.
 * @param endpoint El path del endpoint (ej. '/users/me')
 * @param options Opciones de Fetch API, incluyendo 'token' o 'apiKey' para autenticación.
 * @returns La respuesta JSON parseada.
 * @throws ApiError si la respuesta no es OK o hay un error de red/parseo.
 */
export async function fetchApi<T>(
  endpoint: string,
  options: FetchOptions = {}
): Promise<T> {
  const { token, apiKey, ...fetchOptions } = options;
  const url = `${API_BASE_URL}${endpoint}`;
  const headers = new Headers(fetchOptions.headers || {});

  // Añadir Content-Type si hay body y no está ya (por defecto JSON)
  if (fetchOptions.body && !headers.has('Content-Type') && !(fetchOptions.body instanceof URLSearchParams)) {
    headers.append('Content-Type', 'application/json');
  }

  // Añadir cabeceras de autenticación
  if (token) {
    headers.append('Authorization', `Bearer ${token}`);
  }

  if (apiKey && !endpoint.endsWith('/auth/token')) {
    headers.append('X-API-Key', apiKey);
  }

  console.log(`Fetching: ${options.method || 'GET'} ${url}`); // Log para depuración

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
    });

    // Manejo de errores HTTP
    if (!response.ok) {
      let errorResponse: ApiErrorResponse;

      try {
        errorResponse = await response.json();
      } catch (e) {
        // Si no se puede parsear el JSON, crear un error básico
        errorResponse = {
          message: `HTTP error ${response.status}: ${response.statusText}`,
          error_code: 'NETWORK_ERROR',
          status_code: response.status
        };
      }

      throw ApiError.fromResponse(errorResponse);
    }

    // Manejar respuestas sin contenido (ej. 204 No Content)
    if (response.status === 204) {
      return undefined as T;
    }

    // Parsear JSON si hay contenido
    const data = await response.json();
    return data as T;

  } catch (error: unknown) {
    // Si ya es un ApiError, relanzarlo directamente
    if (ApiError.isApiError(error)) {
      throw error;
    }

    // Si es otro tipo de error, convertirlo a ApiError
    if (error instanceof Error) {
      throw new ApiError(
        error.message,
        0, // 0 para errores de red
        'NETWORK_ERROR',
        null
      );
    }

    // Para errores desconocidos
    throw new ApiError(
      'Error desconocido al realizar la petición',
      0,
      'UNKNOWN_ERROR',
      null
    );
  }
}

// --- Tipos de Datos Esperados de la API (Ejemplos) ---
// Es BUENA PRÁCTICA definir interfaces/types para las respuestas esperadas

interface AuthTokenResponse {
  access_token: string;
  token_type: string;
}

export interface AccountInfo {
  account_id: number;
  name: string;
  api_key_prefix: string | null;
  api_key_last_chars: string | null;
  api_key_created_at: string | null; // Fecha como string ISO
  api_key_revealed: boolean;
  mercadopago_customer_id: string | null;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  deleted_at: string | null;
  // Información básica de la suscripción (coincide con SubscriptionBasicInfo del backend)
  subscription?: {
    plan: string;
    is_active: boolean;
    expires_at: string | null;
  };
  // Puede incluir la API Key completa SÓLO al regenerar
  api_key?: string;
}

export interface AccountUsage {
  subscription: {
    plan: string;
    is_active: boolean;
    expires_at: string | null;
  };
  api_calls: {
    used: number;
    limit: number;
    percentage: number;
    reset_date: string | null;
    next_reset: string | null;
  };
  storage: {
    used_bytes: number;
    used_mb: number;
    used_gb: number;
    limit_bytes: number;
    limit_mb: number;
    limit_gb: number;
    percentage: number;
    last_measured: string;
    source: string;
  };
  training: {
    frequency_limit: string;
    last_training_date: string | null;
    next_training_available: string | null;
    can_train_now: boolean;
    training_data_limit: number;
    training_data_limit_formatted: string;
  };
  features: {
    available_models: string[];
    max_items: number;
    max_users: number;
    max_requests_per_minute: number;
    max_concurrent_requests: number;
  };
  billing: {
    mercadopago_customer_id: string | null;
    has_payment_method: boolean;
    portal_url: string;
    checkout_url: string;
    upgrade_available: boolean;
  };
  available_plans: Record<string, any>;
}

export interface UsageHistoryPoint {
  date: string;
  api_calls: number;
  storage: number;
}

export interface ApiHealthStatus {
  status: string;
  message: string;
  version?: string;
}

interface UserInfo {
  id: number;
  email: string;
  account_id: number;
  is_admin: boolean;
  is_active: boolean;
  // ...otros campos como roles
}

export interface CheckoutSessionResponse {
  url: string;
  session_id?: string;
}

export interface PlanInfo {
  id: string;
  name: string;
  description: string;
  price: string;
  features: string[];
  limits: {
    api_calls: number;
    storage_bytes: number;
    storage_mb: number;
    storage_gb: number;
    max_requests_per_minute: number;
    max_concurrent_requests: number;
    training_frequency: string;
    training_data_limit: number;
    training_data_limit_formatted: string;
    max_items: number;
    max_items_formatted: string;
    max_users: number;
    max_users_formatted: string;
    recommendation_cache_ttl: number;
    available_models: string[];
  };
  mercadopago_price_id?: string;
  stripe_price_id?: string; // Legacy field for backward compatibility
  contact_required?: boolean;
  recommended?: boolean;
}

// --- Funciones Específicas de la API ---

// Auth
/**
 * Inicia sesión de un SystemUser usando email/password.
 * @param credentials Objeto con 'email' y 'password'.
 * @returns Objeto con el token de acceso JWT.
 */
export const loginUser = (credentials: { email: string; password: string }): Promise<AuthTokenResponse> =>
  fetchApi<AuthTokenResponse>(
    '/auth/token', // Endpoint del backend para obtener el token JWT
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
      // No se pasa 'token' (JWT) aquí
    }
  );

/**
 * Obtiene los datos del usuario del sistema actualmente autenticado (requiere token JWT).
 * @param token El token JWT del usuario.
 * @param apiKey La API Key de la cuenta (opcional, se usará si se proporciona).
 * @returns Información del usuario.
 */
export const getMe = (token: string, apiKey?: string | null): Promise<UserInfo> =>
  fetchApi<UserInfo>('/system-users/me', { token, apiKey }); // Solo requiere JWT, API Key es opcional

// Accounts
/**
 * Crea una nueva cuenta (endpoint público).
 * @param data Datos de la cuenta (nombre, plan inicial).
 * @returns Información de la cuenta creada.
 */
export const createAccount = (data: { name: string }): Promise<AccountInfo> =>
  fetchApi<AccountInfo>('/accounts', { // Endpoint público, sin autenticación explícita aquí
    method: 'POST',
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json' } // Asegurar JSON
  });

// Respuesta para el registro
export interface RegisterResponse {
  access_token: string;
  token_type: string;
  account_id: number;
  is_admin: boolean;
  api_key: string;
  message: string;
}

// Función para el registro
export const registerUser = (accountName: string, email: string, password: string): Promise<RegisterResponse> =>
  fetchApi<RegisterResponse>('/auth/register', {
    method: 'POST',
    body: JSON.stringify({
      accountName,
      email,
      password
    }),
    headers: {
      'Content-Type': 'application/json'
    }
  });

/**
 * Obtiene la información de la cuenta actual (requiere API Key de la cuenta).
 * Útil para obtener datos de la cuenta antes de que un usuario inicie sesión con JWT,
 * o si ciertos endpoints de cuenta solo requieren la API Key.
 * @param apiKey La API Key de la cuenta.
 * @returns Información de la cuenta.
 */
export const getCurrentAccountByApiKey = (apiKey: string): Promise<AccountInfo> =>
  fetchApi<AccountInfo>('/accounts/current', { apiKey }); // Requiere API Key de cuenta

/**
 * Obtiene la información de la cuenta asociada al usuario autenticado (requiere token JWT).
 * Esta función es más segura que getCurrentAccountByApiKey ya que utiliza el token JWT para autenticación.
 * @param token El token JWT del usuario logueado.
 * @param apiKey La API Key de la cuenta (opcional, se usará si se proporciona).
 * @returns Información de la cuenta.
 */
export const getMyAccount = (token: string, apiKey?: string): Promise<AccountInfo> =>
  fetchApi<AccountInfo>('/accounts/current', { token, apiKey }); // Requiere JWT, API Key es opcional

// Usage
/**
 * Obtiene las métricas de uso de la cuenta actual (requiere token JWT y API Key de cuenta).
 * @param token El token JWT del usuario logueado.
 * @param apiKey La API Key de la cuenta.
 * @returns Métricas de uso.
 */
export const getAccountUsage = (token: string, apiKey: string): Promise<AccountUsage> =>
  fetchApi<AccountUsage>('/usage/summary', { token, apiKey }); // Requiere JWT y API Key de cuenta

/**
 * Obtiene el historial de uso para un rango de fechas.
 * @param token El token JWT del usuario logueado.
 * @param apiKey La API Key de la cuenta.
 * @param startDate Fecha de inicio opcional (formato ISO).
 * @param endDate Fecha de fin opcional (formato ISO).
 * @returns Datos históricos de uso.
 */
export const getUsageHistory = (
  token: string,
  apiKey: string,
  startDate?: string,
  endDate?: string
): Promise<UsageHistoryPoint[]> => {
  let url = '/usage/history';
  const params = new URLSearchParams();

  if (startDate) params.append('start_date', startDate);
  if (endDate) params.append('end_date', endDate);

  if (params.toString()) {
    url += `?${params.toString()}`;
  }

  return fetchApi<UsageHistoryPoint[]>(url, { token, apiKey });
};


/**
 * Verifica el estado de salud de la API.
 * @returns Estado de salud de la API.
 */
export const getApiHealth = (): Promise<ApiHealthStatus> =>
  fetchApi<ApiHealthStatus>('/health');

// --- Funciones para Facturación ---

/**
 * Llama al backend para crear una sesión de checkout con Mercado Pago para suscribirse a un plan.
 * @param priceId El ID del precio para el plan seleccionado.
 * @param token El token JWT del usuario logueado.
 * @param apiKey La API Key de la cuenta.
 * @returns Objeto con la URL de la sesión de checkout.
 */
export async function createCheckoutSession(
  priceId: string,
  token: string,
  apiKey: string
): Promise<CheckoutSessionResponse> {
  return fetchApi<CheckoutSessionResponse>('/billing/create-checkout-session', {
    method: 'POST',
    body: JSON.stringify({
      price_id: priceId
    }),
    headers: { 'Content-Type': 'application/json' },
    token: token, // Requiere JWT
    apiKey: apiKey // Requiere API Key de cuenta
  });
}

/**
 * Llama al backend para crear una sesión del Portal de Facturación de Mercado Pago.
 * @param token El token JWT del usuario logueado.
 * @param apiKey La API Key de la cuenta.
 * @returns Objeto con la URL del portal de facturación.
 */
export async function createBillingPortalSession(
  token: string,
  apiKey: string
): Promise<CheckoutSessionResponse> {
  return fetchApi<CheckoutSessionResponse>('/billing/create-portal-session', {
    method: 'POST',
    token: token, // Requiere JWT
    apiKey: apiKey // Requiere API Key de cuenta
  });
}

// Email Verification
/**
 * Solicita un nuevo email de verificación para el usuario actual.
 * @param token El token JWT del usuario logueado.
 * @returns Mensaje de confirmación.
 */
export const requestEmailVerification = (token: string): Promise<{ message: string }> =>
  fetchApi<{ message: string }>('/auth/send-verification-email', {
    method: 'POST',
    token
  });

/**
 * Verifica el email de un usuario usando un token de verificación.
 * @param token El token de verificación recibido por email.
 * @returns Mensaje de confirmación.
 */
export const verifyEmail = (token: string): Promise<{ message: string }> =>
  fetchApi<{ message: string }>(`/auth/verify-email?token=${token}`);

// Plans
/**
 * Obtiene la información de todos los planes disponibles (endpoint público).
 * @returns Información detallada de todos los planes disponibles.
 */
export const getAvailablePlans = (): Promise<Record<string, PlanInfo>> =>
  fetchApi<Record<string, PlanInfo>>('/plans');

// Recommendation Metrics
export * from './api/recommendation-metrics';

// API Keys
export interface ApiKey {
  id: number;
  prefix: string | null;
  last_chars: string | null;
  is_active: boolean;
  created_at: string | null;
  last_used: string | null;
  name?: string | null;
}

export interface NewApiKeyResponse {
  id: number;
  api_key: string;
  name?: string | null;
  prefix: string;
  created_at: string;
  message: string;
}

export interface ApiKeyListResponse {
  api_keys: ApiKey[];
  total: number;
}

export interface ApiKeyCreate {
  name?: string | null;
}

export interface ApiKeyUpdate {
  name?: string | null;
}

// Multi-API Key Management Functions

/**
 * Lista todas las API Keys activas para la cuenta actual.
 * @param token El token JWT del usuario logueado.
 * @param apiKey Una API Key válida de la cuenta.
 * @returns Lista de API Keys con metadatos.
 */
export const listApiKeys = (token: string, apiKey: string): Promise<ApiKeyListResponse> =>
  fetchApi<ApiKeyListResponse>('/api-keys', { token, apiKey });

/**
 * Crea una nueva API Key para la cuenta actual.
 * @param token El token JWT del usuario logueado.
 * @param apiKey Una API Key válida de la cuenta.
 * @param data Datos para crear la API Key (nombre opcional).
 * @returns Objeto con la nueva API Key completa.
 */
export const createApiKey = (token: string, apiKey: string, data: ApiKeyCreate): Promise<NewApiKeyResponse> =>
  fetchApi<NewApiKeyResponse>('/api-keys', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
    token,
    apiKey
  });

/**
 * Actualiza los metadatos de una API Key específica.
 * @param token El token JWT del usuario logueado.
 * @param apiKey Una API Key válida de la cuenta.
 * @param apiKeyId El ID de la API Key a actualizar.
 * @param data Datos a actualizar.
 * @returns La API Key actualizada.
 */
export const updateApiKey = (token: string, apiKey: string, apiKeyId: number, data: ApiKeyUpdate): Promise<ApiKey> =>
  fetchApi<ApiKey>(`/api-keys/${apiKeyId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
    token,
    apiKey
  });

/**
 * Revoca una API Key específica.
 * @param token El token JWT del usuario logueado.
 * @param apiKey Una API Key válida de la cuenta.
 * @param apiKeyId El ID de la API Key a revocar.
 * @returns void
 */
export const revokeApiKey = (token: string, apiKey: string, apiKeyId: number): Promise<void> =>
  fetchApi<void>(`/api-keys/${apiKeyId}`, {
    method: 'DELETE',
    token,
    apiKey
  });

// Legacy Functions for Backward Compatibility

export const getApiKey = (token: string, apiKey: string): Promise<ApiKey> =>
  fetchApi<ApiKey>('/api-keys/current', { token, apiKey });

/**
 * Legacy function - creates a new API Key (now creates multiple keys).
 * @deprecated Use createApiKey with ApiKeyCreate data instead.
 */
export const createLegacyApiKey = (token: string, apiKey: string): Promise<NewApiKeyResponse> =>
  createApiKey(token, apiKey, { name: "Default API Key" });

/**
 * Legacy function - revokes all API Keys.
 * @deprecated Use revokeApiKey with specific API Key ID instead.
 */
export const revokeAllApiKeys = (token: string, apiKey: string): Promise<void> =>
  fetchApi<void>('/api-keys', {
    method: 'DELETE',
    token,
    apiKey
  });

/**
 * Invalida el token JWT actual en el backend.
 * @param token El token JWT a invalidar.
 * @returns Mensaje de confirmación.
 */
export const logout = (token: string): Promise<{ message: string }> =>
  fetchApi<{ message: string }>('/auth/logout', {
    method: 'POST',
    token
  });

// --- Añade más funciones API según necesites ---
// Ejemplos:
// export const getModels = (token: string, apiKey: string) => fetchApi<any[]>('/pipeline/models', { token, apiKey });
// export const triggerTraining = (token: string, apiKey: string, params: any) => fetchApi<any>('/pipeline/train', { method: 'POST', body: JSON.stringify(params), token, apiKey });