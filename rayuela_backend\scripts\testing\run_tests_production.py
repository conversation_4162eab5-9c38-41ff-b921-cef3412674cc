#!/usr/bin/env python3
"""
Script de producción para ejecutar tests de Rayuela.
Diseñado para CI/CD en Google Cloud Platform.
"""

import os
import sys
import subprocess
import json
import time
from typing import Dict, List, Optional
from pathlib import Path


class TestRunner:
    """Ejecutor de tests para producción."""

    def __init__(self):
        self.test_results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {},
            "details": {},
            "coverage": {},
            "recommendations": [],
        }
        self.failed_tests = []

    def run_command(self, command: str, timeout: int = 300) -> tuple[bool, str]:
        """Ejecuta un comando y retorna el resultado."""
        try:
            print(f"🔧 Ejecutando: {command}")
            result = subprocess.run(
                command, shell=True, capture_output=True, text=True, timeout=timeout
            )

            success = result.returncode == 0
            output = result.stdout + result.stderr

            if not success:
                print(f"❌ Comando falló con código {result.returncode}")
                print(f"Output: {output}")

            return success, output

        except subprocess.TimeoutExpired:
            print(f"⏰ Comando expiró después de {timeout} segundos")
            return False, "Command timeout"
        except Exception as e:
            print(f"❌ Error ejecutando comando: {e}")
            return False, str(e)

    def setup_environment(self) -> bool:
        """Configura el entorno para tests."""
        print("🔧 Configurando entorno para tests...")

        # Verificar Docker
        success, _ = self.run_command("docker --version")
        if not success:
            print("❌ Docker no está disponible")
            return False

        # Verificar docker-compose
        success, _ = self.run_command("docker-compose --version")
        if not success:
            print("❌ docker-compose no está disponible")
            return False

        # Limpiar contenedores anteriores
        print("🧹 Limpiando contenedores anteriores...")
        self.run_command("docker-compose -f docker-compose.test.yml down -v")

        return True

    def run_unit_tests(self) -> bool:
        """Ejecuta tests unitarios."""
        print("\n🧪 Ejecutando tests unitarios...")

        success, output = self.run_command(
            "docker-compose -f docker-compose.test.yml run --rm test-runner "
            "pytest tests/unit/ -v --tb=short --junitxml=/app/unit_test_results.xml "
            "--cov=src --cov-report=xml:/app/coverage_unit.xml"
        )

        self.test_results["details"]["unit_tests"] = {
            "success": success,
            "output": output,
        }

        if not success:
            self.failed_tests.append("unit")

        return success

    def run_integration_tests(self) -> bool:
        """Ejecuta tests de integración."""
        print("\n🔗 Ejecutando tests de integración...")

        success, output = self.run_command(
            "docker-compose -f docker-compose.test.yml run --rm test-runner "
            "pytest tests/integration/ -v --tb=short --junitxml=/app/integration_test_results.xml "
            "--cov=src --cov-report=xml:/app/coverage_integration.xml"
        )

        self.test_results["details"]["integration_tests"] = {
            "success": success,
            "output": output,
        }

        if not success:
            self.failed_tests.append("integration")

        return success

    def run_e2e_tests(self) -> bool:
        """Ejecuta tests end-to-end."""
        print("\n🌐 Ejecutando tests end-to-end...")

        success, output = self.run_command(
            "docker-compose -f docker-compose.test.yml run --rm test-runner "
            "pytest tests/e2e/ -v --tb=short --junitxml=/app/e2e_test_results.xml"
        )

        self.test_results["details"]["e2e_tests"] = {
            "success": success,
            "output": output,
        }

        if not success:
            self.failed_tests.append("e2e")

        return success

    def run_security_tests(self) -> bool:
        """Ejecuta tests de seguridad específicos."""
        print("\n🔒 Ejecutando tests de seguridad...")

        success, output = self.run_command(
            "docker-compose -f docker-compose.test.yml run --rm test-runner "
            "pytest tests/unit/core/security/ tests/integration/test_multi_tenancy_security.py "
            "-v --tb=short --junitxml=/app/security_test_results.xml"
        )

        self.test_results["details"]["security_tests"] = {
            "success": success,
            "output": output,
        }

        if not success:
            self.failed_tests.append("security")

        return success

    def extract_coverage_info(self) -> Dict:
        """Extrae información de cobertura de tests."""
        print("\n📊 Extrayendo información de cobertura...")

        coverage_info = {}

        # Intentar obtener reporte de cobertura
        success, output = self.run_command(
            "docker-compose -f docker-compose.test.yml run --rm test-runner "
            "coverage report --format=json"
        )

        if success:
            try:
                coverage_data = json.loads(output)
                coverage_info = {
                    "total_coverage": coverage_data.get("totals", {}).get(
                        "percent_covered", 0
                    ),
                    "lines_covered": coverage_data.get("totals", {}).get(
                        "covered_lines", 0
                    ),
                    "lines_total": coverage_data.get("totals", {}).get(
                        "num_statements", 0
                    ),
                    "files": {},
                }

                for filename, file_data in coverage_data.get("files", {}).items():
                    coverage_info["files"][filename] = {
                        "coverage": file_data.get("summary", {}).get(
                            "percent_covered", 0
                        ),
                        "lines_covered": file_data.get("summary", {}).get(
                            "covered_lines", 0
                        ),
                        "lines_total": file_data.get("summary", {}).get(
                            "num_statements", 0
                        ),
                    }

            except json.JSONDecodeError:
                print("❌ No se pudo parsear el reporte de cobertura")

        self.test_results["coverage"] = coverage_info
        return coverage_info

    def run_load_tests(self) -> bool:
        """Ejecuta tests de carga básicos."""
        print("\n⚡ Ejecutando tests de carga...")

        success, output = self.run_command(
            "docker-compose -f docker-compose.test.yml run --rm test-runner "
            "locust -f tests/load/locustfile.py --headless -u 10 -r 2 -t 30s --host=http://api:8000"
        )

        self.test_results["details"]["load_tests"] = {
            "success": success,
            "output": output,
        }

        if not success:
            self.failed_tests.append("load")

        return success

    def generate_recommendations(self):
        """Genera recomendaciones basadas en los resultados."""
        recommendations = []

        if self.failed_tests:
            recommendations.append(
                {
                    "priority": "HIGH",
                    "category": "Test Failures",
                    "description": f"Tests fallaron en: {', '.join(self.failed_tests)}",
                    "action": "Revisar logs de tests fallidos y corregir problemas",
                }
            )

        coverage = self.test_results.get("coverage", {})
        total_coverage = coverage.get("total_coverage", 0)

        if total_coverage < 80:
            recommendations.append(
                {
                    "priority": "MEDIUM",
                    "category": "Coverage",
                    "description": f"Cobertura de tests es {total_coverage:.1f}%, objetivo: 80%+",
                    "action": "Agregar más tests unitarios y de integración",
                }
            )

        if total_coverage < 60:
            recommendations.append(
                {
                    "priority": "HIGH",
                    "category": "Coverage",
                    "description": "Cobertura de tests críticament baja",
                    "action": "Agregar tests urgentemente antes del despliegue",
                }
            )

        # Recomendar configuración de CI/CD
        recommendations.append(
            {
                "priority": "HIGH",
                "category": "CI/CD",
                "description": "Configurar pipeline de CI/CD en Google Cloud Build",
                "action": "Usar cloudbuild.yaml para automatizar tests en cada commit",
            }
        )

        self.test_results["recommendations"] = recommendations

    def save_results(self, output_file: str = "test_results_production.json"):
        """Guarda los resultados de tests."""
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Resultados guardados en: {output_file}")

    def print_summary(self):
        """Imprime un resumen de los resultados."""
        print("\n" + "=" * 80)
        print("📊 RESUMEN DE TESTS DE PRODUCCIÓN")
        print("=" * 80)

        all_passed = len(self.failed_tests) == 0
        status_emoji = "✅" if all_passed else "❌"

        print(
            f"{status_emoji} Estado general: {'PASARON' if all_passed else 'FALLARON'}"
        )

        if self.failed_tests:
            print(f"❌ Tests fallidos: {', '.join(self.failed_tests)}")

        coverage = self.test_results.get("coverage", {})
        if coverage:
            total_coverage = coverage.get("total_coverage", 0)
            print(f"📊 Cobertura total: {total_coverage:.1f}%")

        print(f"\n🎯 RECOMENDACIONES:")
        for i, rec in enumerate(self.test_results.get("recommendations", []), 1):
            priority_emoji = (
                "🔥"
                if rec["priority"] == "HIGH"
                else "⚡" if rec["priority"] == "MEDIUM" else "💡"
            )
            print(
                f"  {i}. {priority_emoji} [{rec['priority']}] {rec['category']}: {rec['description']}"
            )

    def cleanup(self):
        """Limpia recursos después de los tests."""
        print("\n🧹 Limpiando recursos...")
        self.run_command("docker-compose -f docker-compose.test.yml down -v")

    def run_all_tests(self) -> bool:
        """Ejecuta todos los tipos de tests."""
        try:
            # Configurar entorno
            if not self.setup_environment():
                return False

            # Ejecutar diferentes tipos de tests
            unit_success = self.run_unit_tests()
            integration_success = self.run_integration_tests()
            e2e_success = self.run_e2e_tests()
            security_success = self.run_security_tests()

            # Tests de carga (opcional, no crítico)
            load_success = self.run_load_tests()

            # Extraer información de cobertura
            self.extract_coverage_info()

            # Generar recomendaciones
            self.generate_recommendations()

            # Resumen final
            self.test_results["summary"] = {
                "unit_tests": unit_success,
                "integration_tests": integration_success,
                "e2e_tests": e2e_success,
                "security_tests": security_success,
                "load_tests": load_success,
                "all_critical_passed": unit_success
                and integration_success
                and security_success,
            }

            return self.test_results["summary"]["all_critical_passed"]

        finally:
            self.cleanup()


def main():
    """Función principal."""
    print("🚀 Iniciando suite completa de tests de producción")

    runner = TestRunner()

    try:
        success = runner.run_all_tests()

        # Mostrar resumen
        runner.print_summary()

        # Guardar resultados
        runner.save_results()

        # Exit code para CI/CD
        exit_code = 0 if success else 1

        if success:
            print("\n🎉 ¡Todos los tests críticos pasaron! Listo para despliegue.")
        else:
            print("\n💥 Algunos tests críticos fallaron. Revisar antes de desplegar.")

        sys.exit(exit_code)

    except KeyboardInterrupt:
        print("\n⚠️ Tests interrumpidos por el usuario")
        runner.cleanup()
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Error ejecutando tests: {e}")
        runner.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    main()
