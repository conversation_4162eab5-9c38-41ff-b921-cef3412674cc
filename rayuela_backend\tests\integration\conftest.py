import pytest
import asyncio
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from src.db.base import Base
from src.db.session import get_db
from src.core.config import settings
from src.db.models import (
    Account,
    SystemUser,
    Role,
    Permission,
    SystemUserRole,
    role_permissions,
    Product,
    EndUser,
    Interaction
)
from src.core.security import create_access_token, generate_api_key
from main import app

# Configuración de la base de datos de prueba
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

@pytest.fixture(scope="session")
def event_loop():
    """Crear un event loop para las pruebas asíncronas."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_engine():
    """Crear el motor de base de datos de prueba."""
    engine = create_async_engine(TEST_DATABASE_URL, echo=True)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield engine
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    await engine.dispose()

@pytest.fixture
async def db_session(test_engine):
    """Crear una sesión de base de datos de prueba."""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    async with async_session() as session:
        yield session
        await session.rollback()

@pytest.fixture
async def test_accounts(db_session):
    """Crear cuentas de prueba."""
    accounts = []
    for i in range(2):
        account = Account(
            name=f"Test Account {i}",
            email=f"account{i}@example.com",
            is_active=True
        )
        db_session.add(account)
        accounts.append(account)
    await db_session.commit()
    return accounts

@pytest.fixture
async def test_users(db_session, test_accounts):
    """Crear usuarios de prueba para cada cuenta."""
    users = {}
    for account in test_accounts:
        # Crear usuario admin
        admin = SystemUser(
            email=f"admin@{account.email}",
            name=f"Admin {account.name}",
            hashed_password="hashed_password",
            is_active=True,
            account_id=account.id
        )
        db_session.add(admin)

        # Crear usuario regular
        regular = SystemUser(
            email=f"user@{account.email}",
            name=f"User {account.name}",
            hashed_password="hashed_password",
            is_active=True,
            account_id=account.id
        )
        db_session.add(regular)

        users[account.id] = {"admin": admin, "regular": regular}

    await db_session.commit()
    return users

@pytest.fixture
async def test_roles(db_session):
    """Crear roles de prueba."""
    roles = {
        "admin": Role(name="admin", description="Administrador"),
        "user": Role(name="user", description="Usuario regular")
    }
    for role in roles.values():
        db_session.add(role)
    await db_session.commit()
    return roles

@pytest.fixture
async def test_permissions(db_session):
    """Crear permisos de prueba."""
    permissions = {
        "manage_users": Permission(name="manage_users", description="Gestionar usuarios"),
        "view_products": Permission(name="view_products", description="Ver productos"),
        "manage_products": Permission(name="manage_products", description="Gestionar productos")
    }
    for permission in permissions.values():
        db_session.add(permission)
    await db_session.commit()
    return permissions

@pytest.fixture
async def test_user_roles(db_session, test_users, test_roles):
    """Asignar roles a usuarios de prueba."""
    for account_users in test_users.values():
        # Asignar rol admin al usuario admin
        admin_role = SystemUserRole(
            user_id=account_users["admin"].id,
            role_id=test_roles["admin"].id
        )
        db_session.add(admin_role)

        # Asignar rol user al usuario regular
        user_role = SystemUserRole(
            user_id=account_users["regular"].id,
            role_id=test_roles["user"].id
        )
        db_session.add(user_role)

    await db_session.commit()

@pytest.fixture
async def test_role_permissions(db_session, test_roles, test_permissions):
    """Asignar permisos a roles de prueba."""
    # Asignar todos los permisos al rol admin
    for permission in test_permissions.values():
        # Usar la tabla de asociación role_permissions directamente
        stmt = role_permissions.insert().values(
            role_id=test_roles["admin"].id,
            permission_id=permission.id
        )
        await db_session.execute(stmt)

    # Asignar solo permisos de lectura al rol user
    stmt = role_permissions.insert().values(
        role_id=test_roles["user"].id,
        permission_id=test_permissions["view_products"].id
    )
    await db_session.execute(stmt)

    await db_session.commit()

@pytest.fixture
async def test_products(db_session, test_accounts):
    """Crear productos de prueba para cada cuenta."""
    products = {}
    for account in test_accounts:
        account_products = []
        for i in range(5):
            product = Product(
                name=f"Product {i} for {account.name}",
                description=f"Description {i}",
                price=10.0 * (i + 1),
                account_id=account.id
            )
            db_session.add(product)
            account_products.append(product)
        products[account.id] = account_products

    await db_session.commit()
    return products

@pytest.fixture
async def test_end_users(db_session, test_accounts):
    """Crear usuarios finales de prueba para cada cuenta."""
    end_users = {}
    for account in test_accounts:
        account_end_users = []
        for i in range(3):
            end_user = EndUser(
                email=f"enduser{i}@{account.email}",
                name=f"End User {i}",
                account_id=account.id
            )
            db_session.add(end_user)
            account_end_users.append(end_user)
        end_users[account.id] = account_end_users

    await db_session.commit()
    return end_users

@pytest.fixture
async def test_interactions(db_session, test_accounts, test_end_users, test_products):
    """Crear interacciones de prueba para cada cuenta."""
    for account in test_accounts:
        for end_user in test_end_users[account.id]:
            for product in test_products[account.id]:
                interaction = Interaction(
                    end_user_id=end_user.id,
                    product_id=product.id,
                    interaction_type="view",
                    timestamp=datetime.utcnow(),
                    account_id=account.id
                )
                db_session.add(interaction)

    await db_session.commit()

@pytest.fixture
async def test_tokens(db_session, test_users, test_accounts):
    """Crear tokens de acceso para usuarios de prueba."""
    from src.core.security.api_key import generate_api_key, hash_api_key

    tokens = {}
    for account_id, users in test_users.items():
        account_tokens = {}
        for role, user in users.items():
            # Crear token JWT
            access_token = create_access_token(
                data={"sub": user.email, "account_id": account_id}
            )

            # Generar API key y crear en la tabla api_keys
            api_key, api_key_hash = generate_api_key()

            # Crear API key en la tabla api_keys
            from src.db.models.api_key import ApiKey
            api_key_model = ApiKey(
                account_id=account_id,
                name=f"Test API Key for {role}",
                api_key_hash=api_key_hash,
                api_key_prefix=api_key.split("_")[0] if "_" in api_key else api_key[:5],
                api_key_last_chars=api_key[-6:],
                is_active=True
            )
            db_session.add(api_key_model)

            account_tokens[role] = {
                "access_token": access_token,
                "api_key": api_key
            }
        tokens[account_id] = account_tokens

    await db_session.commit()
    return tokens

@pytest.fixture
async def test_client(db_session):
    """Crear un cliente de prueba."""
    from fastapi.testclient import TestClient

    async def override_get_db():
        yield db_session

    app.dependency_overrides[get_db] = override_get_db
    return TestClient(app)

@pytest.fixture
def mock_celery_tasks():
    """Mock fixture for Celery tasks."""
    return {}

@pytest.fixture
async def execute_mock_tasks(mock_celery_tasks, db_session):
    """Mock fixture for executing Celery tasks."""
    async def _execute():
        # Simulate task execution
        pass
    return _execute

@pytest.fixture
def mock_gcs():
    """Mock fixture for Google Cloud Storage."""
    from unittest.mock import MagicMock
    mock = MagicMock()
    mock.bucket.return_value = MagicMock()
    return mock

class MockCeleryTaskRegistry:
    """Mock Celery task registry."""
    def __init__(self):
        self.tasks = {}
    
    def register(self, name, task):
        self.tasks[name] = task
    
    def get(self, name):
        return self.tasks.get(name)

@pytest.fixture
def mock_celery_task_registry():
    """Mock fixture for Celery task registry."""
    return MockCeleryTaskRegistry()