#!/usr/bin/env python3
"""
Script para reparar automáticamente problemas en tests de Rayuela.
Corrige imports, patrones obsoletos y actualiza dependencias.
"""

import os
import re
import json
import shutil
from pathlib import Path
from typing import Dict, List, Set
import subprocess


class TestFixer:
    """Clase principal para reparar tests."""

    def __init__(self, report_file: str = "test_health_report.json"):
        """Inicializa el fixer con el reporte de diagnóstico."""
        try:
            with open(report_file, "r", encoding="utf-8") as f:
                self.report = json.load(f)
        except FileNotFoundError:
            print(f"❌ Archivo de reporte no encontrado: {report_file}")
            print("🔧 Ejecuta primero: python scripts/test_health_checker.py")
            exit(1)

        self.fixes_applied = []
        self.errors_found = []

    def backup_file(self, file_path: str) -> str:
        """Crea un backup de un archivo antes de modificarlo."""
        backup_path = f"{file_path}.backup"
        shutil.copy2(file_path, backup_path)
        return backup_path

    def fix_import_issues(self, file_path: str) -> bool:
        """Corrige problemas de imports en un archivo."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            original_content = content

            # Mapeo de imports incorrectos a correctos
            import_fixes = {
                "from main import": "from src.main import",
                "import main": "import src.main as main",
                "from tests.integration.utils.test_db": "from tests.conftest",
                "from tests.integration.utils.mock_celery": "from tests.integration.conftest",
                "from tests.integration.utils.mock_external_services": "from tests.integration.conftest",
            }

            for old_import, new_import in import_fixes.items():
                if old_import in content:
                    content = content.replace(old_import, new_import)
                    self.fixes_applied.append(
                        f"📝 {file_path}: Fixed import '{old_import}' -> '{new_import}'"
                    )

            # Agregar imports faltantes comunes
            if "import pytest" not in content and "test_" in file_path:
                # Agregar import de pytest al inicio
                lines = content.split("\n")
                insert_index = 0
                for i, line in enumerate(lines):
                    if line.startswith('"""') or line.startswith("'''"):
                        # Encontrar el final del docstring
                        for j in range(i + 1, len(lines)):
                            if lines[j].endswith('"""') or lines[j].endswith("'''"):
                                insert_index = j + 1
                                break
                        break
                    elif line.strip() and not line.startswith("#"):
                        insert_index = i
                        break

                lines.insert(insert_index, "import pytest")
                content = "\n".join(lines)
                self.fixes_applied.append(
                    f"📝 {file_path}: Added missing 'import pytest'"
                )

            # Escribir solo si hay cambios
            if content != original_content:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                return True

            return False

        except Exception as e:
            error_msg = f"❌ Error fixing imports in {file_path}: {e}"
            self.errors_found.append(error_msg)
            print(error_msg)
            return False

    def fix_deprecated_patterns(self, file_path: str) -> bool:
        """Corrige patrones obsoletos en un archivo."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            original_content = content

            # Patrones de reemplazo
            replacements = {
                # Fixtures obsoletos
                r"@pytest\.yield_fixture": "@pytest.fixture",
                r'@pytest\.fixture\(scope="function"\)': "@pytest.fixture",
                r"yield\s+(\w+)": r"return \1",
                # Asyncio obsoleto
                r"asyncio\.get_event_loop\(\)\.run_until_complete\(([^)]+)\)": r"asyncio.run(\1)",
                r"loop\.run_until_complete\(([^)]+)\)": r"asyncio.run(\1)",
                # TestClient patterns
                r"from fastapi\.testclient import TestClient": "from httpx import AsyncClient",
                r"TestClient\(app\)": 'AsyncClient(app=app, base_url="http://test")',
                # Patrones de assert obsoletos
                r"assert\s+(\w+)\s+==\s+True": r"assert \1",
                r"assert\s+(\w+)\s+==\s+False": r"assert not \1",
                r"assert\s+(\w+)\s+is\s+True": r"assert \1",
                r"assert\s+(\w+)\s+is\s+False": r"assert not \1",
            }

            for pattern, replacement in replacements.items():
                old_content = content
                content = re.sub(pattern, replacement, content)
                if content != old_content:
                    self.fixes_applied.append(
                        f"📝 {file_path}: Fixed pattern '{pattern}'"
                    )

            # Escribir solo si hay cambios
            if content != original_content:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                return True

            return False

        except Exception as e:
            error_msg = f"❌ Error fixing deprecated patterns in {file_path}: {e}"
            self.errors_found.append(error_msg)
            print(error_msg)
            return False

    def add_missing_async_markers(self, file_path: str) -> bool:
        """Agrega markers @pytest.mark.asyncio a funciones async."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            original_content = content
            lines = content.split("\n")

            for i, line in enumerate(lines):
                # Detectar funciones async sin marker
                if (
                    line.strip().startswith("async def test_")
                    and i > 0
                    and "@pytest.mark.asyncio" not in lines[i - 1]
                ):

                    # Insertar el marker
                    lines.insert(i, "@pytest.mark.asyncio")
                    self.fixes_applied.append(
                        f"📝 {file_path}: Added @pytest.mark.asyncio marker"
                    )
                    break

            content = "\n".join(lines)

            # Escribir solo si hay cambios
            if content != original_content:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                return True

            return False

        except Exception as e:
            error_msg = f"❌ Error adding async markers in {file_path}: {e}"
            self.errors_found.append(error_msg)
            print(error_msg)
            return False

    def fix_test_structure(self, file_path: str) -> bool:
        """Corrige la estructura de tests para que sean compatibles con pytest."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            original_content = content

            # Convertir clases unittest a funciones pytest
            if "unittest.TestCase" in content:
                # Este es un cambio más complejo que requiere análisis manual
                self.fixes_applied.append(
                    f"📝 {file_path}: Marked for manual conversion from unittest to pytest"
                )
                return False

            # Asegurar que las funciones de test siguen la convención
            lines = content.split("\n")
            for i, line in enumerate(lines):
                # Renombrar funciones que no siguen la convención test_
                if re.match(r"\s*def\s+test\w+\(", line) and not re.match(
                    r"\s*def\s+test_\w+\(", line
                ):
                    old_name = re.search(r"def\s+(\w+)\(", line).group(1)
                    new_name = (
                        f"test_{old_name[4:]}"
                        if old_name.startswith("test")
                        else f"test_{old_name}"
                    )
                    lines[i] = line.replace(f"def {old_name}(", f"def {new_name}(")
                    self.fixes_applied.append(
                        f"📝 {file_path}: Renamed function '{old_name}' to '{new_name}'"
                    )

            content = "\n".join(lines)

            # Escribir solo si hay cambios
            if content != original_content:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                return True

            return False

        except Exception as e:
            error_msg = f"❌ Error fixing test structure in {file_path}: {e}"
            self.errors_found.append(error_msg)
            print(error_msg)
            return False

    def create_missing_utils(self):
        """Crea archivos de utilidades faltantes."""
        utils_dir = Path("tests/integration/utils")
        utils_dir.mkdir(parents=True, exist_ok=True)

        # Crear __init__.py si no existe
        init_file = utils_dir / "__init__.py"
        if not init_file.exists():
            init_file.write_text("# Integration test utilities\n")
            self.fixes_applied.append("📝 Created tests/integration/utils/__init__.py")

        # Crear mock_celery.py
        mock_celery_file = utils_dir / "mock_celery.py"
        if not mock_celery_file.exists():
            mock_celery_content = '''"""Mock utilities for Celery in tests."""
import pytest
from unittest.mock import MagicMock, patch

@pytest.fixture
def mock_celery():
    """Mock Celery for tests."""
    with patch('src.workers.celery_app.celery_app') as mock:
        mock.send_task = MagicMock()
        mock.delay = MagicMock()
        yield mock

@pytest.fixture
def mock_celery_task():
    """Mock individual Celery task."""
    with patch('celery.Task.delay') as mock:
        yield mock
'''
            mock_celery_file.write_text(mock_celery_content)
            self.fixes_applied.append(
                "📝 Created tests/integration/utils/mock_celery.py"
            )

        # Crear mock_external_services.py
        mock_services_file = utils_dir / "mock_external_services.py"
        if not mock_services_file.exists():
            mock_services_content = '''"""Mock utilities for external services in tests."""
import pytest
from unittest.mock import MagicMock, patch

@pytest.fixture
def mock_gcs():
    """Mock Google Cloud Storage."""
    with patch('google.cloud.storage.Client') as mock:
        yield mock

@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    with patch('redis.Redis') as mock:
        yield mock

@pytest.fixture
def mock_mercadopago():
    """Mock MercadoPago SDK."""
    with patch('mercadopago.SDK') as mock:
        yield mock
'''
            mock_services_file.write_text(mock_services_content)
            self.fixes_applied.append(
                "📝 Created tests/integration/utils/mock_external_services.py"
            )

    def update_conftest_files(self):
        """Actualiza archivos conftest.py para incluir fixtures faltantes."""
        main_conftest = Path("tests/conftest.py")

        if main_conftest.exists():
            with open(main_conftest, "r", encoding="utf-8") as f:
                content = f.read()

            # Agregar imports para utilidades si no existen
            if "from tests.integration.utils" not in content:
                additional_imports = """
# Import utilities
try:
    from tests.integration.utils.mock_celery import mock_celery, mock_celery_task
    from tests.integration.utils.mock_external_services import mock_gcs, mock_redis, mock_mercadopago
except ImportError:
    pass  # Utilities might not be available in all test contexts
"""
                content += additional_imports

                with open(main_conftest, "w", encoding="utf-8") as f:
                    f.write(content)

                self.fixes_applied.append(
                    "📝 Updated tests/conftest.py with utility imports"
                )

    def run_fixes(self):
        """Ejecuta todas las reparaciones necesarias."""
        print("🔧 Iniciando reparación automática de tests...")

        # Crear utilidades faltantes
        print("📦 Creando archivos de utilidades faltantes...")
        self.create_missing_utils()
        self.update_conftest_files()

        # Procesar cada archivo con problemas
        problem_files = set()

        # Agregar archivos con problemas de import
        for issue in self.report.get("issues", {}).get("import_failures", []):
            problem_files.add(issue["file"])

        # Agregar archivos con patrones obsoletos
        for file_path in (
            self.report.get("issues", {}).get("deprecated_patterns", {}).keys()
        ):
            problem_files.add(file_path)

        print(f"🔍 Procesando {len(problem_files)} archivos con problemas...")

        for file_path in problem_files:
            print(f"🔧 Reparando: {file_path}")

            # Crear backup
            backup_path = self.backup_file(file_path)

            try:
                # Aplicar reparaciones
                self.fix_import_issues(file_path)
                self.fix_deprecated_patterns(file_path)
                self.add_missing_async_markers(file_path)
                self.fix_test_structure(file_path)

            except Exception as e:
                # Restaurar backup en caso de error
                print(f"❌ Error procesando {file_path}: {e}")
                shutil.copy2(backup_path, file_path)
                self.errors_found.append(f"Failed to process {file_path}: {e}")

        print("\n" + "=" * 60)
        print("📊 RESUMEN DE REPARACIONES")
        print("=" * 60)
        print(f"✅ Reparaciones aplicadas: {len(self.fixes_applied)}")
        print(f"❌ Errores encontrados: {len(self.errors_found)}")

        if self.fixes_applied:
            print("\n🔧 REPARACIONES APLICADAS:")
            for fix in self.fixes_applied:
                print(f"  {fix}")

        if self.errors_found:
            print("\n❌ ERRORES ENCONTRADOS:")
            for error in self.errors_found:
                print(f"  {error}")

        print(f"\n💡 SIGUIENTE PASO:")
        print(f"   Ejecuta: docker-compose -f docker-compose.test.yml up --build")


def main():
    """Función principal."""
    fixer = TestFixer()
    fixer.run_fixes()


if __name__ == "__main__":
    main()
