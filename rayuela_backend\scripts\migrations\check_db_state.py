#!/usr/bin/env python3
"""
Script para verificar el estado actual de la base de datos
"""

import asyncio
import sys
import os
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

# Agregar el directorio src al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from src.core.config import settings


async def check_db_state():
    """Verifica qué elementos ya existen en la base de datos"""
    engine = create_async_engine(settings.database_url)

    async with engine.begin() as conn:
        print("🔍 Verificando estado de la base de datos...\n")

        # Verificar si el enum orderstatus existe
        result = await conn.execute(
            text(
                """
            SELECT EXISTS (
                SELECT 1 FROM pg_type 
                WHERE typname = 'orderstatus'
            );
        """
            )
        )
        enum_exists = result.scalar()
        print(f"Enum 'orderstatus': {'✅ Existe' if enum_exists else '❌ No existe'}")

        # Verificar si la tabla orders existe
        result = await conn.execute(
            text(
                """
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_name = 'orders'
            );
        """
            )
        )
        orders_exists = result.scalar()
        print(f"Tabla 'orders': {'✅ Existe' if orders_exists else '❌ No existe'}")

        # Verificar si la tabla order_items existe
        result = await conn.execute(
            text(
                """
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_name = 'order_items'
            );
        """
            )
        )
        order_items_exists = result.scalar()
        print(
            f"Tabla 'order_items': {'✅ Existe' if order_items_exists else '❌ No existe'}"
        )

        # Verificar estructura de la tabla end_users
        print("\n🔍 Verificando estructura de end_users:")
        result = await conn.execute(
            text(
                """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'end_users'
            ORDER BY ordinal_position;
        """
            )
        )
        end_users_columns = result.fetchall()
        if end_users_columns:
            for col in end_users_columns:
                print(
                    f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})"
                )
        else:
            print("  ❌ Tabla end_users no existe")

        # Verificar estructura de la tabla products
        print("\n🔍 Verificando estructura de products:")
        result = await conn.execute(
            text(
                """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'products'
            ORDER BY ordinal_position;
        """
            )
        )
        products_columns = result.fetchall()
        if products_columns:
            for col in products_columns:
                print(
                    f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})"
                )
        else:
            print("  ❌ Tabla products no existe")

        # Verificar estructura de la tabla interactions
        print("\n🔍 Verificando estructura de interactions:")
        result = await conn.execute(
            text(
                """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'interactions'
            ORDER BY ordinal_position;
        """
            )
        )
        interactions_columns = result.fetchall()
        if interactions_columns:
            for col in interactions_columns:
                print(
                    f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})"
                )
        else:
            print("  ❌ Tabla interactions no existe")

        # Verificar el estado de alembic
        try:
            result = await conn.execute(
                text(
                    """
                SELECT version_num FROM alembic_version;
            """
                )
            )
            current_version = result.scalar()
            print(f"\nVersión actual de Alembic: {current_version}")
        except Exception as e:
            print(f"\nError obteniendo versión de Alembic: {e}")

        print("\n📋 Resumen:")
        if enum_exists and not orders_exists and not order_items_exists:
            print(
                "⚠️  Solo el enum existe. La migración falló después de crear el enum."
            )
            print("💡 Solución: Continuar con la migración corregida.")
        elif enum_exists and orders_exists and order_items_exists:
            print("✅ Todas las estructuras existen. La migración se completó.")
        elif not enum_exists and not orders_exists and not order_items_exists:
            print("🆕 Ninguna estructura existe. Migración limpia.")
        else:
            print("🤔 Estado parcial. Revisar manualmente.")

    await engine.dispose()


if __name__ == "__main__":
    asyncio.run(check_db_state())
