#!/bin/bash

# Script para ejecutar tests críticos de multi-tenancy y atomicidad de transacciones
# Este script es usado tanto localmente como en CI/CD

set -e  # Exit on any error

echo "🔒 EJECUTANDO TESTS CRÍTICOS DE MULTI-TENANCY Y ATOMICIDAD"
echo "=========================================================="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar que estamos en el directorio correcto
if [ ! -f "alembic.ini" ]; then
    log_error "Este script debe ejecutarse desde el directorio rayuela_backend"
    exit 1
fi

# Variables de entorno para tests
export ENV=test
export POSTGRES_USER=${POSTGRES_USER:-postgres}
export POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
export POSTGRES_SERVER=${POSTGRES_SERVER:-localhost}
export POSTGRES_PORT=${POSTGRES_PORT:-5432}
export POSTGRES_DB=${POSTGRES_DB:-rayuela_test}
export REDIS_HOST=${REDIS_HOST:-localhost}
export REDIS_PORT=${REDIS_PORT:-6379}
export SECRET_KEY=${SECRET_KEY:-test_secret_key_for_ci_cd_pipeline_at_least_32_chars}

log_info "Configuración de test:"
log_info "  - Base de datos: ${POSTGRES_SERVER}:${POSTGRES_PORT}/${POSTGRES_DB}"
log_info "  - Redis: ${REDIS_HOST}:${REDIS_PORT}"
log_info "  - Entorno: ${ENV}"

# Lista de tests críticos que DEBEN pasar
CRITICAL_TESTS=(
    "tests/integration/test_multi_tenancy_comprehensive.py"
    "tests/middleware/test_tenant_middleware_comprehensive.py"
    "tests/unit/db/repositories/test_base_repository_tenant.py"
    "tests/integration/test_celery_tenant_isolation_extended.py"
    "tests/integration/test_transaction_atomicity.py"
)

# Verificar que todos los archivos de test existen
log_info "Verificando archivos de test..."
for test_file in "${CRITICAL_TESTS[@]}"; do
    if [ ! -f "$test_file" ]; then
        log_error "Archivo de test no encontrado: $test_file"
        exit 1
    fi
    log_info "  ✓ $test_file"
done

# Función para ejecutar tests con retry
run_tests_with_retry() {
    local max_attempts=3
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "Intento $attempt de $max_attempts..."
        
        if python -m pytest "${CRITICAL_TESTS[@]}" \
            -v \
            --tb=short \
            --junitxml=test_results/critical_tests_results.xml \
            --cov=src \
            --cov-report=xml:test_results/coverage_critical.xml \
            --cov-report=html:test_results/htmlcov_critical \
            --cov-fail-under=80 \
            --maxfail=1; then
            
            log_success "Tests críticos completados exitosamente en intento $attempt"
            return 0
        else
            log_warning "Tests fallaron en intento $attempt"
            if [ $attempt -eq $max_attempts ]; then
                log_error "Tests críticos fallaron después de $max_attempts intentos"
                return 1
            fi
            attempt=$((attempt + 1))
            sleep 5
        fi
    done
}

# Crear directorio para resultados
mkdir -p test_results

# Ejecutar verificación RLS
log_info "Ejecutando verificación de políticas RLS..."
if python scripts/verify_rls_comprehensive.py; then
    log_success "Verificación RLS completada exitosamente"
else
    log_error "Verificación RLS falló"
    exit 1
fi

# Ejecutar tests críticos
log_info "Ejecutando tests críticos de multi-tenancy y atomicidad..."
if run_tests_with_retry; then
    log_success "Todos los tests críticos pasaron"
else
    log_error "Tests críticos fallaron"
    exit 1
fi

# Generar reporte de cobertura
if [ -f "test_results/coverage_critical.xml" ]; then
    log_info "Reporte de cobertura generado en test_results/coverage_critical.xml"
    log_info "Reporte HTML de cobertura disponible en test_results/htmlcov_critical/index.html"
fi

# Resumen final
echo ""
echo "🎉 RESUMEN DE TESTS CRÍTICOS"
echo "============================"
log_success "✅ Verificación RLS: PASÓ"
log_success "✅ Tests de multi-tenancy: PASARON"
log_success "✅ Tests de atomicidad de transacciones: PASARON"
log_success "✅ Tests de middleware de tenant: PASARON"
log_success "✅ Tests de aislamiento de repositorios: PASARON"
log_success "✅ Tests de aislamiento Celery: PASARON"

echo ""
log_info "🔒 GARANTÍAS DE SEGURIDAD VALIDADAS:"
log_info "  • Aislamiento completo entre tenants"
log_info "  • Atomicidad de transacciones complejas"
log_info "  • Políticas RLS funcionando correctamente"
log_info "  • Middleware de tenant propagando contexto"
log_info "  • Repositorios respetando filtros de tenant"
log_info "  • Tareas Celery aisladas por tenant"

echo ""
log_success "🚀 SISTEMA LISTO PARA DEPLOYMENT"
